#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量替换print语句为logger的脚本
"""

import re
import os

def fix_print_statements(file_path):
    """将文件中的print语句替换为logger"""
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 记录原始内容
    original_content = content
    
    # 替换模式
    replacements = [
        # 简单的print语句
        (r'print\(f"([^"]*)"', r'logger.info(f"\1"'),
        (r"print\(f'([^']*)'\)", r"logger.info(f'\1')"),
        (r'print\("([^"]*)"', r'logger.info("\1"'),
        (r"print\('([^']*)'\)", r"logger.info('\1')"),
        
        # 带变量的print语句
        (r'print\(f"([^"]*){([^}]*)}"', r'logger.info(f"\1{\2}"'),
        (r"print\(f'([^']*){([^}]*)}'\)", r"logger.info(f'\1{\2}')"),
        
        # 特殊情况 - 警告信息
        (r'logger\.info\("⚠️', r'logger.warning("'),
        (r"logger\.info\('⚠️", r"logger.warning('"),
        (r'logger\.info\(f"⚠️', r'logger.warning(f"'),
        (r"logger\.info\(f'⚠️", r"logger.warning(f'"),
        
        # 特殊情况 - 错误信息
        (r'logger\.info\("❌', r'logger.error("'),
        (r"logger\.info\('❌", r"logger.error('"),
        (r'logger\.info\(f"❌', r'logger.error(f"'),
        (r"logger\.info\(f'❌", r"logger.error(f'"),
    ]
    
    # 应用替换
    for pattern, replacement in replacements:
        content = re.sub(pattern, replacement, content)
    
    # 检查是否有变化
    if content != original_content:
        # 备份原文件
        backup_path = file_path + '.backup'
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(original_content)
        
        # 写入修改后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Fixed {file_path}")
        print(f"   Backup saved to {backup_path}")
        return True
    else:
        print(f"ℹ️  No changes needed for {file_path}")
        return False

def main():
    """主函数"""
    print("🔧 Fixing print statements to use logger...")
    
    # 要修复的文件
    files_to_fix = [
        "tools/training/train.py"
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_print_statements(file_path):
                fixed_count += 1
        else:
            print(f"❌ File not found: {file_path}")
    
    print(f"\n📊 Summary: Fixed {fixed_count} files")

if __name__ == "__main__":
    main()
