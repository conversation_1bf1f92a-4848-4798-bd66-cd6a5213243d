{"timestamp": "2025-05-27T14:05:25.205431", "cleaned_files": [], "compressed_files": [{"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_105501.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_105501.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_105521.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_105521.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_105529.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_105529.log.gz", "original_size": 381, "compressed_size": 202, "space_saved": 179}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_105530.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_105530.log.gz", "original_size": 381, "compressed_size": 199, "space_saved": 182}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110422.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110422.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110429.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110429.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110445.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110445.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110452.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110452.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110455.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110455.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110457.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110457.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110459.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110459.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110501.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110501.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110504.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110504.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110831.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110831.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110839.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110839.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110841.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110841.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110844.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110844.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110846.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110846.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110848.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110848.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110850.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_110850.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_111854.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_111854.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112142.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112142.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112150.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112150.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112153.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112153.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112155.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112155.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112158.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112158.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112351.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112351.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112359.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112359.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112401.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112401.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112534.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112534.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112542.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_112542.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_121928.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_121928.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_121942.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_121942.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_121949.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_121949.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_130131.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_130131.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_130138.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_130138.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_132324.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_132324.log.gz", "original_size": 127, "compressed_size": 185, "space_saved": -58}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_132331.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_132331.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_133017.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_133017.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_133024.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_133024.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_133647.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_133647.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_133654.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_133654.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_134742.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_134742.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_134749.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_134749.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_140349.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_140349.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}, {"original_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_140356.log", "compressed_file": "E:\\Proj\\pytorch-model-train\\logs\\deep_learning_framework_20250526_140356.log.gz", "original_size": 127, "compressed_size": 187, "space_saved": -60}], "total_space_freed": -2277, "errors": [], "archived_files": [], "created_log_directories": ["logs/training", "logs/testing", "logs/scheduler", "logs/analysis", "logs/system"], "cleaned_directories": 60}