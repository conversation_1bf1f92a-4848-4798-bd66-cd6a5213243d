'''
数据预处理
处理目的结果：
将打标图和数据绑定，然后重命名文件
base_dir = E:\Proj\pytorch-model-train\dataset\SeedVisionDataset\YOLO_Data
base_dir下的no_val.xlsx文件是每种油菜籽对应的数值，将这些数值赋予每种油菜籽下的每个图片
第一列是编号，第二列是编号对应的油菜籽种类，第三列跳过，第四列是含油量oil，第五列是蛋白质含量protein
base_dir下的文件夹名对应编号，处理时直接将no_val.xlsx文件中的编号和文件夹名对应，然后将对应编号的油菜籽种类和含油量oil和蛋白质含量protein赋予对应的图片
'''
import os
import pandas as pd
import shutil
IMAGE_TOTAL_NUM = 2800 # 处理的图片总量，然后尽量平均分给每一类

# 基础路径
BASE_DIR = r'E:\Proj\pytorch-model-train\dataset\SeedVisionDataset'
# 原始数据路径
ORIGINAL_DATA_DIR = BASE_DIR + r'\Origin_Data'
# 原始数据标注
NO_VAR = ORIGINAL_DATA_DIR + r'\No_Var.xlsx' # 每个种类的基础标注
# 基础输出路径
BASE_OUTPUT_DIR = BASE_DIR + r'\YOLO_Data'
# 本次处理的输出路径
CURRENT_OUTPUT_DIR = BASE_OUTPUT_DIR + r'\yolotrain1'
# 本次处理的数据csv路径
CURRENT_OUTPUT_CSV = CURRENT_OUTPUT_DIR + r'\data.csv'
# 数据集划分的路径
# 这里严格来说应该再有变量表示划分路径的，但是这里用不到就这样写了
TRAIN_IMAGE_DIR = CURRENT_OUTPUT_DIR + r'\train\images'
VAL_IMAGE_DIR = CURRENT_OUTPUT_DIR + r'\val\images'
TEST_IMAGE_DIR = CURRENT_OUTPUT_DIR + r'\test\images'

# 如果不存在就创建
if not os.path.exists(CURRENT_OUTPUT_DIR):
    os.makedirs(CURRENT_OUTPUT_DIR)
if not os.path.exists(TRAIN_IMAGE_DIR):
    os.makedirs(TRAIN_IMAGE_DIR)
if not os.path.exists(VAL_IMAGE_DIR):
    os.makedirs(VAL_IMAGE_DIR)
if not os.path.exists(TEST_IMAGE_DIR):
    os.makedirs(TEST_IMAGE_DIR)

TRAIN_RATE = 0.8 # 训练集比例
VAL_RATE = 0.1 # 验证集比例
TEST_RATE = 0.1 # 测试集比例

def process():
    # 初步处理，先把图和数值绑定，然后重命名文件并进行打标
    # 读取no_val.xlsx文件，使用原文件表头
    df = pd.read_excel(NO_VAR)
    # 多少个种类
    class_num = len(df)
    # 每个种类的图片数量
    image_num = IMAGE_TOTAL_NUM // class_num
    # 数据集列表
    dataset_list = []
    # 已处理图片数量
    processed_image_num = 0
    # 遍历每个种类
    for index, row in df.iterrows():
        # 获取编号和种类
        no = row['编号']
        class_name = row['种类']
        protein = row['蛋白量']
        oil = row['含油量']
        # 获取对应编号的文件夹名
        folder_name = str(no)
        # 获取原始数据文件夹下的所有图片
        image_path = os.path.join(ORIGINAL_DATA_DIR, folder_name)
        image_list = os.listdir(image_path)
        # 从每个文件夹下image_num张图片
        image_list = image_list[:image_num] # 记录每个种类的图片列表
        
        # 重命名图片
        for i, image in enumerate(image_list):
            # 获取图片的后缀
            suffix = image.split('.')[-1]
            # 重命名图片
            new_name = f'{no}_{i}.{suffix}'
            # 新路径
            new_path = os.path.join(CURRENT_OUTPUT_DIR, new_name)
            # 加入数据集列表
            dataset_list.append([new_path ,new_name, class_name, oil, protein])
            # 按比例将图片复制到对应文件夹下并重命名，保留原文件
            # 训练集，验证集，测试集
            if processed_image_num < IMAGE_TOTAL_NUM* TRAIN_RATE:
                shutil.copy2(os.path.join(image_path, image), os.path.join(TRAIN_IMAGE_DIR, new_name))
            elif processed_image_num < IMAGE_TOTAL_NUM * (TRAIN_RATE + VAL_RATE):
                shutil.copy2(os.path.join(image_path, image), os.path.join(VAL_IMAGE_DIR, new_name))
            else:
                shutil.copy2(os.path.join(image_path, image), os.path.join(TEST_IMAGE_DIR, new_name))
            # 已处理图片数量加1
            processed_image_num += 1

    # 将数据写入csv文件，第一列是图片名，第二列是种类，第三列是蛋白质含量，第四列是含油量
    df = pd.DataFrame(dataset_list, columns=['path', 'type','image', 'cls', 'oil', 'protein'])
    # utf8编码
    df.to_csv(CURRENT_OUTPUT_CSV, index=False, encoding='utf-8')

def update_path():
    # 读取csv文件，更新path列
    df = pd.read_csv(CURRENT_OUTPUT_CSV)
    # 在前两列增加 path和type字段
    df.insert(0, 'path', '')  # 在第一列插入path
    df.insert(1, 'type', '')  # 在第二列插入type
    # 读取输出后的所有图片
    train_images = os.listdir(TRAIN_IMAGE_DIR)
    val_images = os.listdir(VAL_IMAGE_DIR)
    test_images = os.listdir(TEST_IMAGE_DIR)
    # 遍历每一行
    for index, row in df.iterrows():
        # 获取图片名
        image_name = row['image']
        # 比对图片名，更新path列
        if image_name in train_images:
            df.at[index, 'path'] = os.path.join(TRAIN_IMAGE_DIR, image_name)
            # 更新数据集归属
            df.at[index, 'type'] = 'train'
        elif image_name in val_images:
            df.at[index, 'path'] = os.path.join(VAL_IMAGE_DIR, image_name)
            # 更新数据集归属
            df.at[index, 'type'] = 'val'
        elif image_name in test_images:
            df.at[index, 'path'] = os.path.join(TEST_IMAGE_DIR, image_name)
            # 更新数据集归属
            df.at[index, 'type'] = 'test'

    # 写回
    df.to_csv(CURRENT_OUTPUT_CSV, index=False, encoding='utf-8')
        

if __name__ == '__main__':
    update_path()