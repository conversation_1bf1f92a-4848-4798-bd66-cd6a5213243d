#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量启用所有用户配置脚本

将所有user_开头的配置设置为enable: true
"""

import yaml
import os
import sys

def enable_all_user_configs():
    """启用所有用户配置"""
    config_path = "config/training_config.yaml"
    
    # 读取配置文件
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"[ERROR] 无法读取配置文件: {e}")
        return False
    
    # 统计信息
    user_configs_count = 0
    enabled_count = 0
    
    # 遍历所有训练配置
    if 'training_configs' in config:
        for config_name, config_data in config['training_configs'].items():
            if config_name.startswith('user_'):
                user_configs_count += 1
                if not config_data.get('enable', False):
                    config_data['enable'] = True
                    enabled_count += 1
                    print(f"[ENABLED] {config_name}")
                else:
                    print(f"[ALREADY ENABLED] {config_name}")
    
    # 保存配置文件
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        print(f"\n[SUCCESS] 配置文件已保存")
        print(f"[SUMMARY] 找到 {user_configs_count} 个用户配置，新启用 {enabled_count} 个")
        return True
    except Exception as e:
        print(f"[ERROR] 无法保存配置文件: {e}")
        return False

def disable_all_non_user_configs():
    """禁用所有非用户配置"""
    config_path = "config/training_config.yaml"
    
    # 读取配置文件
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"[ERROR] 无法读取配置文件: {e}")
        return False
    
    # 统计信息
    non_user_configs_count = 0
    disabled_count = 0
    
    # 遍历所有训练配置
    if 'training_configs' in config:
        for config_name, config_data in config['training_configs'].items():
            if not config_name.startswith('user_'):
                non_user_configs_count += 1
                if config_data.get('enable', False):
                    config_data['enable'] = False
                    disabled_count += 1
                    print(f"[DISABLED] {config_name}")
    
    # 保存配置文件
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        print(f"\n[SUCCESS] 配置文件已保存")
        print(f"[SUMMARY] 找到 {non_user_configs_count} 个非用户配置，禁用 {disabled_count} 个")
        return True
    except Exception as e:
        print(f"[ERROR] 无法保存配置文件: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("[CONFIG MANAGER] 批量配置管理工具")
    print("=" * 80)
    
    # 切换到项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(script_dir))
    os.chdir(project_root)
    print(f"[INFO] 工作目录: {os.getcwd()}")
    
    # 检查配置文件是否存在
    config_path = "config/training_config.yaml"
    if not os.path.exists(config_path):
        print(f"[ERROR] 配置文件不存在: {config_path}")
        return
    
    print(f"\n[STEP 1] 禁用所有非用户配置...")
    disable_all_non_user_configs()
    
    print(f"\n[STEP 2] 启用所有用户配置...")
    enable_all_user_configs()
    
    print(f"\n[COMPLETE] 配置更新完成！")
    print("现在所有32个用户配置都已启用，其他配置都已禁用。")

if __name__ == "__main__":
    main()
