#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试带有Original级别评估的训练功能

这个脚本测试在实际训练过程中Original级别评估的工作情况：
1. 使用小规模数据进行快速训练测试
2. 验证每个epoch都能正确显示样本级别和Original级别的R²
3. 确保训练过程中不会出现错误
"""

import sys
import os
import torch

# 添加路径
sys.path.append('E:\Proj\pytorch-model-train')

def test_training_with_original_eval():
    """测试带有Original级别评估的训练"""
    print("🚀 Testing Training with Original-level Evaluation")
    print("=" * 70)

    try:
        # 导入必要的模块
        from config.config_loader import ConfigLoader
        from tools.myscripts.train import train_model
        import torchvision.transforms as transforms

        print("✅ Successfully imported required modules")

        # 加载配置
        config_loader = ConfigLoader()
        enabled_configs = config_loader.get_enabled_training_configs()

        if not enabled_configs:
            print("❌ No enabled training configurations found")
            return False

        # 使用第一个启用的配置
        config = enabled_configs[0]
        print(f"✅ Using config: {config['name']}")

        # 获取采样策略配置
        sampling_strategy_config = config.get('sampling_strategy_config', {})
        if sampling_strategy_config:
            strategy_name = sampling_strategy_config.get('strategy_name', 'unknown')
            strategy_type = sampling_strategy_config.get('strategy_type', 'unknown')
            print(f"✅ Sampling strategy: {strategy_name} ({strategy_type})")

        # 设置训练参数（小规模测试）
        test_config = {
            'model_config': config['model'],
            'hyperparam_config': config_loader.get_hyperparameter_config(config['hyperparameter_config']),
            'transform': transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ]),
            'result_dir': 'output/test_original_eval',
            'device': torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
            'num_epochs': 2,  # 只训练2个epoch进行测试
            'batch_size': 20,  # 小批次大小
            'task_name': 'Original_Eval_Test',
            'sample_size': 200,  # 小样本量测试
            'sampling_strategy_config': sampling_strategy_config,
            'use_original_sampling': sampling_strategy_config.get('strategy_type') == 'original_based'
        }

        # 创建输出目录
        os.makedirs(test_config['result_dir'], exist_ok=True)

        print(f"🎯 Test Configuration:")
        print(f"  - Epochs: {test_config['num_epochs']}")
        print(f"  - Batch size: {test_config['batch_size']}")
        print(f"  - Sample size: {test_config['sample_size']}")
        print(f"  - Device: {test_config['device']}")
        print(f"  - Output dir: {test_config['result_dir']}")
        print()

        print("🏃 Starting training test...")
        print("-" * 50)

        # 开始训练
        history, model = train_model(**test_config)

        print("-" * 50)
        print("✅ Training test completed successfully!")

        # 验证历史记录
        print("\n📊 Verifying training history:")

        if 'train_losses' in history:
            print(f"  ✅ Train losses recorded: {len(history['train_losses'])} epochs")

        if 'val_losses' in history:
            print(f"  ✅ Validation losses recorded: {len(history['val_losses'])} epochs")

        if 'val_metrics' in history:
            val_metrics_list = history['val_metrics']
            print(f"  ✅ Validation metrics recorded: {len(val_metrics_list)} epochs")

            # 检查最后一个epoch的metrics格式
            if val_metrics_list:
                last_metrics = val_metrics_list[-1]
                if 'sample_level' in last_metrics:
                    print("  ✅ Sample-level metrics found")
                    sample_oil_r2 = last_metrics['sample_level']['oil']['R2']
                    sample_protein_r2 = last_metrics['sample_level']['protein']['R2']
                    print(f"    Sample-level R²: Oil={sample_oil_r2:.4f}, Protein={sample_protein_r2:.4f}")

                if 'original_level' in last_metrics:
                    print("  ✅ Original-level metrics found")
                    original_oil_r2 = last_metrics['original_level']['oil']['R2']
                    original_protein_r2 = last_metrics['original_level']['protein']['R2']
                    print(f"    Original-level R²: Oil={original_oil_r2:.4f}, Protein={original_protein_r2:.4f}")

                    # 检查original结果详情
                    if 'original_results' in last_metrics:
                        original_results = last_metrics['original_results']
                        print(f"    Original count: {len(original_results)}")

                        # 显示前3个original的详情
                        for i, (orig_name, result) in enumerate(list(original_results.items())[:3]):
                            sample_count = result['sample_count']
                            mean_pred = result['mean_prediction']
                            print(f"      {orig_name}: {sample_count} samples, mean_pred=[{mean_pred[0]:.3f}, {mean_pred[1]:.3f}]")
                else:
                    print("  ⚠️  Original-level metrics not found (may be expected if not enough originals)")

        # 检查输出文件
        print("\n📁 Checking output files:")
        output_files = [
            'best_model.pth',
            'final_model.pth',
            'loss_curve.png',
            'oil_r2_curve.png',
            'protein_r2_curve.png',
            'oil_regression.png',
            'protein_regression.png'
        ]

        for filename in output_files:
            filepath = os.path.join(test_config['result_dir'], filename)
            if os.path.exists(filepath):
                print(f"  ✅ {filename}")
            else:
                print(f"  ❌ {filename} (missing)")

        print("\n🎉 Training with Original-level evaluation test completed!")
        return True

    except Exception as e:
        print(f"❌ Training test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 SeedVision v1 - Original-level Evaluation Training Test")
    print("=" * 70)

    success = test_training_with_original_eval()

    if success:
        print("\n✅ All tests passed! Original-level evaluation works correctly in training.")
        print("\n💡 You can now run full training with:")
        print("   python models/SeedVision_v1/main.py --sequential --max_memory 8.0")
        print("\n🎯 Expected output during training:")
        print("   📊 Sample-level R²:")
        print("     Oil: Training=0.xxxx, Validation=0.xxxx")
        print("     Protein: Training=0.xxxx, Validation=0.xxxx")
        print("   🎯 Original-level R²:")
        print("     Oil: 0.xxxx")
        print("     Protein: 0.xxxx")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")

    return success

if __name__ == "__main__":
    main()
