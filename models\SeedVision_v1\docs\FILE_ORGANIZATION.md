# SeedVision v1 - 文件整理方案

## 📋 当前问题

当前文件结构存在以下问题：
1. 根目录文件过多，缺乏分类
2. 测试文件散落在不同位置
3. 文档文件混杂在代码中
4. 输出文件和日志文件位置不统一

## 🎯 整理目标

1. **按功能分类**：将相关文件归类到对应目录
2. **清晰层次**：建立清晰的目录层次结构
3. **易于维护**：便于后续开发和维护
4. **标准化**：遵循Python项目标准结构

## 📁 新的文件结构

```
models/SeedVision_v1/
├── README.md                           # 项目主文档
├── main.py                            # 主程序入口（原有）
├── main_scheduler.py                  # 智能调度主程序
│
├── docs/                              # 📚 文档目录
│   ├── README.md                      # 文档索引
│   ├── OPTIMIZATION_SUMMARY.md        # 优化总结
│   ├── ORIGINAL_LEVEL_EVALUATION.md   # Original级别评估文档
│   ├── SAMPLING_STRATEGY_REFACTOR.md  # 采样策略重构文档
│   ├── SCHEDULER_SUMMARY.md           # 调度器总结
│   └── design.md                      # 设计文档
│
├── config/                            # ⚙️ 配置目录
│   ├── __init__.py
│   ├── config_loader.py               # 配置加载器
│   ├── training_config.yaml           # 训练配置文件
│   ├── CONFIG_GUIDE.md               # 配置指南
│   └── SAMPLING_STRATEGIES.md        # 采样策略文档
│
├── models/                            # 🧠 模型定义目录
│   ├── __init__.py
│   ├── FasterNet.py                   # FasterNet模型
│   ├── Mixed_YOLO_FasterNet.py        # 混合模型
│   ├── Simple_Mixed.py                # 简单混合模型
│   ├── fasternet_blocks.py            # FasterNet模块
│   └── model_utils.py                 # 模型工具函数
│
├── tools/                             # 🔧 工具目录
│   ├── __init__.py
│   ├── train_utils.py                 # 训练工具
│   ├── data/                          # 数据处理工具
│   │   ├── __init__.py
│   │   ├── load_data.py               # 数据加载
│   │   ├── data_process1.py           # 数据处理1
│   │   ├── data_process2.py           # 数据处理2
│   │   └── redistribute_data.py       # 数据重分布
│   ├── training/                      # 训练相关工具
│   │   ├── __init__.py
│   │   ├── train.py                   # 训练脚本
│   │   ├── validate.py                # 验证脚本
│   │   └── visualize.py               # 可视化脚本
│   └── analysis/                      # 分析工具
│       ├── __init__.py
│       ├── analyze_original_distribution.py
│       └── visualization_results/     # 可视化结果
│
├── scheduler/                         # 📋 调度器目录
│   ├── __init__.py
│   ├── resource_estimator.py          # 资源预估器
│   ├── task_scheduler.py              # 任务调度器
│   ├── process_manager.py             # 进程管理器
│   ├── README.md                      # 调度器文档
│   └── example_configs.json           # 示例配置
│
├── tests/                             # 🧪 测试目录
│   ├── __init__.py
│   ├── README.md                      # 测试说明
│   ├── unit/                          # 单元测试
│   │   ├── __init__.py
│   │   ├── test_config.py             # 配置测试
│   │   ├── test_data_loading.py       # 数据加载测试
│   │   └── test_validation_fix.py     # 验证修复测试
│   ├── integration/                   # 集成测试
│   │   ├── __init__.py
│   │   ├── test_cross_subset_sampling.py
│   │   ├── test_data_format_fix.py
│   │   ├── test_text_position_fix.py
│   │   └── test_visualization_fix.py
│   ├── examples/                      # 示例测试
│   │   ├── __init__.py
│   │   ├── test_original_evaluation.py
│   │   ├── test_training_with_original_eval.py
│   │   ├── simple_original_test.py
│   │   ├── scheduler_example.py
│   │   └── final_verification.py
│   └── debug/                         # 调试脚本
│       ├── __init__.py
│       └── debug_data_format.py
│
├── output/                            # 📊 输出目录
│   ├── models/                        # 模型输出
│   ├── results/                       # 训练结果
│   ├── logs/                          # 日志文件
│   └── visualizations/                # 可视化结果
│
└── scripts/                           # 📜 脚本目录
    ├── setup.py                       # 环境设置脚本
    ├── clean.py                       # 清理脚本
    └── organize_files.py               # 文件整理脚本
```

## 🔄 文件移动计划

### 第一步：创建新目录结构
```bash
mkdir -p docs tests/unit tests/integration tests/examples tests/debug
mkdir -p tools/data tools/training tools/analysis
mkdir -p output/models output/results output/logs output/visualizations
mkdir -p scripts
```

### 第二步：移动文档文件
```bash
# 移动到 docs/ 目录
OPTIMIZATION_SUMMARY.md → docs/
ORIGINAL_LEVEL_EVALUATION.md → docs/
SAMPLING_STRATEGY_REFACTOR.md → docs/
SCHEDULER_SUMMARY.md → docs/
design.md → docs/
```

### 第三步：重组工具文件
```bash
# 移动数据处理工具
tools/myscripts/load_data.py → tools/data/
tools/myscripts/data_process1.py → tools/data/
tools/myscripts/data_process2.py → tools/data/
tools/myscripts/redistribute_data.py → tools/data/

# 移动训练工具
tools/myscripts/train.py → tools/training/
tools/myscripts/validate.py → tools/training/
tools/myscripts/visualize.py → tools/training/

# 移动分析工具
tools/myscripts/analyze_original_distribution.py → tools/analysis/
tools/myscripts/visualization_results/ → tools/analysis/
```

### 第四步：重组测试文件
```bash
# 移动单元测试
test/test_config.py → tests/unit/
test/test_data_loading.py → tests/unit/
test/test_validation_fix.py → tests/unit/

# 移动集成测试
test/test_cross_subset_sampling.py → tests/integration/
test/test_data_format_fix.py → tests/integration/
test/test_text_position_fix.py → tests/integration/
test/test_visualization_fix.py → tests/integration/

# 移动示例测试
test_original_evaluation.py → tests/examples/
test_training_with_original_eval.py → tests/examples/
simple_original_test.py → tests/examples/
scheduler_example.py → tests/examples/
final_verification.py → tests/examples/

# 移动调试脚本
test/debug_data_format.py → tests/debug/
```

### 第五步：整理输出文件
```bash
# 移动输出文件
output/ → output/results/
logs/ → output/logs/
```

### 第六步：清理模型目录
```bash
# 移除嵌套的模型目录
models/SeedVision_v1/ → 删除（内容已在外层）
```

## 📝 需要更新的导入路径

### 1. 工具模块导入
```python
# 原来
from tools.myscripts.train import train_model
from tools.myscripts.load_data import load_data

# 更新后
from tools.training.train import train_model
from tools.data.load_data import load_data
```

### 2. 测试文件导入
```python
# 原来
from test.test_config import TestConfig

# 更新后
from tests.unit.test_config import TestConfig
```

### 3. 配置文件路径
```python
# 原来
config_path = "config/training_config.yaml"

# 更新后（保持不变，因为相对路径正确）
config_path = "config/training_config.yaml"
```

## 🎯 整理后的优势

### 1. **清晰的功能分离**
- 📚 `docs/` - 所有文档集中管理
- 🧠 `models/` - 模型定义清晰分离
- 🔧 `tools/` - 工具按功能分类
- 🧪 `tests/` - 测试按类型组织
- 📊 `output/` - 输出文件统一管理

### 2. **标准化结构**
- 遵循Python项目标准结构
- 便于IDE识别和索引
- 符合开源项目规范

### 3. **易于维护**
- 相关文件集中在一起
- 减少文件查找时间
- 便于新功能添加

### 4. **更好的可扩展性**
- 为未来功能预留空间
- 模块化设计便于重用
- 清晰的依赖关系

## 🚀 执行整理

整理过程将分步进行：
1. 创建新目录结构
2. 移动文件到新位置
3. 更新导入路径
4. 测试功能完整性
5. 更新文档

这样整理后，项目结构将更加清晰、专业，便于长期维护和开发。
