'''
模型评价方法，使用torch的
决定系数（R2）：衡量预测值与真实值的拟合程度（越接近 1 越好）。
均方根误差（RMSE）：反映预测误差的标准差（值越小越好）。
平均绝对误差（MAE）：计算预测值与真实值误差的绝对值均值（值越小越好）。
剩余预测偏差（RPD）：评估模型预测准确性（RPD>2.5 表示良好）。
'''

import torch
import numpy as np

def calculate_r2(y_true, y_pred):
    """计算决定系数（R2）"""
    ss_total = torch.sum((y_true - torch.mean(y_true)) ** 2)
    ss_residual = torch.sum((y_true - y_pred) ** 2)
    r2 = 1 - (ss_residual / ss_total)
    return r2.item()

def calculate_rmse(y_true, y_pred):
    """计算均方根误差（RMSE）"""
    mse = torch.mean((y_true - y_pred) ** 2)
    rmse = torch.sqrt(mse)
    return rmse.item()

def calculate_mae(y_true, y_pred):
    """计算平均绝对误差（MAE）"""
    mae = torch.mean(torch.abs(y_true - y_pred))
    return mae.item()

def calculate_rpd(y_true, y_pred):
    """计算剩余预测偏差（RPD）"""
    rmse = calculate_rmse(y_true, y_pred)
    std = torch.std(y_true)
    rpd = std / rmse
    return rpd.item()

def evaluate_model(y_true, y_pred):
    """综合评估模型"""
    r2 = calculate_r2(y_true, y_pred)
    rmse = calculate_rmse(y_true, y_pred)
    mae = calculate_mae(y_true, y_pred)
    rpd = calculate_rpd(y_true, y_pred)

    return {
        'R2': r2,
        'RMSE': rmse,
        'MAE': mae,
        'RPD': rpd
    }

def evaluate_original_level(predictions, labels, original_names):
    """
    Original级别评估：对同一original下的所有样本预测值取均值，然后与标签比较

    参数:
        predictions: 预测值数组 (N, 2) - [oil, protein]
        labels: 标签值数组 (N, 2) - [oil, protein]
        original_names: original名称列表 (N,)

    返回:
        original_metrics: Original级别评估指标
        original_results: 每个original的详细结果
    """
    from collections import defaultdict

    # 按original分组
    original_groups = defaultdict(list)
    for i, original_name in enumerate(original_names):
        original_groups[original_name].append({
            'prediction': predictions[i],
            'label': labels[i],
            'index': i
        })

    # 计算每个original的均值预测和标签
    original_predictions = []
    original_labels = []
    original_results = {}

    for original_name, items in original_groups.items():
        # 提取该original的所有预测值和标签
        orig_preds = np.array([item['prediction'] for item in items])
        orig_labels = np.array([item['label'] for item in items])

        # 计算预测值的均值
        mean_pred = np.mean(orig_preds, axis=0)
        # 标签应该都相同，取第一个即可
        label = orig_labels[0]

        original_predictions.append(mean_pred)
        original_labels.append(label)

        # 保存详细结果
        original_results[original_name] = {
            'sample_count': len(items),
            'predictions': orig_preds,
            'labels': orig_labels,
            'mean_prediction': mean_pred,
            'label': label,
            'oil_std': np.std(orig_preds[:, 0]),  # 油含量预测的标准差
            'protein_std': np.std(orig_preds[:, 1])  # 蛋白质含量预测的标准差
        }

    # 转换为numpy数组
    original_predictions = np.array(original_predictions)
    original_labels = np.array(original_labels)

    # 转换为torch张量进行评估
    original_preds_tensor = torch.tensor(original_predictions, dtype=torch.float32)
    original_labels_tensor = torch.tensor(original_labels, dtype=torch.float32)

    # 计算Original级别的评估指标
    original_metrics = {
        'oil': {
            'R2': calculate_r2(original_labels_tensor[:, 0], original_preds_tensor[:, 0]),
            'RMSE': calculate_rmse(original_labels_tensor[:, 0], original_preds_tensor[:, 0]),
            'MAE': calculate_mae(original_labels_tensor[:, 0], original_preds_tensor[:, 0]),
            'RPD': calculate_rpd(original_labels_tensor[:, 0], original_preds_tensor[:, 0])
        },
        'protein': {
            'R2': calculate_r2(original_labels_tensor[:, 1], original_preds_tensor[:, 1]),
            'RMSE': calculate_rmse(original_labels_tensor[:, 1], original_preds_tensor[:, 1]),
            'MAE': calculate_mae(original_labels_tensor[:, 1], original_preds_tensor[:, 1]),
            'RPD': calculate_rpd(original_labels_tensor[:, 1], original_preds_tensor[:, 1])
        }
    }

    return original_metrics, original_results
