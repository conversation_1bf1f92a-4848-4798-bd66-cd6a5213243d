#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 统一训练系统主入口

功能选择：
1. 基础训练 (单个或多个配置)
2. 智能调度训练 (高级功能)
3. 配置管理

使用方法：
python main.py
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def show_menu():
    """显示主菜单"""
    print("=" * 60)
    print("SeedVision v1 - 训练系统")
    print("=" * 60)
    print("1. 基础训练 (推荐)")
    print("2. 智能调度训练 (高级)")
    print("3. 配置管理工具")
    print("4. 退出")
    print("-" * 60)

def main():
    """主函数"""
    while True:
        show_menu()
        choice = input("请选择功能 (1-4): ").strip()

        if choice == "1":
            print("\n启动基础训练...")
            from runners.training.main import main as training_main
            training_main()
            break
        elif choice == "2":
            print("\n启动智能调度训练...")
            from runners.training.main_scheduler import main as scheduler_main
            scheduler_main()
            break
        elif choice == "3":
            print("\n启动配置管理工具...")
            from runners.utilities.config_mongo_tool import main as config_tool_main
            config_tool_main()
        elif choice == "4":
            print("退出系统")
            break
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
