#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB 连接器使用示例

演示如何使用 MongoDB 连接器进行数据库操作
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def example_basic_usage():
    """基础使用示例"""
    print("\n[EXAMPLE 1] 基础使用示例")
    print("-" * 40)

    try:
        from utils.db_utils import database_connecter

        # 获取数据库连接器实例
        db_conn = database_connecter

        if db_conn is None:
            print("[ERROR] 数据库连接器未能初始化")
            return False

        # 列出所有 MongoDB 数据库
        databases = db_conn.list_mongodb_databases()
        print(f"[INFO] 可用数据库: {databases}")

        # 选择一个数据库
        my_db = db_conn.get_mongodb_database('example_db')
        print(f"[INFO] 选择数据库: {my_db.name}")

        # 获取集合
        users_collection = my_db['users']

        # 插入文档
        user_doc = {
            'name': '张三',
            'age': 25,
            'email': '<EMAIL>',
            'created_at': datetime.now()
        }

        result = users_collection.insert_one(user_doc)
        print(f"[INFO] 插入用户文档，ID: {result.inserted_id}")

        # 查询文档
        found_user = users_collection.find_one({'name': '张三'})
        if found_user:
            print(f"[INFO] 查询到用户: {found_user['name']}, 邮箱: {found_user['email']}")

        # 更新文档
        update_result = users_collection.update_one(
            {'name': '张三'},
            {'$set': {'age': 26}}
        )
        print(f"[INFO] 更新文档数量: {update_result.modified_count}")

        # 删除文档
        delete_result = users_collection.delete_one({'name': '张三'})
        print(f"[INFO] 删除文档数量: {delete_result.deleted_count}")

        return True

    except Exception as e:
        print(f"[ERROR] 基础使用示例失败: {e}")
        return False

def example_multiple_databases():
    """多数据库使用示例"""
    print("\n[EXAMPLE 2] 多数据库使用示例")
    print("-" * 40)

    try:
        from utils.db_utils import database_connecter

        db_conn = database_connecter

        # 使用不同的数据库
        user_db = db_conn.get_mongodb_database('user_system')
        log_db = db_conn.get_mongodb_database('log_system')
        analytics_db = db_conn.get_mongodb_database('analytics')

        print(f"[INFO] 用户系统数据库: {user_db.name}")
        print(f"[INFO] 日志系统数据库: {log_db.name}")
        print(f"[INFO] 分析系统数据库: {analytics_db.name}")

        # 在不同数据库中操作
        user_db['profiles'].insert_one({'user_id': 1, 'profile': 'admin'})
        log_db['access_logs'].insert_one({'timestamp': datetime.now(), 'action': 'login'})
        analytics_db['metrics'].insert_one({'date': datetime.now().date().isoformat(), 'visits': 100})

        print("[INFO] 在多个数据库中插入数据成功")

        # 清理测试数据
        user_db['profiles'].delete_many({})
        log_db['access_logs'].delete_many({})
        analytics_db['metrics'].delete_many({})

        print("[INFO] 清理测试数据完成")

        return True

    except Exception as e:
        print(f"[ERROR] 多数据库示例失败: {e}")
        return False

def example_training_data_storage():
    """训练数据存储示例"""
    print("\n[EXAMPLE 3] 训练数据存储示例")
    print("-" * 40)

    try:
        from utils.db_utils import database_connecter

        db_conn = database_connecter

        # 选择训练数据库
        training_db = db_conn.get_mongodb_database('seedvision_training')

        # 存储训练配置
        configs_collection = training_db['training_configs']

        training_config = {
            'config_name': 'user_224x224_norm_high_lr',
            'model_type': 'FasterNet',
            'input_size': [224, 224],
            'normalization': True,
            'learning_rate': 0.01,
            'batch_size': 32,
            'epochs': 100,
            'created_at': datetime.now(),
            'status': 'pending'
        }

        config_result = configs_collection.insert_one(training_config)
        print(f"[INFO] 存储训练配置，ID: {config_result.inserted_id}")

        # 存储训练结果
        results_collection = training_db['training_results']

        training_result = {
            'config_id': config_result.inserted_id,
            'config_name': 'user_224x224_norm_high_lr',
            'final_loss': 0.0234,
            'best_r2_oil': 0.892,
            'best_r2_protein': 0.876,
            'training_time_hours': 4.2,
            'model_path': '/path/to/best_model.pth',
            'completed_at': datetime.now(),
            'status': 'completed'
        }

        result_result = results_collection.insert_one(training_result)
        print(f"[INFO] 存储训练结果，ID: {result_result.inserted_id}")

        # 查询训练历史
        completed_trainings = results_collection.find({'status': 'completed'})
        count = results_collection.count_documents({'status': 'completed'})
        print(f"[INFO] 查询到 {count} 个已完成的训练")

        # 清理测试数据
        configs_collection.delete_many({})
        results_collection.delete_many({})

        print("[INFO] 训练数据存储示例完成")

        return True

    except Exception as e:
        print(f"[ERROR] 训练数据存储示例失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("[MONGODB EXAMPLES] MongoDB 连接器使用示例")
    print("=" * 60)

    # 检查依赖
    try:
        import pymongo
        print(f"[INFO] PyMongo 版本: {pymongo.version}")
    except ImportError:
        print("[ERROR] 请先安装 pymongo: pip install pymongo")
        return

    # 运行示例
    examples = [
        ("基础使用", example_basic_usage),
        ("多数据库使用", example_multiple_databases),
        ("训练数据存储", example_training_data_storage)
    ]

    success_count = 0
    for name, example_func in examples:
        try:
            if example_func():
                success_count += 1
                print(f"[SUCCESS] {name} 示例执行成功")
            else:
                print(f"[FAILED] {name} 示例执行失败")
        except Exception as e:
            print(f"[ERROR] {name} 示例异常: {e}")

    print(f"\n[SUMMARY] {success_count}/{len(examples)} 个示例执行成功")

    print("\n" + "=" * 60)
    print("[USAGE] 在您的代码中使用 MongoDB 连接器:")
    print("=" * 60)
    print("""
# 方式1: 使用统一连接器
from utils.db_utils import database_connecter

db_conn = database_connecter
my_db = db_conn.get_mongodb_database('my_database')
collection = my_db['my_collection']
collection.insert_one({'key': 'value'})

# 方式2: 使用便捷函数
from utils.db_utils import get_mongodb_database

my_db = get_mongodb_database('my_database')
collection = my_db['my_collection']
collection.insert_one({'key': 'value'})

# 方式3: 同时使用 MySQL 和 MongoDB
from utils.db_utils import database_connecter

db_conn = database_connecter
mysql_conn = db_conn.get_mysql_connection()  # MySQL
mongo_db = db_conn.get_mongodb_database('my_db')  # MongoDB
    """)

if __name__ == "__main__":
    main()
