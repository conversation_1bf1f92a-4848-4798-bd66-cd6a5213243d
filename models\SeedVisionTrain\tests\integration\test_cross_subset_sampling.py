#!/usr/bin/env python3
"""
测试跨子集采样功能的脚本
验证能否无视train/val/test划分，从整个数据集中为每个original提取足够样本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('E:\Proj\pytorch-model-train')

from tools.myscripts.load_data import load_data, get_subset, fixed_sample_by_original_cross_subset, analyze_original_images
from config.config_loader import Config<PERSON>oader

def test_cross_subset_sampling():
    """测试跨子集采样功能"""
    print("🧪 Testing Cross-Subset Sampling...")
    print("="*60)
    
    try:
        # 加载所有数据
        print("Loading all data...")
        all_data = load_data()
        
        # 分析原始数据分布
        print(f"\n📊 Original Data Distribution:")
        train_data = get_subset(all_data, 'train')
        val_data = get_subset(all_data, 'val')
        test_data = get_subset(all_data, 'test')
        
        print(f"  - Total data: {len(all_data)}")
        print(f"  - Train: {len(train_data)}")
        print(f"  - Val: {len(val_data)}")
        print(f"  - Test: {len(test_data)}")
        
        # 分析每个子集的original分布
        print(f"\n🔍 Analyzing Original Distribution in Each Subset:")
        
        train_originals = analyze_original_images(train_data)
        val_originals = analyze_original_images(val_data)
        test_originals = analyze_original_images(test_data)
        all_originals = analyze_original_images(all_data)
        
        print(f"  - Train originals: {len(train_originals)}")
        print(f"  - Val originals: {len(val_originals)}")
        print(f"  - Test originals: {len(test_originals)}")
        print(f"  - All originals: {len(all_originals)}")
        
        # 找出有足够样本的original（在所有数据中）
        target_samples = 60
        valid_originals_all = {k: v for k, v in all_originals.items() if len(v) >= target_samples}
        valid_originals_train = {k: v for k, v in train_originals.items() if len(v) >= target_samples}
        
        print(f"\n📈 Original Availability Analysis (for {target_samples} samples):")
        print(f"  - Valid originals in ALL data: {len(valid_originals_all)}")
        print(f"  - Valid originals in TRAIN only: {len(valid_originals_train)}")
        print(f"  - Improvement: +{len(valid_originals_all) - len(valid_originals_train)} originals")
        
        # 测试跨子集采样
        print(f"\n🌐 Testing Cross-Subset Sampling:")
        
        test_config = {
            'target_originals': 10,
            'samples_per_original': 60,
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1
        }
        
        print(f"  - Target originals: {test_config['target_originals']}")
        print(f"  - Samples per original: {test_config['samples_per_original']}")
        print(f"  - Train/Val/Test ratio: {test_config['train_ratio']}/{test_config['val_ratio']}/{test_config['test_ratio']}")
        
        # 执行跨子集采样
        new_train, new_val, new_test, original_mapping = fixed_sample_by_original_cross_subset(
            all_data, original_sampling_config=test_config
        )
        
        print(f"\n✅ Cross-Subset Sampling Results:")
        print(f"  - New train: {len(new_train)} samples")
        print(f"  - New val: {len(new_val)} samples")
        print(f"  - New test: {len(new_test)} samples")
        print(f"  - Total: {len(new_train) + len(new_val) + len(new_test)} samples")
        print(f"  - Originals used: {len(original_mapping)}")
        
        # 验证每个original的分布
        print(f"\n🔍 Verifying Original Distribution:")
        for i, (original_name, mapping) in enumerate(original_mapping.items()):
            if i < 3:  # 只显示前3个original的详情
                print(f"  Original {i+1}: {original_name}")
                print(f"    - Total samples: {len(mapping['all'])}")
                print(f"    - Train: {len(mapping['train'])}")
                print(f"    - Val: {len(mapping['val'])}")
                print(f"    - Test: {len(mapping['test'])}")
        
        if len(original_mapping) > 3:
            print(f"  ... and {len(original_mapping) - 3} more originals")
        
        # 验证数据来源
        print(f"\n🔍 Verifying Data Source Distribution:")
        
        # 统计新训练集中数据的原始来源
        train_sources = {'train': 0, 'val': 0, 'test': 0}
        for item in new_train:
            original_subset = item.get('subset', 'unknown')
            if original_subset in train_sources:
                train_sources[original_subset] += 1
        
        print(f"  New training set sources:")
        for source, count in train_sources.items():
            percentage = (count / len(new_train)) * 100 if len(new_train) > 0 else 0
            print(f"    - From original {source}: {count} ({percentage:.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ Cross-subset sampling test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_integration():
    """测试配置集成"""
    print(f"\n🔧 Testing Configuration Integration...")
    print("="*60)
    
    try:
        # 加载配置
        config_loader = ConfigLoader()
        enabled_configs = config_loader.get_enabled_training_configs()
        
        for config in enabled_configs:
            if 'original' in config['name'].lower():
                print(f"\n📋 Testing config: {config['name']}")
                
                original_sampling = config.get('original_sampling_config', {})
                cross_subset = original_sampling.get('cross_subset_sampling', False)
                
                print(f"  - Cross-subset sampling: {'✅ Enabled' if cross_subset else '❌ Disabled'}")
                
                if cross_subset:
                    print(f"  - Train ratio: {original_sampling.get('train_ratio', 'N/A')}")
                    print(f"  - Val ratio: {original_sampling.get('val_ratio', 'N/A')}")
                    print(f"  - Test ratio: {original_sampling.get('test_ratio', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration integration test failed: {e}")
        return False

def compare_sampling_strategies():
    """比较传统采样和跨子集采样的效果"""
    print(f"\n⚖️  Comparing Sampling Strategies...")
    print("="*60)
    
    try:
        # 加载数据
        all_data = load_data()
        train_data = get_subset(all_data, 'train')
        
        config = {
            'target_originals': 20,
            'samples_per_original': 60,
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1
        }
        
        print(f"📊 Comparison for {config['target_originals']} originals × {config['samples_per_original']} samples:")
        
        # 1. 传统采样（仅从训练集）
        print(f"\n1️⃣ Traditional Sampling (Train subset only):")
        train_originals = analyze_original_images(train_data)
        valid_train_originals = {k: v for k, v in train_originals.items() if len(v) >= config['samples_per_original']}
        
        print(f"  - Available originals: {len(train_originals)}")
        print(f"  - Valid originals: {len(valid_train_originals)}")
        print(f"  - Can satisfy request: {'✅ Yes' if len(valid_train_originals) >= config['target_originals'] else '❌ No'}")
        
        # 2. 跨子集采样（从所有数据）
        print(f"\n2️⃣ Cross-Subset Sampling (All data):")
        all_originals = analyze_original_images(all_data)
        valid_all_originals = {k: v for k, v in all_originals.items() if len(v) >= config['samples_per_original']}
        
        print(f"  - Available originals: {len(all_originals)}")
        print(f"  - Valid originals: {len(valid_all_originals)}")
        print(f"  - Can satisfy request: {'✅ Yes' if len(valid_all_originals) >= config['target_originals'] else '❌ No'}")
        
        # 3. 改进效果
        improvement = len(valid_all_originals) - len(valid_train_originals)
        print(f"\n📈 Improvement Analysis:")
        print(f"  - Additional valid originals: +{improvement}")
        print(f"  - Improvement percentage: +{(improvement / len(valid_train_originals) * 100):.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison failed: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Testing Cross-Subset Sampling Feature...")
    print("="*80)
    
    # 测试跨子集采样
    sampling_success = test_cross_subset_sampling()
    
    # 测试配置集成
    config_success = test_config_integration()
    
    # 比较采样策略
    comparison_success = compare_sampling_strategies()
    
    # 总结
    print(f"\n" + "="*80)
    print("📊 CROSS-SUBSET SAMPLING TEST RESULTS")
    print("="*80)
    print(f"✅ Cross-Subset Sampling: {'PASS' if sampling_success else 'FAIL'}")
    print(f"✅ Configuration Integration: {'PASS' if config_success else 'FAIL'}")
    print(f"✅ Strategy Comparison: {'PASS' if comparison_success else 'FAIL'}")
    
    all_passed = sampling_success and config_success and comparison_success
    
    if all_passed:
        print(f"\n🎉 All cross-subset sampling tests passed!")
        print(f"\n💡 Key Benefits:")
        print(f"  1. 🌐 Can access samples from any subset (train/val/test)")
        print(f"  2. 📈 Increases available originals for sampling")
        print(f"  3. ⚖️  Maintains desired train/val/test ratios")
        print(f"  4. 🎯 Guarantees sufficient samples per original")
        print(f"\n🚀 Ready for training with cross-subset sampling!")
    else:
        print(f"\n❌ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
