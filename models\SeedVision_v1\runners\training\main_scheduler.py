#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 智能调度训练脚本

功能：
1. 集成资源预估、任务调度和进程管理
2. 智能GPU显存管理和任务排队
3. 支持多任务并行训练
4. 提供详细的资源监控和报告

使用方法：
python main_scheduler.py --mode estimate  # 资源预估模式
python main_scheduler.py --mode schedule  # 调度训练模式
python main_scheduler.py --mode monitor   # 监控模式
"""

import os
import sys
import argparse
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append('E:\Proj\pytorch-model-train')

from config.config_loader import ConfigLoader
from scheduler import SchedulerManager, TrainingTask, TaskPriority, ResourceEstimator

def estimate_resources(config_file=None):
    """资源预估模式"""
    print("🔍 资源预估模式")
    print("=" * 60)
    
    # 加载配置
    config_loader = ConfigLoader(config_file)
    configs = config_loader.get_enabled_training_configs()
    
    if not configs:
        print("❌ 没有找到启用的训练配置")
        return
    
    estimator = ResourceEstimator()
    
    print(f"📊 分析 {len(configs)} 个训练配置:")
    print()
    
    total_memory = 0
    total_time = 0
    
    for i, config in enumerate(configs):
        print(f"📋 配置 {i+1}: {config['name']}")
        print(f"   描述: {config.get('description', 'N/A')}")
        
        # 生成资源报告
        report = estimator.generate_resource_report(config)
        
        memory_gb = report['memory_estimate']['total_gb']
        time_hours = report['time_estimate']['total_hours']
        
        total_memory = max(total_memory, memory_gb)  # 并行时取最大值
        total_time += time_hours  # 串行时累加
        
        print(f"   💾 预估显存: {memory_gb:.2f} GB")
        print(f"   ⏱️  预估时间: {time_hours:.1f} 小时")
        print(f"   ✅ 可运行: {report['can_run']}")
        
        if not report['can_run']:
            print(f"   ❌ 原因: {report['run_reason']}")
        
        if report['recommendations']:
            print(f"   💡 建议:")
            for rec in report['recommendations']:
                print(f"      - {rec}")
        print()
    
    # 总结
    print("📈 总体预估:")
    print(f"   - 最大显存需求: {total_memory:.2f} GB")
    print(f"   - 串行总时间: {total_time:.1f} 小时")
    print(f"   - 并行总时间: {max(report['time_estimate']['total_hours'] for report in [estimator.generate_resource_report(c) for c in configs]):.1f} 小时")
    
    # 系统资源
    print("\n💻 当前系统资源:")
    resources = estimator.get_system_resources()
    
    if resources['gpu']:
        gpu_0 = resources['gpu']['gpu_0']
        print(f"   - GPU: {gpu_0['name']}")
        print(f"   - 总显存: {gpu_0['total_gb']:.1f} GB")
        print(f"   - 可用显存: {gpu_0['free_gb']:.1f} GB")
        print(f"   - 使用率: {gpu_0['utilization_percent']:.1f}%")
    
    print(f"   - CPU使用率: {resources['cpu']['usage_percent']:.1f}%")
    print(f"   - 内存可用: {resources['memory']['available_gb']:.1f} GB")

def schedule_training(config_file=None, max_memory=8.0, max_concurrent=2):
    """调度训练模式"""
    print("📋 智能调度训练模式")
    print("=" * 60)
    
    # 加载配置
    config_loader = ConfigLoader(config_file)
    configs = config_loader.get_enabled_training_configs()
    
    if not configs:
        print("❌ 没有找到启用的训练配置")
        return
    
    # 创建调度管理器
    scheduler = SchedulerManager(
        max_gpu_memory=max_memory,
        max_concurrent_tasks=max_concurrent,
        log_dir="logs/scheduler"
    )
    
    # 启动调度器
    print(f"🚀 启动调度器 (最大显存: {max_memory}GB, 最大并发: {max_concurrent})")
    scheduler.start()
    
    # 创建训练任务
    tasks = []
    for i, config in enumerate(configs):
        # 根据配置设置优先级
        priority = TaskPriority.NORMAL
        if 'urgent' in config['name'].lower():
            priority = TaskPriority.URGENT
        elif 'high' in config['name'].lower():
            priority = TaskPriority.HIGH
        elif 'low' in config['name'].lower():
            priority = TaskPriority.LOW
        
        task = TrainingTask(
            task_id=f"task_{i+1:03d}",
            name=config['name'],
            config=config,
            priority=priority,
            on_start=lambda t: print(f"🏃 任务开始: {t.name}"),
            on_complete=lambda t: print(f"✅ 任务完成: {t.name}"),
            on_error=lambda t, e: print(f"❌ 任务失败: {t.name} - {e}")
        )
        
        tasks.append(task)
        
        # 提交任务
        task_id = scheduler.submit_task(task)
        print(f"📤 提交任务: {config['name']} (ID: {task_id})")
    
    print(f"\n👀 监控 {len(tasks)} 个任务的执行状态...")
    
    # 监控任务执行
    start_time = time.time()
    last_status = None
    
    while True:
        status = scheduler.get_status()
        
        # 只在状态变化时打印
        current_status = (
            status['scheduler']['pending_count'],
            status['scheduler']['running_count'],
            status['scheduler']['completed_count'],
            status['scheduler']['failed_count']
        )
        
        if current_status != last_status:
            elapsed = time.time() - start_time
            print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} (运行 {elapsed/60:.1f} 分钟)")
            print(f"📊 队列状态: 等待={status['scheduler']['pending_count']}, "
                  f"运行={status['scheduler']['running_count']}, "
                  f"完成={status['scheduler']['completed_count']}, "
                  f"失败={status['scheduler']['failed_count']}")
            
            if status['processes']['total_processes'] > 0:
                print(f"🔄 进程状态: 总数={status['processes']['total_processes']}, "
                      f"已启动={status['processes']['statistics']['total_started']}, "
                      f"已完成={status['processes']['statistics']['total_completed']}")
            
            last_status = current_status
        
        # 检查是否所有任务都完成
        if (status['scheduler']['pending_count'] == 0 and 
            status['scheduler']['running_count'] == 0):
            break
        
        time.sleep(5)  # 每5秒检查一次
    
    # 生成最终报告
    print("\n📋 生成最终报告...")
    report = scheduler.generate_report()
    
    print(f"\n🏆 训练完成总结:")
    stats = report['scheduler_report']['statistics']
    print(f"   - 总提交: {stats['total_submitted']}")
    print(f"   - 总完成: {stats['total_completed']}")
    print(f"   - 总失败: {stats['total_failed']}")
    print(f"   - 成功率: {stats['success_rate_percent']:.1f}%")
    
    # 任务历史
    history = report['task_history']
    print(f"\n📚 任务历史:")
    for task_info in history:
        status_icon = "✅" if task_info['status'] == 'completed' else "❌" if task_info['status'] == 'failed' else "🔄"
        duration = f"{task_info['duration_hours']:.1f}h" if task_info['duration_hours'] else "N/A"
        print(f"   {status_icon} {task_info['name']} ({task_info['status']}) - {duration}")
    
    # 停止调度器
    scheduler.stop()
    print("\n✅ 调度器已停止")

def monitor_system():
    """系统监控模式"""
    print("📊 系统监控模式")
    print("=" * 60)
    
    estimator = ResourceEstimator()
    
    try:
        while True:
            # 清屏 (Windows)
            os.system('cls' if os.name == 'nt' else 'clear')
            
            print("📊 SeedVision v1 - 系统资源监控")
            print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)
            
            # 获取系统资源
            resources = estimator.get_system_resources()
            
            # CPU信息
            cpu = resources['cpu']
            print(f"💻 CPU:")
            print(f"   使用率: {cpu['usage_percent']:6.1f}%")
            print(f"   核心数: {cpu['core_count']:6d}")
            
            # 内存信息
            memory = resources['memory']
            print(f"\n🧠 内存:")
            print(f"   总内存: {memory['total_gb']:6.1f} GB")
            print(f"   已使用: {memory['used_gb']:6.1f} GB")
            print(f"   可用:   {memory['available_gb']:6.1f} GB")
            print(f"   使用率: {memory['usage_percent']:6.1f}%")
            
            # GPU信息
            print(f"\n🎮 GPU:")
            if resources['gpu']:
                for gpu_id, gpu_info in resources['gpu'].items():
                    print(f"   {gpu_id}: {gpu_info['name']}")
                    print(f"   总显存: {gpu_info['total_gb']:6.1f} GB")
                    print(f"   已用:   {gpu_info['cached_gb']:6.1f} GB")
                    print(f"   可用:   {gpu_info['free_gb']:6.1f} GB")
                    print(f"   使用率: {gpu_info['utilization_percent']:6.1f}%")
            else:
                print("   未检测到GPU")
            
            print(f"\n按 Ctrl+C 退出监控")
            time.sleep(2)  # 每2秒更新一次
            
    except KeyboardInterrupt:
        print(f"\n\n✅ 监控已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SeedVision v1 智能调度训练系统')
    parser.add_argument('--mode', choices=['estimate', 'schedule', 'monitor'], 
                       default='schedule', help='运行模式')
    parser.add_argument('--config', type=str, default=None, help='配置文件路径')
    parser.add_argument('--max_memory', type=float, default=8.0, help='最大GPU显存限制(GB)')
    parser.add_argument('--max_concurrent', type=int, default=2, help='最大并发任务数')
    
    args = parser.parse_args()
    
    print("🚀 SeedVision v1 - 智能调度训练系统")
    print("=" * 80)
    
    try:
        if args.mode == 'estimate':
            estimate_resources(args.config)
        elif args.mode == 'schedule':
            schedule_training(args.config, args.max_memory, args.max_concurrent)
        elif args.mode == 'monitor':
            monitor_system()
    except KeyboardInterrupt:
        print(f"\n\n✅ 程序已停止")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
