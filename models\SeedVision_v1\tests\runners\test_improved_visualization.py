#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进后的图像生成和输出路径测试脚本

测试功能：
1. 标准化输出目录结构
2. 改进的文本位置算法
3. 更美观的图像样式
4. 训练和测试输出分离
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_output_config():
    """测试输出配置系统"""
    print("🧪 Testing Output Configuration System...")
    
    try:
        from config.output_config import OutputConfig, get_training_paths, get_testing_paths
        
        # 创建输出配置
        config = OutputConfig()
        
        # 打印目录结构
        print("📁 Directory Structure:")
        config.print_structure()
        
        # 创建所有目录
        config.create_all_directories()
        print("✅ All directories created")
        
        # 测试训练路径
        training_paths = get_training_paths("test_task")
        print(f"\n📋 Training Paths for 'test_task':")
        for key, path in training_paths.items():
            print(f"   {key}: {path}")
        
        # 测试测试路径
        testing_paths = get_testing_paths("test_visualization")
        print(f"\n📋 Testing Paths for 'test_visualization':")
        for key, path in testing_paths.items():
            print(f"   {key}: {path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Output configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_improved_text_positioning():
    """测试改进的文本位置算法"""
    print("\n🧪 Testing Improved Text Positioning...")
    
    try:
        from tools.training.visualize import find_best_text_position, plot_protein_regression, plot_oil_regression
        from config.output_config import get_testing_paths
        
        # 获取测试路径
        paths = get_testing_paths("improved_visualization")
        viz_dir = paths['viz_dir']
        
        # 创建不同分布的测试数据
        test_cases = [
            {
                'name': 'scattered_data',
                'description': '分散数据 - 测试智能位置选择',
                'data_func': lambda: (
                    np.random.uniform(20, 40, 100),
                    np.random.uniform(15, 45, 100)
                )
            },
            {
                'name': 'diagonal_data',
                'description': '对角线数据 - 测试对角线避让',
                'data_func': lambda: (
                    np.linspace(20, 40, 100) + np.random.normal(0, 1, 100),
                    np.linspace(20, 40, 100) + np.random.normal(0, 1, 100)
                )
            },
            {
                'name': 'corner_dense_data',
                'description': '角落密集数据 - 测试密度避让',
                'data_func': lambda: (
                    np.concatenate([np.random.uniform(20, 25, 50), np.random.uniform(35, 40, 50)]),
                    np.concatenate([np.random.uniform(35, 40, 50), np.random.uniform(20, 25, 50)])
                )
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            print(f"   Testing case {i+1}: {test_case['description']}")
            
            # 生成测试数据
            true_values, pred_values = test_case['data_func']()
            
            # 测试文本位置算法
            position, corner_name = find_best_text_position(true_values, pred_values)
            print(f"      Best position: {corner_name} at {position}")
            
            # 生成蛋白质回归图
            protein_path = os.path.join(viz_dir, f"protein_{test_case['name']}.png")
            plot_protein_regression(
                true_values,
                pred_values,
                title=f"Protein Content - {test_case['description']}",
                save_path=protein_path
            )
            
            # 生成含油量回归图
            oil_path = os.path.join(viz_dir, f"oil_{test_case['name']}.png")
            plot_oil_regression(
                true_values + 10,  # 稍微调整数值范围
                pred_values + 10,
                title=f"Oil Content - {test_case['description']}",
                save_path=oil_path
            )
            
            print(f"      ✅ Generated: {protein_path}")
            print(f"      ✅ Generated: {oil_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Improved text positioning test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_with_new_paths():
    """测试使用新路径的训练功能"""
    print("\n🧪 Testing Training with New Output Paths...")
    
    try:
        from tools.training.train import train_model
        from config.output_config import get_training_paths, get_testing_paths
        
        # 创建简单的测试配置
        model_config = {
            'embed_dim': 32,
            'depths': [1, 1, 2, 1],
            'mlp_ratio': 2.0,
            'n_div': 2,
            'drop_path_rate': 0.1
        }
        
        hyperparam_config = {
            'learning_rate': 0.001,
            'weight_decay': 1e-4,
            'optimizer': 'Adam',
            'scheduler': 'ReduceLROnPlateau',
            'scheduler_params': {'mode': 'min', 'factor': 0.5, 'patience': 2}
        }
        
        sampling_strategy_config = {
            'strategy_type': 'random',
            'parameters': {
                'sample_size': 50,
                'seed': 42
            }
        }
        
        # 测试训练模式
        print("   Testing training mode...")
        task_name = "test_training_paths"
        
        # 获取预期的路径
        expected_paths = get_training_paths(task_name)
        print(f"   Expected training paths:")
        for key, path in expected_paths.items():
            print(f"      {key}: {path}")
        
        # 运行训练（只训练1个epoch用于测试）
        history, model = train_model(
            model_config=model_config,
            hyperparam_config=hyperparam_config,
            num_epochs=1,
            batch_size=10,
            task_name=task_name,
            sampling_strategy_config=sampling_strategy_config,
            test_mode=False
        )
        
        # 检查生成的文件
        print("   Checking generated files...")
        viz_dir = expected_paths['viz_dir']
        if os.path.exists(viz_dir):
            files = [f for f in os.listdir(viz_dir) if f.endswith('.png')]
            print(f"   ✅ Generated {len(files)} visualization files:")
            for file in files:
                print(f"      📄 {file}")
        
        # 测试测试模式
        print("\n   Testing test mode...")
        test_task_name = "test_testing_paths"
        
        # 获取预期的测试路径
        expected_test_paths = get_testing_paths(test_task_name)
        print(f"   Expected testing paths:")
        for key, path in expected_test_paths.items():
            print(f"      {key}: {path}")
        
        # 运行测试模式训练
        history, model = train_model(
            model_config=model_config,
            hyperparam_config=hyperparam_config,
            num_epochs=1,
            batch_size=10,
            task_name=test_task_name,
            sampling_strategy_config=sampling_strategy_config,
            test_mode=True
        )
        
        # 检查生成的测试文件
        print("   Checking generated test files...")
        test_viz_dir = expected_test_paths['viz_dir']
        if os.path.exists(test_viz_dir):
            test_files = [f for f in os.listdir(test_viz_dir) if f.endswith('.png')]
            print(f"   ✅ Generated {len(test_files)} test visualization files:")
            for file in test_files:
                print(f"      📄 {file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Training with new paths test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization_quality():
    """测试可视化质量改进"""
    print("\n🧪 Testing Visualization Quality Improvements...")
    
    try:
        from tools.training.visualize import plot_train_val_loss
        from config.output_config import get_testing_paths
        
        # 获取测试路径
        paths = get_testing_paths("quality_test")
        viz_dir = paths['viz_dir']
        
        # 创建高质量的模拟训练数据
        epochs = 30
        train_losses = []
        val_losses = []
        
        for i in range(epochs):
            # 更真实的损失曲线
            train_loss = 1.5 * np.exp(-0.1 * i) + 0.05 + 0.02 * np.random.randn()
            val_loss = 1.6 * np.exp(-0.08 * i) + 0.08 + 0.03 * np.random.randn()
            
            # 添加一些过拟合效果
            if i > 15:
                val_loss += 0.001 * (i - 15)
            
            train_losses.append(max(0.02, train_loss))
            val_losses.append(max(0.05, val_loss))
        
        # 生成高质量损失曲线
        loss_path = os.path.join(viz_dir, "high_quality_loss_curve.png")
        plot_train_val_loss(
            train_losses,
            val_losses,
            title="High Quality Training and Validation Loss Curve",
            save_path=loss_path
        )
        
        print(f"   ✅ High quality loss curve: {loss_path}")
        
        # 检查文件大小和质量
        if os.path.exists(loss_path):
            file_size = os.path.getsize(loss_path) / 1024  # KB
            print(f"   📊 File size: {file_size:.1f} KB")
            
            if file_size > 100:  # 高质量图片应该有合理的文件大小
                print("   ✅ File size indicates good quality")
            else:
                print("   ⚠️  File size might be too small")
        
        return True
        
    except Exception as e:
        print(f"❌ Visualization quality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_summary_report(results):
    """生成测试总结报告"""
    print("\n" + "="*60)
    print("📊 Improved Visualization Test Summary Report")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    failed_tests = total_tests - passed_tests
    
    print(f"📈 Test Statistics:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {failed_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    print(f"\n📋 Detailed Results:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    # 检查生成的文件
    output_dirs = [
        "output/training",
        "output/testing",
        "output/scheduler",
        "output/analysis"
    ]
    
    print(f"\n📁 Generated Output Structure:")
    for output_dir in output_dirs:
        if os.path.exists(output_dir):
            print(f"   📂 {output_dir}/")
            for root, dirs, files in os.walk(output_dir):
                level = root.replace(output_dir, '').count(os.sep)
                indent = '   ' + '  ' * level
                print(f"{indent}📁 {os.path.basename(root)}/")
                subindent = '   ' + '  ' * (level + 1)
                for file in files[:5]:  # 只显示前5个文件
                    if file.endswith('.png'):
                        file_size = os.path.getsize(os.path.join(root, file)) / 1024
                        print(f"{subindent}📄 {file} ({file_size:.1f} KB)")
                if len(files) > 5:
                    print(f"{subindent}... and {len(files)-5} more files")
    
    if failed_tests == 0:
        print(f"\n🎉 All improved visualization tests passed!")
        print(f"✨ Image generation and output organization are working perfectly.")
        print(f"🎨 Text positioning improvements are effective.")
        print(f"📁 Standardized output structure is functional.")
    else:
        print(f"\n⚠️  {failed_tests} tests failed.")
        print(f"🔧 Please check the error messages above.")
    
    return failed_tests == 0

def main():
    """主函数"""
    print("🎨 SeedVision v1 - Improved Visualization Test Suite")
    print("=" * 60)
    
    # 运行测试
    tests = [
        ("Output Configuration", test_output_config),
        ("Improved Text Positioning", test_improved_text_positioning),
        ("Training with New Paths", test_training_with_new_paths),
        ("Visualization Quality", test_visualization_quality),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 生成总结报告
    success = generate_summary_report(results)
    
    return success

if __name__ == "__main__":
    main()
