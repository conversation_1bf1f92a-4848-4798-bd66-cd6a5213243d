#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 系统完整测试脚本

功能：
1. 测试所有模块导入
2. 测试配置系统
3. 测试数据加载
4. 测试模型功能
5. 测试训练流程
6. 测试调度系统
7. 测试Original级别评估
8. 生成完整测试报告

使用方法：
python system_test.py
"""

import sys
import os
import time
import traceback
from datetime import datetime
import json

# 添加路径
sys.path.append('E:\Proj\pytorch-model-train')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class SystemTester:
    """系统测试器"""

    def __init__(self):
        self.test_results = {}
        self.start_time = datetime.now()
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0

    def log_test(self, test_name, success, message="", details=None):
        """记录测试结果"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            status = "✅ PASS"
        else:
            self.failed_tests += 1
            status = "❌ FAIL"

        self.test_results[test_name] = {
            'status': status,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }

        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
        if not success and details:
            print(f"    Details: {details}")

    def test_imports(self):
        """测试模块导入"""
        print("\n测试模块导入")
        print("=" * 60)

        import_tests = [
            ("config.config_loader", "from config.config_loader import ConfigLoader"),
            ("models.FasterNet", "from models.FasterNet import model as FasterNet_model"),
            ("tools.data.load_data", "from tools.data.load_data import load_data"),
            ("tools.training.train", "from tools.training.train import train_model"),
            ("tools.training.validate", "from tools.training.validate import calculate_r2, evaluate_original_level"),
            ("tools.training.visualize", "from tools.training.visualize import plot_train_val_loss"),
            ("scheduler", "from scheduler import SchedulerManager, ResourceEstimator, TrainingTask"),
        ]

        for test_name, import_code in import_tests:
            try:
                exec(import_code)
                self.log_test(f"Import {test_name}", True, "模块导入成功")
            except Exception as e:
                self.log_test(f"Import {test_name}", False, f"导入失败: {e}", str(traceback.format_exc()))

    def test_configuration(self):
        """测试配置系统"""
        print("\n测试配置系统")
        print("=" * 60)

        try:
            from config.config_loader import ConfigLoader

            # 测试配置加载
            config_loader = ConfigLoader()
            self.log_test("Config Loading", True, "配置加载成功")

            # 测试配置解析
            configs = config_loader.get_enabled_training_configs()
            if configs:
                self.log_test("Config Parsing", True, f"找到 {len(configs)} 个启用的配置")

                # 测试配置内容
                config = configs[0]
                required_keys = ['name', 'model', 'resources', 'transform_config']
                missing_keys = [key for key in required_keys if key not in config]

                if not missing_keys:
                    self.log_test("Config Structure", True, "配置结构完整")
                else:
                    self.log_test("Config Structure", False, f"缺少配置项: {missing_keys}")
            else:
                self.log_test("Config Parsing", False, "未找到启用的配置")

        except Exception as e:
            self.log_test("Configuration System", False, f"配置系统测试失败: {e}", str(traceback.format_exc()))

    def test_data_loading(self):
        """测试数据加载"""
        print("\n测试数据加载")
        print("=" * 60)

        try:
            from tools.data.load_data import load_data, get_subset

            # 测试基础数据加载
            data = load_data()
            if data and len(data) > 0:
                self.log_test("Data Loading", True, f"加载数据 {len(data)} 条")

                # 测试数据格式
                sample = data[0]
                if len(sample) >= 3:  # (image, oil, protein, ...)
                    self.log_test("Data Format", True, "数据格式正确")
                else:
                    self.log_test("Data Format", False, f"数据格式异常: {len(sample)} 字段")

                # 测试子集获取
                train_data = get_subset(data, 'train')
                val_data = get_subset(data, 'val')

                if train_data and val_data:
                    self.log_test("Data Subset", True, f"训练集: {len(train_data)}, 验证集: {len(val_data)}")
                else:
                    self.log_test("Data Subset", False, "子集获取失败")
            else:
                self.log_test("Data Loading", False, "数据加载失败或为空")

        except Exception as e:
            self.log_test("Data Loading System", False, f"数据加载测试失败: {e}", str(traceback.format_exc()))

    def test_model_functionality(self):
        """测试模型功能"""
        print("\n测试模型功能")
        print("=" * 60)

        try:
            import torch
            from models.FasterNet import model as FasterNet_model

            # 测试模型创建
            model_config = {
                'embed_dim': 64,
                'depths': [2, 2, 8, 2],
                'mlp_ratio': 2.0,
                'n_div': 2,
                'drop_path_rate': 0.1
            }

            model = FasterNet_model(**model_config)
            self.log_test("Model Creation", True, "模型创建成功")

            # 测试模型前向传播
            test_input = torch.randn(2, 3, 224, 224)
            with torch.no_grad():
                output = model(test_input)

            if output.shape == (2, 2):  # batch_size=2, output_dim=2
                self.log_test("Model Forward", True, f"模型输出形状正确: {output.shape}")
            else:
                self.log_test("Model Forward", False, f"模型输出形状错误: {output.shape}")

        except Exception as e:
            self.log_test("Model Functionality", False, f"模型功能测试失败: {e}", str(traceback.format_exc()))

    def test_scheduler_system(self):
        """测试调度系统"""
        print("\n测试调度系统")
        print("=" * 60)

        try:
            from scheduler import ResourceEstimator, SchedulerManager, TrainingTask, TaskPriority

            # 测试资源预估器
            estimator = ResourceEstimator()
            resources = estimator.get_system_resources()

            if 'cpu' in resources and 'memory' in resources:
                self.log_test("Resource Monitor", True, "系统资源监控正常")
            else:
                self.log_test("Resource Monitor", False, "系统资源监控失败")

            # 测试资源预估
            test_config = {
                'name': 'test_config',
                'model': {'embed_dim': 64, 'depths': [2, 2, 8, 2]},
                'resources': {'batch_size': 20},
                'transform_config': '224x224_norm',
                'training': {'num_epochs': 10},
                'dataset_config': {'strategy_parameters': {'total_samples': 400}}
            }

            report = estimator.generate_resource_report(test_config)
            if 'memory_estimate' in report and 'time_estimate' in report:
                memory_gb = report['memory_estimate']['total_gb']
                time_hours = report['time_estimate']['total_hours']
                self.log_test("Resource Estimation", True, f"显存预估: {memory_gb:.2f}GB, 时间预估: {time_hours:.1f}h")
            else:
                self.log_test("Resource Estimation", False, "资源预估失败")

            # 测试调度管理器创建
            scheduler = SchedulerManager(max_gpu_memory=8.0, max_concurrent_tasks=1)
            self.log_test("Scheduler Creation", True, "调度管理器创建成功")

            # 测试任务创建
            task = TrainingTask(
                task_id='test_task',
                name='测试任务',
                config=test_config,
                priority=TaskPriority.NORMAL
            )
            self.log_test("Task Creation", True, "任务创建成功")

        except Exception as e:
            self.log_test("Scheduler System", False, f"调度系统测试失败: {e}", str(traceback.format_exc()))

    def test_original_evaluation(self):
        """测试Original级别评估"""
        print("\n测试Original级别评估")
        print("=" * 60)

        try:
            import numpy as np
            import torch
            from tools.training.validate import evaluate_original_level, calculate_r2

            # 创建测试数据
            predictions = np.array([
                [20.1, 35.2], [19.9, 34.8], [20.0, 35.0],  # Original A
                [25.1, 40.1], [24.9, 39.9], [25.0, 40.0], [25.2, 40.2],  # Original B
                [19.0, 32.0], [19.1, 32.1]  # Original C
            ])

            labels = np.array([
                [20.0, 35.0], [20.0, 35.0], [20.0, 35.0],  # Original A
                [25.0, 40.0], [25.0, 40.0], [25.0, 40.0], [25.0, 40.0],  # Original B
                [19.0, 32.0], [19.0, 32.0]  # Original C
            ])

            original_names = ['A', 'A', 'A', 'B', 'B', 'B', 'B', 'C', 'C']

            # 测试Original级别评估
            original_metrics, original_results = evaluate_original_level(
                predictions, labels, original_names
            )

            if original_metrics and original_results:
                oil_r2 = original_metrics['oil']['R2']
                protein_r2 = original_metrics['protein']['R2']
                self.log_test("Original Evaluation", True,
                            f"Oil R²: {oil_r2:.4f}, Protein R²: {protein_r2:.4f}")

                # 验证结果数量
                if len(original_results) == 3:  # A, B, C
                    self.log_test("Original Results", True, f"Original结果数量正确: {len(original_results)}")
                else:
                    self.log_test("Original Results", False, f"Original结果数量错误: {len(original_results)}")
            else:
                self.log_test("Original Evaluation", False, "Original级别评估失败")

        except Exception as e:
            self.log_test("Original Evaluation", False, f"Original评估测试失败: {e}", str(traceback.format_exc()))

    def test_training_pipeline(self):
        """测试训练流程（快速测试）"""
        print("\n测试训练流程")
        print("=" * 60)

        try:
            from config.config_loader import ConfigLoader
            from tools.training.train import train_model
            import torch

            # 加载配置
            config_loader = ConfigLoader()
            configs = config_loader.get_enabled_training_configs()

            if not configs:
                self.log_test("Training Pipeline", False, "没有可用的训练配置")
                return

            config = configs[0]

            # 创建快速测试配置
            test_config = {
                'model_config': config['model'],
                'hyperparam_config': config.get('hyperparameter_config_data', {}),
                'transform': config.get('transform'),
                'result_dir': 'output/system_test',
                'device': torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
                'num_epochs': 1,  # 只训练1个epoch
                'batch_size': 10,  # 小批次
                'sample_size': 100,  # 少量样本
                'task_name': 'SystemTest',
                'sampling_strategy_config': {
                    'strategy_type': 'random',
                    'parameters': {'sample_size': 100, 'seed': 42}
                }
            }

            # 运行快速训练测试
            print("    开始快速训练测试（1 epoch, 100 samples）...")
            history, model = train_model(**test_config)

            if history and 'train_losses' in history and len(history['train_losses']) > 0:
                self.log_test("Training Pipeline", True, "训练流程测试成功")
            else:
                self.log_test("Training Pipeline", False, "训练流程返回结果异常")

        except Exception as e:
            self.log_test("Training Pipeline", False, f"训练流程测试失败: {e}", str(traceback.format_exc()))

    def test_file_structure(self):
        """测试文件结构"""
        print("\n测试文件结构")
        print("=" * 60)

        required_dirs = [
            'docs', 'config', 'models', 'tools/data', 'tools/training',
            'tools/analysis', 'scheduler', 'tests/unit', 'tests/integration',
            'tests/examples', 'tests/debug', 'output'
        ]

        required_files = [
            'main.py', 'main_scheduler.py', 'config/config_loader.py',
            'config/training_config.yaml', 'models/FasterNet.py',
            'tools/training/train.py', 'tools/training/validate.py',
            'scheduler/resource_estimator.py', 'scheduler/task_scheduler.py'
        ]

        # 测试目录
        missing_dirs = []
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                missing_dirs.append(dir_path)

        if not missing_dirs:
            self.log_test("Directory Structure", True, f"所有必需目录存在 ({len(required_dirs)}个)")
        else:
            self.log_test("Directory Structure", False, f"缺少目录: {missing_dirs}")

        # 测试文件
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if not missing_files:
            self.log_test("File Structure", True, f"所有必需文件存在 ({len(required_files)}个)")
        else:
            self.log_test("File Structure", False, f"缺少文件: {missing_files}")

    def generate_report(self):
        """生成测试报告"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()

        print("\n" + "=" * 80)
        print("SeedVision v1 - 系统测试报告")
        print("=" * 80)

        print(f"测试统计:")
        print(f"   - 总测试数: {self.total_tests}")
        print(f"   - 通过: {self.passed_tests}")
        print(f"   - 失败: {self.failed_tests}")
        print(f"   - 成功率: {(self.passed_tests/self.total_tests*100):.1f}%")
        print(f"   - 测试时长: {duration:.1f}秒")

        print(f"\n详细结果:")
        for test_name, result in self.test_results.items():
            print(f"   {result['status']} {test_name}")
            if result['message']:
                print(f"      {result['message']}")

        # 保存报告到文件
        report_data = {
            'summary': {
                'total_tests': self.total_tests,
                'passed_tests': self.passed_tests,
                'failed_tests': self.failed_tests,
                'success_rate': self.passed_tests/self.total_tests*100,
                'duration_seconds': duration,
                'start_time': self.start_time.isoformat(),
                'end_time': end_time.isoformat()
            },
            'results': self.test_results
        }

        os.makedirs('output', exist_ok=True)
        report_file = f'output/system_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)

        print(f"\n详细报告已保存到: {report_file}")

        # 总结
        if self.failed_tests == 0:
            print(f"\n所有测试通过！系统运行正常。")
            print(f"SeedVision v1 已准备好投入使用！")
        else:
            print(f"\n有 {self.failed_tests} 个测试失败，请检查上述错误信息。")
            print(f"建议修复失败的测试项后再使用系统。")

    def run_all_tests(self):
        """运行所有测试"""
        print("SeedVision v1 - 系统完整测试")
        print("=" * 80)
        print(f"开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 按顺序执行所有测试
        self.test_file_structure()
        self.test_imports()
        self.test_configuration()
        self.test_data_loading()
        self.test_model_functionality()
        self.test_scheduler_system()
        self.test_original_evaluation()
        # self.test_training_pipeline()  # 可选：完整训练测试（耗时较长）

        # 生成报告
        self.generate_report()

def main():
    """主函数"""
    tester = SystemTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
