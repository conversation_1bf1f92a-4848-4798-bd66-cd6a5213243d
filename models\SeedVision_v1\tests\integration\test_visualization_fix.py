#!/usr/bin/env python3
"""
测试可视化修复的脚本
验证所有图表标题都使用英文，不会出现白框问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('E:\Proj\pytorch-model-train')
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import matplotlib.pyplot as plt

# 修复导入路径
try:
    from tools.myscripts.visualize import (
        plot_train_val_loss,
        plot_protein_regression,
        plot_oil_regression,
        setup_matplotlib
    )
except ImportError:
    # 如果直接导入失败，尝试相对导入
    import sys
    import os
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.insert(0, parent_dir)
    from tools.myscripts.visualize import (
        plot_train_val_loss,
        plot_protein_regression,
        plot_oil_regression,
        setup_matplotlib
    )

def test_matplotlib_setup():
    """测试matplotlib设置"""
    print("🧪 Testing Matplotlib Setup...")
    print("="*60)

    try:
        # 调用setup函数
        setup_matplotlib()

        # 检查字体设置
        print(f"✅ Font family: {plt.rcParams['font.sans-serif']}")
        print(f"✅ Unicode minus: {plt.rcParams['axes.unicode_minus']}")

        return True

    except Exception as e:
        print(f"❌ Matplotlib setup failed: {e}")
        return False

def test_loss_curve_visualization():
    """测试损失曲线可视化"""
    print(f"\n🧪 Testing Loss Curve Visualization...")
    print("="*60)

    try:
        # 创建模拟数据
        epochs = 50
        train_losses = [np.exp(-0.05 * i) + 0.1 + 0.02 * np.random.randn() for i in range(epochs)]
        val_losses = [np.exp(-0.04 * i) + 0.15 + 0.03 * np.random.randn() for i in range(epochs)]

        # 创建输出目录
        output_dir = "test_visualization_output"
        os.makedirs(output_dir, exist_ok=True)

        # 测试英文标题
        save_path = os.path.join(output_dir, "test_loss_curve.png")
        plot_train_val_loss(
            train_losses,
            val_losses,
            title="FasterNet Training and Validation Loss Curve",
            save_path=save_path
        )

        print(f"✅ Loss curve visualization successful!")
        print(f"  - File saved: {save_path}")
        print(f"  - Title: English (no Chinese characters)")

        return True

    except Exception as e:
        print(f"❌ Loss curve visualization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_regression_visualization():
    """测试回归图可视化"""
    print(f"\n🧪 Testing Regression Visualization...")
    print("="*60)

    try:
        # 创建模拟数据
        n_samples = 100

        # 油含量数据
        oil_true = 40 + 15 * np.random.rand(n_samples)
        oil_pred = oil_true + 2 * np.random.randn(n_samples)

        # 蛋白质含量数据
        protein_true = 20 + 10 * np.random.rand(n_samples)
        protein_pred = protein_true + 1.5 * np.random.randn(n_samples)

        # 创建输出目录
        output_dir = "test_visualization_output"
        os.makedirs(output_dir, exist_ok=True)

        # 测试油含量回归图
        oil_save_path = os.path.join(output_dir, "test_oil_regression.png")
        plot_oil_regression(
            oil_true,
            oil_pred,
            title="FasterNet Oil Content Prediction Results (Test)",
            save_path=oil_save_path
        )

        # 测试蛋白质含量回归图
        protein_save_path = os.path.join(output_dir, "test_protein_regression.png")
        plot_protein_regression(
            protein_true,
            protein_pred,
            title="FasterNet Protein Content Prediction Results (Test)",
            save_path=protein_save_path
        )

        print(f"✅ Regression visualization successful!")
        print(f"  - Oil regression file: {oil_save_path}")
        print(f"  - Protein regression file: {protein_save_path}")
        print(f"  - Titles: English (no Chinese characters)")
        print(f"  - Axis labels: English")

        return True

    except Exception as e:
        print(f"❌ Regression visualization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_r2_curve_visualization():
    """测试R2曲线可视化"""
    print(f"\n🧪 Testing R2 Curve Visualization...")
    print("="*60)

    try:
        # 创建模拟R2数据
        epochs = 50
        train_oil_r2 = [0.1 + 0.8 * (1 - np.exp(-0.1 * i)) + 0.05 * np.random.randn() for i in range(epochs)]
        val_oil_r2 = [0.05 + 0.75 * (1 - np.exp(-0.08 * i)) + 0.07 * np.random.randn() for i in range(epochs)]

        train_protein_r2 = [0.15 + 0.7 * (1 - np.exp(-0.09 * i)) + 0.04 * np.random.randn() for i in range(epochs)]
        val_protein_r2 = [0.1 + 0.65 * (1 - np.exp(-0.07 * i)) + 0.06 * np.random.randn() for i in range(epochs)]

        # 创建输出目录
        output_dir = "test_visualization_output"
        os.makedirs(output_dir, exist_ok=True)

        # 绘制油含量R2曲线
        plt.figure(figsize=(10, 6))
        plt.plot(range(1, epochs+1), train_oil_r2, 'b-', label='Training')
        plt.plot(range(1, epochs+1), val_oil_r2, 'r-', label='Validation')
        plt.title('Oil Content Prediction R² Curve', fontsize=16)
        plt.xlabel('Epoch', fontsize=14)
        plt.ylabel('R²', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        oil_r2_path = os.path.join(output_dir, "test_oil_r2_curve.png")
        plt.savefig(oil_r2_path, dpi=300, bbox_inches='tight')
        plt.close()

        # 绘制蛋白质含量R2曲线
        plt.figure(figsize=(10, 6))
        plt.plot(range(1, epochs+1), train_protein_r2, 'b-', label='Training')
        plt.plot(range(1, epochs+1), val_protein_r2, 'r-', label='Validation')
        plt.title('Protein Content Prediction R² Curve', fontsize=16)
        plt.xlabel('Epoch', fontsize=14)
        plt.ylabel('R²', fontsize=14)
        plt.legend(fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        protein_r2_path = os.path.join(output_dir, "test_protein_r2_curve.png")
        plt.savefig(protein_r2_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ R2 curve visualization successful!")
        print(f"  - Oil R2 curve: {oil_r2_path}")
        print(f"  - Protein R2 curve: {protein_r2_path}")
        print(f"  - All titles and labels: English")

        return True

    except Exception as e:
        print(f"❌ R2 curve visualization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_output_files():
    """检查输出文件"""
    print(f"\n🔍 Checking Output Files...")
    print("="*60)

    output_dir = "test_visualization_output"
    expected_files = [
        "test_loss_curve.png",
        "test_oil_regression.png",
        "test_protein_regression.png",
        "test_oil_r2_curve.png",
        "test_protein_r2_curve.png"
    ]

    all_exist = True
    for filename in expected_files:
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"✅ {filename}: {file_size:,} bytes")
        else:
            print(f"❌ {filename}: Missing")
            all_exist = False

    return all_exist

def main():
    """主测试函数"""
    print("🚀 Testing Visualization Fix...")
    print("="*80)

    # 测试matplotlib设置
    setup_success = test_matplotlib_setup()

    # 测试损失曲线
    loss_success = test_loss_curve_visualization()

    # 测试回归图
    regression_success = test_regression_visualization()

    # 测试R2曲线
    r2_success = test_r2_curve_visualization()

    # 检查输出文件
    files_success = check_output_files()

    # 总结
    print(f"\n" + "="*80)
    print("📊 VISUALIZATION FIX TEST RESULTS")
    print("="*80)
    print(f"✅ Matplotlib Setup: {'PASS' if setup_success else 'FAIL'}")
    print(f"✅ Loss Curve: {'PASS' if loss_success else 'FAIL'}")
    print(f"✅ Regression Plots: {'PASS' if regression_success else 'FAIL'}")
    print(f"✅ R2 Curves: {'PASS' if r2_success else 'FAIL'}")
    print(f"✅ Output Files: {'PASS' if files_success else 'FAIL'}")

    all_passed = setup_success and loss_success and regression_success and r2_success and files_success

    if all_passed:
        print(f"\n🎉 All visualization tests passed!")
        print(f"\n💡 Key Improvements:")
        print(f"  1. 🔤 All titles now use English instead of Chinese")
        print(f"  2. 🎨 Proper font configuration to avoid white boxes")
        print(f"  3. 📊 Consistent English labels and legends")
        print(f"  4. 🖼️  High-quality PNG output with proper encoding")
        print(f"\n📁 Test output saved to: test_visualization_output/")
        print(f"🔍 Please check the generated images to verify no white boxes appear")
    else:
        print(f"\n❌ Some visualization tests failed. Please check the errors above.")

    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
