#!/usr/bin/env python3
"""
测试数据格式修复的脚本
验证SeedDataset能否正确处理字典格式的数据
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('E:\Proj\pytorch-model-train')

import torch
from torch.utils.data import DataLoader
from tools.myscripts.load_data import load_data, fixed_sample_by_original_cross_subset
from tools.myscripts.train import SeedDataset

def test_seed_dataset_with_dict_data():
    """测试SeedDataset处理字典格式数据"""
    print("🧪 Testing SeedDataset with Dictionary Data...")
    print("="*60)
    
    try:
        # 加载数据并进行跨子集采样
        data = load_data()
        
        config = {
            'target_originals': 3,
            'samples_per_original': 5,
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1
        }
        
        print(f"🌐 Performing cross-subset sampling...")
        train_data, val_data, test_data, original_mapping = fixed_sample_by_original_cross_subset(
            data, original_sampling_config=config
        )
        
        print(f"📊 Sampling results:")
        print(f"  - Train data: {len(train_data)} items")
        print(f"  - Val data: {len(val_data)} items")
        print(f"  - Test data: {len(test_data)} items")
        
        # 检查数据格式
        if len(train_data) > 0:
            sample = train_data[0]
            print(f"\n🔍 Sample data format:")
            print(f"  - Type: {type(sample)}")
            print(f"  - Keys: {list(sample.keys()) if isinstance(sample, dict) else 'Not a dict'}")
        
        # 创建SeedDataset
        print(f"\n🎯 Creating SeedDataset...")
        train_dataset = SeedDataset(train_data)
        
        print(f"✅ SeedDataset created successfully!")
        print(f"  - Dataset length: {len(train_dataset)}")
        
        # 测试数据加载
        print(f"\n🔍 Testing data loading...")
        
        # 测试单个样本
        sample_image, sample_label = train_dataset[0]
        print(f"✅ Single sample loaded successfully!")
        print(f"  - Image shape: {sample_image.shape}")
        print(f"  - Label shape: {sample_label.shape}")
        print(f"  - Label values: {sample_label}")
        
        # 测试DataLoader
        print(f"\n🔍 Testing DataLoader...")
        train_loader = DataLoader(train_dataset, batch_size=2, shuffle=False)
        
        for batch_idx, (images, labels) in enumerate(train_loader):
            print(f"✅ Batch {batch_idx + 1} loaded successfully!")
            print(f"  - Images shape: {images.shape}")
            print(f"  - Labels shape: {labels.shape}")
            print(f"  - Labels: {labels}")
            
            if batch_idx >= 1:  # 只测试前2个batch
                break
        
        print(f"\n🎉 All tests passed! SeedDataset can handle dictionary data correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mixed_data_formats():
    """测试混合数据格式"""
    print(f"\n🧪 Testing Mixed Data Formats...")
    print("="*60)
    
    try:
        # 创建混合格式的测试数据
        from PIL import Image
        import numpy as np
        
        # 创建测试图像
        test_image = Image.new('RGB', (224, 224), color='red')
        
        # 混合格式数据
        mixed_data = [
            # 字典格式
            {
                'path': 'test_image_1.jpg',
                'oil': 35.5,
                'protein': 22.3,
                'original_image': 'original_1.jpg'
            },
            # 元组格式 (3元素)
            (test_image, 40.2, 25.1),
            # 元组格式 (4元素)
            (test_image, 38.7, 23.8, 'original_2.jpg')
        ]
        
        print(f"📊 Mixed data created:")
        print(f"  - Dict format: 1 item")
        print(f"  - Tuple (3): 1 item")
        print(f"  - Tuple (4): 1 item")
        
        # 创建数据集（注意：字典格式的数据会尝试加载图像文件，这里会失败但有fallback）
        print(f"\n🎯 Creating SeedDataset with mixed formats...")
        dataset = SeedDataset(mixed_data)
        
        print(f"✅ Mixed format dataset created!")
        print(f"  - Dataset length: {len(dataset)}")
        
        # 测试每种格式
        for i in range(len(dataset)):
            try:
                image, label = dataset[i]
                print(f"✅ Item {i} loaded successfully!")
                print(f"  - Image shape: {image.shape}")
                print(f"  - Label: {label}")
            except Exception as e:
                print(f"❌ Item {i} failed: {e}")
                return False
        
        print(f"\n🎉 Mixed format test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Mixed format test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Testing Data Format Fix...")
    print("="*80)
    
    # 测试字典格式数据
    dict_test_success = test_seed_dataset_with_dict_data()
    
    # 测试混合格式数据
    mixed_test_success = test_mixed_data_formats()
    
    # 总结
    print(f"\n" + "="*80)
    print("📊 DATA FORMAT FIX TEST RESULTS")
    print("="*80)
    print(f"✅ Dictionary Data Test: {'PASS' if dict_test_success else 'FAIL'}")
    print(f"✅ Mixed Format Test: {'PASS' if mixed_test_success else 'FAIL'}")
    
    all_passed = dict_test_success and mixed_test_success
    
    if all_passed:
        print(f"\n🎉 All data format tests passed!")
        print(f"\n💡 SeedDataset now supports:")
        print(f"  1. 📖 Dictionary format (from cross-subset sampling)")
        print(f"  2. 📝 Tuple format (3 elements: image, oil, protein)")
        print(f"  3. 📝 Tuple format (4 elements: image, oil, protein, original)")
        print(f"  4. 🔄 Automatic image loading from file paths")
        print(f"  5. 🛡️  Error handling with fallback images")
        print(f"\n🚀 Ready for training with cross-subset sampling!")
    else:
        print(f"\n❌ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
