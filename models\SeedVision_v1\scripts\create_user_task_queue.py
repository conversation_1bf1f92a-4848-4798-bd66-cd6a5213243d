#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户配置任务队列生成器

功能：
1. 自动将所有用户配置添加到任务队列
2. 支持优先级设置和任务过滤
3. 智能排序和资源预估
4. 生成任务执行计划

使用方法：
python create_user_task_queue.py --all                    # 添加所有用户配置
python create_user_task_queue.py --filter 224x224         # 只添加224x224配置
python create_user_task_queue.py --priority high          # 设置高优先级
python create_user_task_queue.py --dry-run               # 预览不执行
"""

import sys
import os
import yaml
import argparse
from datetime import datetime
import uuid

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scheduler.task_scheduler import TaskScheduler, TrainingTask, TaskPriority
from config.config_loader import ConfigLoader

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def load_user_configs():
    """加载所有用户配置"""
    try:
        config_loader = ConfigLoader()
        all_configs = config_loader.get_all_training_configs()
        
        # 筛选用户配置
        user_configs = {}
        for name, config in all_configs.items():
            if name.startswith('user_'):
                user_configs[name] = config
        
        return user_configs
    except Exception as e:
        print(f"{Colors.RED}[ERROR]{Colors.END} 无法加载配置: {e}")
        return {}

def filter_configs(configs, filter_text=None):
    """根据过滤条件筛选配置"""
    if not filter_text:
        return configs
    
    filtered = {}
    for name, config in configs.items():
        if filter_text.lower() in name.lower():
            filtered[name] = config
    
    return filtered

def create_training_task(config_name, config, priority=TaskPriority.NORMAL):
    """创建训练任务"""
    task_id = f"{config_name}_{uuid.uuid4().hex[:8]}"
    
    # 构建完整的任务配置
    task_config = {
        'name': config_name,
        'description': config.get('description', ''),
        'model': config.get('model', {}),
        'resources': config.get('resources', {}),
        'training': config.get('training', {}),
        'dataset_config': config.get('dataset_config', {}),
        'transform_config': config.get('transform_config', ''),
        'hyperparameter_config': config.get('hyperparameter_config', ''),
        'enable': True  # 强制启用
    }
    
    task = TrainingTask(
        task_id=task_id,
        name=config_name,
        config=task_config,
        priority=priority
    )
    
    return task

def estimate_total_time(tasks):
    """估算总训练时间"""
    total_hours = 0
    for task in tasks:
        # 基于配置估算时间
        config = task.config
        
        # 基础时间估算 (小时)
        base_time = 4.0  # 基础4小时
        
        # 根据输入尺寸调整
        transform_config = config.get('transform_config', '')
        if '224x224' in transform_config:
            time_multiplier = 1.0
        elif '112x112' in transform_config:
            time_multiplier = 0.7
        elif '80x80' in transform_config:
            time_multiplier = 0.5
        elif '56x56' in transform_config:
            time_multiplier = 0.3
        else:
            time_multiplier = 1.0
        
        # 根据学习率调整 (低学习率可能需要更多epoch)
        hyperparameter_config = config.get('hyperparameter_config', '')
        if 'very_low_lr' in hyperparameter_config:
            lr_multiplier = 1.2
        elif 'low_lr' in hyperparameter_config:
            lr_multiplier = 1.1
        else:
            lr_multiplier = 1.0
        
        estimated_time = base_time * time_multiplier * lr_multiplier
        task.estimated_time_hours = estimated_time
        total_hours += estimated_time
    
    return total_hours

def sort_tasks_by_priority(tasks):
    """按优先级和预估时间排序任务"""
    # 排序策略：
    # 1. 优先级高的先执行
    # 2. 同优先级下，快速任务先执行
    # 3. 同时间下，按名称排序保证一致性
    
    def sort_key(task):
        # 优先级权重 (数值越大优先级越高)
        priority_weight = task.priority.value * 1000
        
        # 时间权重 (时间越短权重越高)
        time_weight = 100 - min(task.estimated_time_hours, 10)
        
        # 名称权重 (保证一致性)
        name_weight = hash(task.name) % 100
        
        return -(priority_weight + time_weight + name_weight)
    
    return sorted(tasks, key=sort_key)

def print_task_plan(tasks, total_hours):
    """打印任务执行计划"""
    print(f"\n{Colors.BOLD}[TASK PLAN]{Colors.END} 任务执行计划")
    print("=" * 100)
    
    print(f"{Colors.CYAN}[SUMMARY]{Colors.END}")
    print(f"  总任务数: {len(tasks)}")
    print(f"  预估总时间: {total_hours:.1f} 小时 ({total_hours/24:.1f} 天)")
    print(f"  平均每任务: {total_hours/len(tasks):.1f} 小时")
    print()
    
    print(f"{Colors.YELLOW}[EXECUTION ORDER]{Colors.END}")
    print(f"{'序号':<4} {'任务名':<35} {'优先级':<8} {'预估时间':<10} {'描述':<40}")
    print("-" * 100)
    
    cumulative_time = 0
    for i, task in enumerate(tasks, 1):
        cumulative_time += task.estimated_time_hours
        
        priority_color = {
            TaskPriority.LOW: Colors.WHITE,
            TaskPriority.NORMAL: Colors.BLUE,
            TaskPriority.HIGH: Colors.YELLOW,
            TaskPriority.URGENT: Colors.RED
        }.get(task.priority, Colors.WHITE)
        
        description = task.config.get('description', '')[:38] + '...' if len(task.config.get('description', '')) > 40 else task.config.get('description', '')
        
        print(f"{i:<4} {task.name:<35} {priority_color}{task.priority.name:<8}{Colors.END} "
              f"{task.estimated_time_hours:<10.1f} {description:<40}")
    
    print("-" * 100)
    print(f"预估完成时间: {cumulative_time:.1f} 小时")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='用户配置任务队列生成器')
    parser.add_argument('--all', action='store_true', help='添加所有用户配置')
    parser.add_argument('--filter', type=str, help='过滤配置 (例如: 224x224, norm, high_lr)')
    parser.add_argument('--priority', choices=['low', 'normal', 'high', 'urgent'], 
                       default='normal', help='任务优先级')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际添加任务')
    parser.add_argument('--start-scheduler', action='store_true', help='添加任务后启动调度器')
    
    args = parser.parse_args()
    
    # 检查参数
    if not args.all and not args.filter:
        print(f"{Colors.RED}[ERROR]{Colors.END} 请指定 --all 或 --filter 参数")
        return
    
    # 设置优先级
    priority_map = {
        'low': TaskPriority.LOW,
        'normal': TaskPriority.NORMAL,
        'high': TaskPriority.HIGH,
        'urgent': TaskPriority.URGENT
    }
    priority = priority_map[args.priority]
    
    print(f"{Colors.BOLD}[QUEUE GENERATOR]{Colors.END} 用户配置任务队列生成器")
    print("=" * 80)
    
    # 加载配置
    print(f"{Colors.BLUE}[LOADING]{Colors.END} 加载用户配置...")
    user_configs = load_user_configs()
    
    if not user_configs:
        print(f"{Colors.RED}[ERROR]{Colors.END} 没有找到用户配置")
        return
    
    print(f"找到 {len(user_configs)} 个用户配置")
    
    # 过滤配置
    if args.filter:
        user_configs = filter_configs(user_configs, args.filter)
        print(f"过滤后剩余 {len(user_configs)} 个配置")
    
    if not user_configs:
        print(f"{Colors.YELLOW}[WARNING]{Colors.END} 过滤后没有配置匹配")
        return
    
    # 创建任务
    print(f"{Colors.BLUE}[CREATING]{Colors.END} 创建训练任务...")
    tasks = []
    for config_name, config in user_configs.items():
        task = create_training_task(config_name, config, priority)
        tasks.append(task)
    
    # 估算时间和排序
    total_hours = estimate_total_time(tasks)
    tasks = sort_tasks_by_priority(tasks)
    
    # 显示计划
    print_task_plan(tasks, total_hours)
    
    if args.dry_run:
        print(f"\n{Colors.YELLOW}[DRY RUN]{Colors.END} 预览模式，未实际添加任务")
        return
    
    # 确认执行
    print(f"\n{Colors.YELLOW}[CONFIRM]{Colors.END} 是否要添加这些任务到队列？")
    print(f"  任务数量: {len(tasks)}")
    print(f"  预估时间: {total_hours:.1f} 小时")
    print(f"  优先级: {priority.name}")
    
    confirm = input("确认添加？ (y/N): ").strip().lower()
    if confirm != 'y':
        print(f"{Colors.YELLOW}[CANCELLED]{Colors.END} 操作已取消")
        return
    
    # 创建调度器并添加任务
    print(f"\n{Colors.BLUE}[ADDING]{Colors.END} 添加任务到队列...")
    scheduler = TaskScheduler(max_gpu_memory=8.0, max_concurrent_tasks=1)
    
    added_count = 0
    for task in tasks:
        try:
            task_id = scheduler.add_task(task)
            added_count += 1
            print(f"  {Colors.GREEN}[ADDED]{Colors.END} {task.name} (ID: {task_id[:8]})")
        except Exception as e:
            print(f"  {Colors.RED}[FAILED]{Colors.END} {task.name}: {e}")
    
    print(f"\n{Colors.GREEN}[SUCCESS]{Colors.END} 成功添加 {added_count}/{len(tasks)} 个任务")
    
    # 启动调度器
    if args.start_scheduler:
        print(f"\n{Colors.BLUE}[STARTING]{Colors.END} 启动任务调度器...")
        scheduler.start_scheduler()
        print(f"{Colors.GREEN}[RUNNING]{Colors.END} 调度器已启动，任务将自动执行")
        print(f"\n使用以下命令查看进度:")
        print(f"  python main_scheduler.py --status")
    else:
        print(f"\n{Colors.BLUE}[INFO]{Colors.END} 任务已添加到队列")
        print(f"使用以下命令启动调度器:")
        print(f"  python main_scheduler.py --start")

if __name__ == "__main__":
    main()
