#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户训练配置生成脚本

根据用户需求生成完整的训练配置：
- 数据采集：original 20张原图 每个图60个样本 
- transform：224*224/112*112/80*80/56*56 with/without Normalize 
- 学习率：0.01~0.00001 
- 优化器：Adam

生成32个配置组合并追加到training_config.yaml文件
"""

import yaml
import os

def generate_user_configs():
    """生成用户指定的训练配置"""
    
    # 配置参数
    sizes = ["224x224", "112x112", "80x80", "56x56"]
    normalizations = ["norm", "no_norm"]
    learning_rates = [
        ("high_lr", "0.01"),
        ("mid_lr", "0.001"), 
        ("low_lr", "0.0001"),
        ("very_low_lr", "0.00001")
    ]
    
    # 模型参数映射 - 根据输入尺寸调整模型复杂度
    model_configs = {
        "224x224": {
            "embed_dim": 192,
            "depths": [3, 4, 18, 3],
            "patch_size": 4,
            "patch_stride": 4,
            "batch_size": 32,
            "estimated_memory": 1.2
        },
        "112x112": {
            "embed_dim": 128,
            "depths": [2, 3, 12, 2],
            "patch_size": 2,
            "patch_stride": 2,
            "batch_size": 48,
            "estimated_memory": 0.8
        },
        "80x80": {
            "embed_dim": 96,
            "depths": [2, 3, 8, 2],
            "patch_size": 2,
            "patch_stride": 2,
            "batch_size": 64,
            "estimated_memory": 0.6
        },
        "56x56": {
            "embed_dim": 64,
            "depths": [2, 2, 6, 2],
            "patch_size": 1,
            "patch_stride": 1,
            "batch_size": 80,
            "estimated_memory": 0.4
        }
    }
    
    configs = []
    
    for size in sizes:
        for norm_type in normalizations:
            for lr_name, lr_value in learning_rates:
                
                # 跳过已经手动添加的224x224配置
                if size == "224x224":
                    continue
                    
                config_name = f"user_{size}_{norm_type}_{lr_name}"
                transform_config = f"{size}_{norm_type}"
                hyperparameter_config = f"user_{lr_name}"
                
                model_config = model_configs[size]
                
                config = {
                    "enable": False,
                    "name": config_name,
                    "description": f"用户配置 - {size}{'带' if norm_type == 'norm' else '不带'}归一化，学习率{lr_value}，20原图×60样本",
                    
                    "training": {
                        "num_epochs": 100,
                        "save_params": True
                    },
                    
                    "dataset_config": {
                        "sampling_strategy": "original_level_sampling",
                        "strategy_parameters": {
                            "target_originals": 20,
                            "samples_per_original": 60,
                            "total_samples": 1200,
                            "cross_subset_sampling": True,
                            "train_ratio": 0.8,
                            "val_ratio": 0.1,
                            "test_ratio": 0.1
                        }
                    },
                    
                    "model": {
                        "embed_dim": model_config["embed_dim"],
                        "depths": model_config["depths"],
                        "mlp_ratio": 2.0,
                        "n_div": 4,
                        "drop_path_rate": 0.2,
                        "layer_scale_init_value": 0,
                        "patch_size": model_config["patch_size"],
                        "patch_stride": model_config["patch_stride"]
                    },
                    
                    "resources": {
                        "estimated_memory": model_config["estimated_memory"],
                        "batch_size": model_config["batch_size"]
                    },
                    
                    "transform_config": transform_config,
                    "hyperparameter_config": hyperparameter_config
                }
                
                configs.append((config_name, config))
    
    return configs

def generate_yaml_content(configs):
    """生成YAML格式的配置内容"""
    content = []
    
    for config_name, config in configs:
        content.append(f"\n  # {config['description']}")
        content.append(f"  {config_name}:")
        
        # 转换为YAML格式
        yaml_str = yaml.dump(config, default_flow_style=False, indent=4, allow_unicode=True)
        # 添加适当的缩进
        yaml_lines = yaml_str.strip().split('\n')
        for line in yaml_lines:
            content.append(f"    {line}")
    
    return '\n'.join(content)

def main():
    """主函数"""
    print("生成用户训练配置...")
    
    # 生成配置
    configs = generate_user_configs()
    
    print(f"生成了 {len(configs)} 个配置:")
    for config_name, _ in configs:
        print(f"  - {config_name}")
    
    # 生成YAML内容
    yaml_content = generate_yaml_content(configs)
    
    # 保存到文件
    output_file = "user_configs_generated.yaml"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(yaml_content)
    
    print(f"\n配置已保存到: {output_file}")
    print("\n请手动将内容追加到 config/training_config.yaml 文件末尾")
    print("\n生成的配置包括:")
    print("- 尺寸: 112x112, 80x80, 56x56 (224x224已手动添加)")
    print("- 归一化: 带归一化 / 不带归一化")
    print("- 学习率: 0.01, 0.001, 0.0001, 0.00001")
    print("- 优化器: Adam")
    print("- 数据: 20张原图 × 60样本 = 1200个样本")

if __name__ == "__main__":
    main()
