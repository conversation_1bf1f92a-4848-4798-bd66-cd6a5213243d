#!/usr/bin/env python3
"""
测试数据加载功能的脚本
验证original级别采样是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('E:\Proj\pytorch-model-train')

from tools.myscripts.load_data import load_data, get_subset, load_batch
from config.config_loader import Config<PERSON><PERSON><PERSON>

def test_basic_data_loading():
    """测试基本数据加载功能"""
    print("🧪 Testing Basic Data Loading...")
    print("="*60)
    
    try:
        # 加载数据
        print("Loading data from database...")
        data = load_data()
        print(f"✅ Total data loaded: {len(data)}")
        
        # 分割数据
        train_data = get_subset(data, 'train')
        val_data = get_subset(data, 'val')
        test_data = get_subset(data, 'test')
        
        print(f"📊 Data Distribution:")
        print(f"  - Training: {len(train_data)}")
        print(f"  - Validation: {len(val_data)}")
        print(f"  - Test: {len(test_data)}")
        
        return data, train_data, val_data
        
    except Exception as e:
        print(f"❌ Basic data loading failed: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

def test_original_sampling(train_data, val_data):
    """测试original级别采样"""
    print(f"\n🎯 Testing Original-level Sampling...")
    print("="*60)
    
    if train_data is None or val_data is None:
        print("❌ Cannot test original sampling: no data available")
        return False
    
    # 测试配置
    test_configs = [
        {
            'name': 'Small Test',
            'target_originals': 5,
            'samples_per_original': 10,
            'total_samples': 50
        },
        {
            'name': 'Medium Test',
            'target_originals': 10,
            'samples_per_original': 20,
            'total_samples': 200
        }
    ]
    
    for config in test_configs:
        print(f"\n📋 Testing {config['name']}:")
        print(f"  - Target originals: {config['target_originals']}")
        print(f"  - Samples per original: {config['samples_per_original']}")
        print(f"  - Expected total: {config['total_samples']}")
        
        try:
            # 测试训练数据采样
            train_result = load_batch(
                train_data,
                use_fixed_sampling=True,
                original_sampling_config=config
            )
            
            if len(train_result) == 3:
                train_batch, train_mapping, train_sampler_info = train_result
            else:
                train_batch, train_mapping = train_result
                train_sampler_info = None
            
            print(f"  ✅ Training sampling successful:")
            print(f"     - Actual samples: {len(train_batch)}")
            print(f"     - Originals used: {len(train_mapping)}")
            
            # 测试验证数据采样
            val_config = {
                'target_originals': max(2, config['target_originals'] // 2),
                'samples_per_original': max(5, config['samples_per_original'] // 2),
                'total_samples': 0  # 会自动计算
            }
            val_config['total_samples'] = val_config['target_originals'] * val_config['samples_per_original']
            
            val_result = load_batch(
                val_data,
                use_fixed_sampling=True,
                original_sampling_config=val_config
            )
            
            if len(val_result) == 3:
                val_batch, val_mapping, _ = val_result
            else:
                val_batch, val_mapping = val_result
            
            print(f"  ✅ Validation sampling successful:")
            print(f"     - Actual samples: {len(val_batch)}")
            print(f"     - Originals used: {len(val_mapping)}")
            
        except Exception as e:
            print(f"  ❌ Sampling failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

def test_config_integration():
    """测试配置集成"""
    print(f"\n🔧 Testing Configuration Integration...")
    print("="*60)
    
    try:
        # 加载配置
        config_loader = ConfigLoader()
        enabled_configs = config_loader.get_enabled_training_configs()
        
        print(f"✅ Found {len(enabled_configs)} enabled configuration(s)")
        
        for config in enabled_configs:
            print(f"\n📋 Testing config: {config['name']}")
            
            # 检查original采样配置
            original_sampling = config.get('original_sampling_config')
            if original_sampling:
                print(f"  - Original sampling config found:")
                print(f"    Target originals: {original_sampling['target_originals']}")
                print(f"    Samples per original: {original_sampling['samples_per_original']}")
                print(f"    Total samples: {original_sampling['total_samples']}")
            else:
                print(f"  - No original sampling config")
            
            # 检查训练配置
            training_config = config.get('training_config', {})
            print(f"  - Training epochs: {training_config.get('num_epochs', 'N/A')}")
            
            # 检查资源配置
            resources = config.get('resources', {})
            print(f"  - Batch size: {resources.get('batch_size', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Starting Data Loading Tests...")
    print("="*80)
    
    # 测试基本数据加载
    data, train_data, val_data = test_basic_data_loading()
    
    if data is None:
        print("\n❌ Basic data loading failed. Cannot proceed with further tests.")
        return False
    
    # 测试original采样
    sampling_success = test_original_sampling(train_data, val_data)
    
    # 测试配置集成
    config_success = test_config_integration()
    
    # 总结
    print(f"\n" + "="*80)
    print("📊 TEST RESULTS SUMMARY")
    print("="*80)
    print(f"✅ Basic Data Loading: {'PASS' if data is not None else 'FAIL'}")
    print(f"✅ Original Sampling: {'PASS' if sampling_success else 'FAIL'}")
    print(f"✅ Configuration Integration: {'PASS' if config_success else 'FAIL'}")
    
    all_passed = data is not None and sampling_success and config_success
    
    if all_passed:
        print(f"\n🎉 All tests passed! The system is ready for training.")
        print(f"\n💡 Next steps:")
        print(f"  1. Run: python main.py --sequential --max_memory 8.0")
        print(f"  2. Monitor the training process")
        print(f"  3. Check results in the results/ directory")
    else:
        print(f"\n❌ Some tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
