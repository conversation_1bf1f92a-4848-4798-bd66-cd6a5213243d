import torch
from torch.utils.data import Dataset, DataLoader
from abc import ABC, abstractmethod
import numpy as np

class BaseDataLoader(ABC):
    """
    基础数据加载器，提供统一的接口
    
    负责数据的加载、预处理、批处理和数据增强
    所有具体的数据加载器都应该继承这个类并实现抽象方法
    """

    '''
    所有数据和标签采用字典形式存储，如果是多级模型需要多种数据进行不同的输入，
    则可以用关键字存储不同的数据，单类数据也需要一个关键字来表示
    '''

    data = {} # 数据
    labels = {} # 数据对应标签

    @abstractmethod
    def __init__(self, **kwargs):
        """
        初始化数据加载器

        Args:
            **kwargs: 其他初始化参数
        """
        pass
    @abstractmethod
    def load_data(self, data_path, **kwargs):
        """
        加载原始数据
        
        Args:
            data_path (str): 数据文件或目录路径
            **kwargs: 其他加载参数
            
        Returns:
            任意: 加载的原始数据
        """
        pass
    
    @abstractmethod
    def preprocess_data(self, data, **kwargs):
        """
        预处理数据
        
        Args:
            data: 原始数据
            **kwargs: 预处理参数
            
        Returns:
            任意: 预处理后的数据
        """
        pass
    
    @abstractmethod
    def create_dataset(self, data, **kwargs):
        """
        创建数据集对象
        
        Args:
            data: 预处理后的数据
            **kwargs: 数据集创建参数
            
        Returns:
            Dataset: PyTorch数据集对象
        """
        pass
    
    def create_dataloader(self, dataset, batch_size=32, shuffle=True, num_workers=4, **kwargs):
        """
        创建数据加载器
        
        Args:
            dataset (Dataset): 数据集对象
            batch_size (int): 批大小
            shuffle (bool): 是否打乱数据
            num_workers (int): 数据加载线程数
            **kwargs: 其他参数
            
        Returns:
            DataLoader: PyTorch数据加载器
        """
        return DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            **kwargs
        )
    
    def split_train_val_test(self, dataset, val_ratio=0.1, test_ratio=0.1, random_seed=42):
        """
        将数据集分割为训练集、验证集和测试集
        
        Args:
            dataset (Dataset): 完整数据集
            val_ratio (float): 验证集比例
            test_ratio (float): 测试集比例
            random_seed (int): 随机种子
            
        Returns:
            tuple: (训练集, 验证集, 测试集)
        """
        # 设置随机种子以确保可重复性
        np.random.seed(random_seed)
        torch.manual_seed(random_seed)
        
        dataset_size = len(dataset)
        test_size = int(dataset_size * test_ratio)
        val_size = int(dataset_size * val_ratio)
        train_size = dataset_size - val_size - test_size
        
        # 使用random_split函数分割数据集
        train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size, test_size]
        )
        
        return train_dataset, val_dataset, test_dataset
    
    def get_data_loaders(self, data_path, batch_size=32, val_ratio=0.1, test_ratio=0.1, **kwargs):
        """
        一站式获取训练、验证和测试数据加载器
        
        Args:
            data_path (str): 数据路径
            batch_size (int): 批大小
            val_ratio (float): 验证集比例
            test_ratio (float): 测试集比例
            **kwargs: 其他参数
            
        Returns:
            tuple: (训练数据加载器, 验证数据加载器, 测试数据加载器)
        """
        # 加载和预处理数据
        raw_data = self.load_data(data_path, **kwargs)
        processed_data = self.preprocess_data(raw_data, **kwargs)
        
        # 创建数据集
        dataset = self.create_dataset(processed_data, **kwargs)
        
        # 分割数据集
        train_dataset, val_dataset, test_dataset = self.split_train_val_test(
            dataset, val_ratio, test_ratio
        )
        
        # 创建数据加载器
        train_loader = self.create_dataloader(
            train_dataset, batch_size=batch_size, shuffle=True, **kwargs
        )
        val_loader = self.create_dataloader(
            val_dataset, batch_size=batch_size, shuffle=False, **kwargs
        )
        test_loader = self.create_dataloader(
            test_dataset, batch_size=batch_size, shuffle=False, **kwargs
        )
        
        return train_loader, val_loader, test_loader