"""
配置文件加载器
负责从YAML配置文件中加载训练配置
"""

import os
import yaml
import torchvision.transforms as transforms
from typing import Dict, List, Any

class ConfigLoader:
    """配置加载器类"""

    def __init__(self, config_path: str = None):
        """
        初始化配置加载器

        参数:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        if config_path is None:
            # 获取当前文件所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, "training_config.yaml")

        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            print(f"✅ Configuration loaded from: {self.config_path}")
            return config
        except FileNotFoundError:
            raise FileNotFoundError(f"❌ Configuration file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"❌ Error parsing YAML configuration: {e}")

    def get_global_settings(self) -> Dict[str, Any]:
        """获取全局设置"""
        return self.config.get('global_settings', {})

    def get_dataset_sampling_strategies(self) -> Dict[str, Any]:
        """获取数据集采样策略配置"""
        return self.config.get('dataset_sampling_strategies', {})

    def get_sampling_strategies(self) -> Dict[str, Any]:
        """获取采样策略详细配置"""
        return self.config.get('sampling_strategies', {})

    def get_sampling_strategy_config(self, strategy_name: str) -> Dict[str, Any]:
        """
        获取指定采样策略的配置

        参数:
            strategy_name: 采样策略名称

        返回:
            采样策略配置字典
        """
        strategies = self.get_sampling_strategies()
        if strategy_name not in strategies:
            raise ValueError(f"❌ Sampling strategy '{strategy_name}' not found")
        return strategies[strategy_name]

    # 保持向后兼容性
    def get_data_sampling_config(self) -> Dict[str, int]:
        """获取数据采样配置（向后兼容）"""
        strategies = self.get_dataset_sampling_strategies()
        return strategies.get('size_based', {})

    def get_original_sampling_config(self) -> Dict[str, Any]:
        """获取Original级别采样配置（向后兼容）"""
        strategies = self.get_sampling_strategies()
        original_strategy = strategies.get('original_level_sampling', {})
        if original_strategy:
            return original_strategy.get('parameters', {
                'target_originals': 40,
                'samples_per_original': 60,
                'total_samples': 2400
            })
        return {
            'target_originals': 40,
            'samples_per_original': 60,
            'total_samples': 2400
        }

    def get_hyperparameter_config(self, name: str) -> Dict[str, Any]:
        """
        获取指定的超参数配置

        参数:
            name: 超参数配置名称

        返回:
            超参数配置字典
        """
        hyperparams = self.config.get('hyperparameters', {})
        if name not in hyperparams:
            raise ValueError(f"❌ Hyperparameter configuration '{name}' not found")
        return hyperparams[name]

    def get_transform_config(self, name: str) -> Dict[str, Any]:
        """
        获取指定的数据变换配置

        参数:
            name: 变换配置名称

        返回:
            变换配置字典
        """
        transforms_config = self.config.get('transforms', {})
        if name not in transforms_config:
            raise ValueError(f"❌ Transform configuration '{name}' not found")
        return transforms_config[name]

    def create_transform(self, name: str) -> transforms.Compose:
        """
        根据配置创建数据变换

        参数:
            name: 变换配置名称

        返回:
            PyTorch transforms.Compose 对象
        """
        transform_config = self.get_transform_config(name)

        transform_list = []

        # 添加Resize
        input_size = transform_config['input_size']
        transform_list.append(transforms.Resize(tuple(input_size)))

        # 添加ToTensor
        transform_list.append(transforms.ToTensor())

        # 添加归一化（如果启用）
        if transform_config.get('normalize', False):
            mean = transform_config.get('mean', [0.485, 0.456, 0.406])
            std = transform_config.get('std', [0.229, 0.224, 0.225])
            transform_list.append(transforms.Normalize(mean=mean, std=std))

        return transforms.Compose(transform_list)

    def get_enabled_training_configs(self) -> List[Dict[str, Any]]:
        """
        获取所有启用的训练配置

        返回:
            启用的训练配置列表
        """
        training_configs = self.config.get('training_configs', {})
        enabled_configs = []

        for config_name, config_data in training_configs.items():
            if config_data.get('enable', False):
                # 合并配置数据
                merged_config = {
                    'config_name': config_name,
                    **config_data
                }

                # 添加引用的配置
                transform_name = config_data.get('transform_config')
                hyperparam_name = config_data.get('hyperparameter_config')

                if transform_name:
                    merged_config['transform'] = self.create_transform(transform_name)
                    merged_config['transform_config_data'] = self.get_transform_config(transform_name)

                if hyperparam_name:
                    merged_config['hyperparameter_config_data'] = self.get_hyperparameter_config(hyperparam_name)

                # 处理数据集采样策略配置
                if 'dataset_config' in config_data:
                    dataset_config = config_data['dataset_config']
                    strategy_name = dataset_config.get('sampling_strategy')

                    if strategy_name:
                        # 获取基础策略配置
                        base_strategy = self.get_sampling_strategy_config(strategy_name)

                        # 合并策略参数（配置中的参数覆盖默认参数）
                        strategy_params = base_strategy.get('parameters', {}).copy()
                        if 'strategy_parameters' in dataset_config:
                            strategy_params.update(dataset_config['strategy_parameters'])

                        merged_config['sampling_strategy_config'] = {
                            'strategy_name': strategy_name,
                            'strategy_type': base_strategy.get('strategy_type'),
                            'description': base_strategy.get('description'),
                            'parameters': strategy_params
                        }
                    else:
                        # 如果没有指定策略，使用默认的original级别采样
                        merged_config['sampling_strategy_config'] = {
                            'strategy_name': 'original_level_sampling',
                            'strategy_type': 'original_based',
                            'parameters': self.get_original_sampling_config()
                        }
                else:
                    # 向后兼容：处理original采样配置
                    if 'original_sampling' in config_data:
                        merged_config['original_sampling_config'] = config_data['original_sampling']
                        # 同时创建新格式的采样策略配置
                        merged_config['sampling_strategy_config'] = {
                            'strategy_name': 'original_level_sampling',
                            'strategy_type': 'original_based',
                            'parameters': config_data['original_sampling']
                        }
                    else:
                        # 使用全局original采样设置
                        merged_config['original_sampling_config'] = self.get_original_sampling_config()
                        merged_config['sampling_strategy_config'] = {
                            'strategy_name': 'original_level_sampling',
                            'strategy_type': 'original_based',
                            'parameters': self.get_original_sampling_config()
                        }

                # 处理训练参数（如epoch数）
                if 'training' in config_data:
                    merged_config['training_config'] = config_data['training']
                else:
                    # 使用全局设置中的默认epoch数
                    merged_config['training_config'] = {
                        'num_epochs': self.get_global_settings().get('num_epochs', 50)
                    }

                enabled_configs.append(merged_config)

        return enabled_configs

    def get_data_sample_size(self, input_size: int, config_name: str = None, sampling_strategy_config: Dict[str, Any] = None) -> int:
        """
        根据输入尺寸、配置名称或采样策略配置获取数据采样大小

        参数:
            input_size: 输入图像尺寸
            config_name: 配置名称（用于检测采样策略）
            sampling_strategy_config: 采样策略配置

        返回:
            采样大小
        """
        # 优先使用采样策略配置
        if sampling_strategy_config:
            strategy_type = sampling_strategy_config.get('strategy_type')
            parameters = sampling_strategy_config.get('parameters', {})

            if strategy_type == 'original_based':
                return parameters.get('total_samples', 2400)
            elif strategy_type == 'random':
                return parameters.get('sample_size', 4500)
            elif strategy_type == 'balanced':
                return parameters.get('total_samples', 4500)
            elif strategy_type == 'stratified':
                return parameters.get('total_samples', 4500)
            elif strategy_type == 'temporal':
                return parameters.get('total_samples', 3000)

        # 向后兼容：检查配置名称
        if config_name:
            if 'original' in config_name.lower() or 'balanced' in config_name.lower():
                strategies = self.get_dataset_sampling_strategies()
                return strategies.get('original_balanced', 2400)
            elif 'random' in config_name.lower():
                return 4500
            elif 'stratified' in config_name.lower():
                return 4500

        # 基于尺寸的默认映射
        sampling_config = self.get_data_sampling_config()
        size_key = f"{input_size}x{input_size}"
        return sampling_config.get(size_key, 4500)  # 默认4500

    def print_config_summary(self):
        """打印配置摘要"""
        print("\n" + "="*80)
        print("📋 TRAINING CONFIGURATION SUMMARY")
        print("="*80)

        # 全局设置
        global_settings = self.get_global_settings()
        print(f"🌐 Global Settings:")
        print(f"   - Max GPU Memory: {global_settings.get('max_gpu_memory', 8.0)}GB")
        print(f"   - Total Images: {global_settings.get('total_images', 190000):,}")
        print(f"   - Epochs: {global_settings.get('num_epochs', 50)}")

        # 数据集采样策略
        dataset_strategies = self.get_dataset_sampling_strategies()
        print(f"\n📊 Dataset Sampling Strategies:")
        if 'size_based' in dataset_strategies:
            size_based = dataset_strategies['size_based']
            print(f"   Size-based sampling:")
            for size, count in size_based.items():
                print(f"     - {size}: {count:,} images")
        if 'original_balanced' in dataset_strategies:
            print(f"   Original-balanced sampling: {dataset_strategies['original_balanced']:,} images")

        # 采样策略详细配置
        sampling_strategies = self.get_sampling_strategies()
        print(f"\n🎯 Available Sampling Strategies ({len(sampling_strategies)}):")
        for strategy_name, strategy_config in sampling_strategies.items():
            strategy_type = strategy_config.get('strategy_type', 'unknown')
            description = strategy_config.get('description', 'No description')
            parameters = strategy_config.get('parameters', {})

            print(f"   - {strategy_name} ({strategy_type})")
            print(f"     Description: {description}")

            # 显示关键参数
            if strategy_type == 'original_based':
                print(f"     Parameters: {parameters.get('target_originals', 'N/A')} originals × {parameters.get('samples_per_original', 'N/A')} samples = {parameters.get('total_samples', 'N/A'):,} total")
            elif 'total_samples' in parameters:
                print(f"     Parameters: {parameters['total_samples']:,} total samples")
            elif 'sample_size' in parameters:
                print(f"     Parameters: {parameters['sample_size']:,} sample size")

        # 启用的配置
        enabled_configs = self.get_enabled_training_configs()
        print(f"\n🚀 Enabled Training Configurations ({len(enabled_configs)}):")

        for i, config in enumerate(enabled_configs, 1):
            transform_data = config.get('transform_config_data', {})
            input_size = transform_data.get('input_size', [0, 0])[0]
            normalize = "✅" if transform_data.get('normalize', False) else "❌"

            # 获取训练配置
            training_config = config.get('training_config', {})
            num_epochs = training_config.get('num_epochs', 50)

            # 获取采样策略配置
            sampling_strategy = config.get('sampling_strategy_config', {})

            print(f"   {i}. {config['name']}")
            print(f"      - Description: {config.get('description', 'N/A')}")
            print(f"      - Input Size: {input_size}x{input_size}")
            print(f"      - Normalization: {normalize}")
            print(f"      - Epochs: {num_epochs}")
            print(f"      - Batch Size: {config['resources']['batch_size']}")
            print(f"      - Est. Memory: {config['resources']['estimated_memory']}GB")
            print(f"      - Hyperparams: {config['hyperparameter_config']}")

            # 显示采样策略信息
            if sampling_strategy:
                strategy_name = sampling_strategy.get('strategy_name', 'unknown')
                strategy_type = sampling_strategy.get('strategy_type', 'unknown')
                parameters = sampling_strategy.get('parameters', {})

                print(f"      - Sampling Strategy: {strategy_name} ({strategy_type})")

                if strategy_type == 'original_based':
                    target_originals = parameters.get('target_originals', 'N/A')
                    samples_per_original = parameters.get('samples_per_original', 'N/A')
                    total_samples = parameters.get('total_samples', 'N/A')
                    print(f"        {target_originals} originals × {samples_per_original} samples = {total_samples:,} total")
                elif 'total_samples' in parameters:
                    print(f"        Total samples: {parameters['total_samples']:,}")
                elif 'sample_size' in parameters:
                    print(f"        Sample size: {parameters['sample_size']:,}")

        print("="*80)

def load_training_config(config_path: str = None) -> ConfigLoader:
    """
    便捷函数：加载训练配置

    参数:
        config_path: 配置文件路径

    返回:
        ConfigLoader 实例
    """
    return ConfigLoader(config_path)

# 示例用法
if __name__ == "__main__":
    # 加载配置
    config_loader = load_training_config()

    # 打印配置摘要
    config_loader.print_config_summary()

    # 获取启用的配置
    enabled_configs = config_loader.get_enabled_training_configs()
    print(f"\nFound {len(enabled_configs)} enabled configurations")
