'''
求快的完成模型混合，不做多态适应
多级模型联合使用
'''

from ultralytics import YOLO
import ultralytics.engine
import ultralytics.engine.results # 引入YOLO
from .FasterNet import FasterNet as FasterNet_base # 引入基础FasterNet
from .FasterNet import model as FasterNet_model # 引入处理后的FasterNet
from torch import nn # 引入nn模块
from utils.logger import logger # 引入日志记录器
from PIL.Image import Image # 引入Image类
from typing import List, Literal # 引入List类
import ultralytics
import torch
# 引入Transform
from torchvision import transforms
import pandas as pd

class SimpleMixedModel(nn.Module): # 定义MixedModel类，继承自nn.Module
    # 模型类里只设计结构，不设计训练和实现
    def __init__(self, device='cpu', mode=Literal['train', 'eval'], confidence = 0.5 ,**kwargs): # 初始化函数
        '''
        初始化函数
        **kwargs: 需要的参数都用关键字提供
        '''
        super(SimpleMixedModel, self).__init__() # 调用父类的初始化函数
        self.device = device # 将device参数赋值给self.device
        self.__name__ = 'SimpleMixedModel' # 将模型名称赋值给self.__name__
        self.mode = mode # 将mode参数赋值给self.mode
        self.confidence = confidence # 将confidence参数赋值给self.confidence
        self.FasterNet_params = kwargs.get('FasterNet_params', {}) # 将FasterNet_params参数赋值给self.FasterNet_params，如果没有则默认为空字典
        # 初始化YOLO模型
        self.yolo = YOLO('E:\Proj\pytorch-model-train\weights\SeedVision_cls\yolo11s.pt') # 初始化YOLO模型
        self.yolo.to(self.device) # 将YOLO模型移动到指定设备上
        self.yolo.eval() # 将YOLO模型设置为评估模式
        # 初始化FasterNet模型
        self.FasterNet = FasterNet_model.FasterNet(num_classes=2, device=self.device) # 初始化FasterNet模型
        self.transform =transforms.Compose([
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(
                    mean=[0.485, 0.456, 0.406],
                    std=[0.229, 0.224, 0.225]
                )
            ])
    
    def _process_yoloOutput(self, yolo_output: ultralytics.engine.results.Results, image: Image):
        """
        处理YOLO的输出，返回裁剪后的目标区域和对应的检测信息
        
        Args:
            yolo_output: YOLO的输出结果
            
        Returns:
            List[Tuple[Tensor, dict]]: 包含裁剪图像和检测信息的列表
                - Tensor: 裁剪后的图像 (C, H, W)
                - dict: 包含检测框坐标和置信度等信息
        """
        cropped_tensors = []
        
        # 遍历YOLO检测结果
        for detection in yolo_output[0].boxes:
            # 获取置信度
            confidence = detection.conf.item()
            if confidence < 0.5: # 置信度阈值
                logger.warning(f"检测框坐标：{x1, y1, x2, y2}，置信度：{confidence} 低于阈值 ，判断为非油菜籽，舍弃该框")
                continue

            # 获取检测框坐标
            x1, y1, x2, y2 = detection.xyxy[0].numpy()

            # 获取类别ID
            class_id = detection.cls.item()

            # 裁剪目标区域
            cropped = image.crop((x1, y1, x2, y2))

            cropped_tensor = self.transform(cropped).unsqueeze(0)

            cropped_tensor = cropped_tensor.to(self.device)

            # # 转换为Tensor并调整通道顺序 (H, W, C) -> (C, H, W)
            # cropped_tensor = torch.from_numpy(cropped).permute(2, 0, 1).float() / 255.0
            
            # # 保存检测信息
            # detection_info = {
            #     'box': [x1, y1, x2, y2],
            #     'confidence': confidence,
            #     # 'class_id': class_id # 无用，只有一个标签
            # }
            
            # processed_results.append((cropped_tensor, detection_info))
            cropped_tensors.append(cropped_tensor)
        
        return cropped_tensors

    def forward_no_1(self, x:Image):
        '''
        前向传播的拆分
        首先用yolo进行图像识别得到若干个油菜籽对象
        将油菜籽对象赋予数值标签
        按批次大小进行分组，多出来的部分存入cache等待下一个batch
        将输出结果转化为可以输入到FasterNet的形式
        
        '''
        # 前向传播第一步：YOLO模型
        yolo_output:List[ultralytics.engine.results.Results] = self.yolo.predict(x) # 调用YOLO模型进行前向传播，得到YOLO模型的输出
        # 先处理输出，得到一个列表，里面是每一个检测到对象的张量
        cropped_tensors = self._process_yoloOutput(yolo_output, x) # 处理YOLO的输出，得到裁剪后的目标区域和对应的检测信息
        
        return cropped_tensors
    
    def forward_no_2(self, x:List[torch.Tensor]):
        '''
        前向传播的拆分
        对每个对象单独预测一遍
        '''
        # 前向传播第二步：FasterNet模型
        for index, tensor in enumerate(x): # 遍历每个目标区域
            tensor = tensor.to(self.device) # 将目标区域移动到指定设备上
            x[index] = self.FasterNet(tensor) # 调用FasterNet模型进行前向传播，得到FasterNet模型的输出

        return x
    
    def forward(self, x:Image):
        '''
        前向传播
        '''
        x = self.forward_no_1(x) # 前向传播第一步：YOLO模型
        x = self.forward_no_2(x) # 前向传播第二步：FasterNet模型
        # x.cpu().numpy() # 将输出结果移动到CPU上，并转换为numpy数组
        
        return x