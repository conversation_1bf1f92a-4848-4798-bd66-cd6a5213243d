#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Original级别评估功能

这个脚本测试新实现的Original级别评估功能，验证：
1. 同一original下所有样本的预测值均值计算
2. Original级别的R²、RMSE、MAE、RPD指标计算
3. 与样本级别评估的对比
"""

import sys
import os
import numpy as np
import torch

# 添加路径
sys.path.append('E:\Proj\pytorch-model-train')
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_original_evaluation():
    """测试Original级别评估功能"""
    print("🧪 Testing Original-level Evaluation Function")
    print("=" * 60)

    # 模拟数据：3个original，每个original有不同数量的样本
    # Original A: 3个样本
    # Original B: 4个样本
    # Original C: 2个样本

    # 模拟预测值 (N, 2) - [oil, protein]
    predictions = np.array([
        # Original A的3个样本
        [20.1, 35.2],  # 样本1
        [19.8, 34.9],  # 样本2
        [20.3, 35.1],  # 样本3
        # Original B的4个样本
        [25.2, 40.1],  # 样本1
        [24.8, 39.9],  # 样本2
        [25.1, 40.2],  # 样本3
        [25.0, 40.0],  # 样本4
        # Original C的2个样本
        [18.9, 32.1],  # 样本1
        [19.1, 31.9],  # 样本2
    ])

    # 模拟标签值 (N, 2) - 同一original的标签应该相同
    labels = np.array([
        # Original A的标签（都相同）
        [20.0, 35.0],
        [20.0, 35.0],
        [20.0, 35.0],
        # Original B的标签（都相同）
        [25.0, 40.0],
        [25.0, 40.0],
        [25.0, 40.0],
        [25.0, 40.0],
        # Original C的标签（都相同）
        [19.0, 32.0],
        [19.0, 32.0],
    ])

    # Original名称
    original_names = [
        'original_A', 'original_A', 'original_A',
        'original_B', 'original_B', 'original_B', 'original_B',
        'original_C', 'original_C'
    ]

    print(f"📊 Test Data Summary:")
    print(f"  - Total samples: {len(predictions)}")
    print(f"  - Original A: 3 samples")
    print(f"  - Original B: 4 samples")
    print(f"  - Original C: 2 samples")
    print()

    # 导入评估函数
    try:
        from tools.training.validate import evaluate_original_level
        print("✅ Successfully imported evaluate_original_level function")
    except ImportError as e:
        print(f"❌ Failed to import evaluate_original_level: {e}")
        return False

    # 执行Original级别评估
    try:
        original_metrics, original_results = evaluate_original_level(
            predictions, labels, original_names
        )
        print("✅ Original-level evaluation completed successfully")
        print()
    except Exception as e:
        print(f"❌ Original-level evaluation failed: {e}")
        return False

    # 显示结果
    print("🎯 Original-level Evaluation Results:")
    print("-" * 40)

    # 显示每个original的详细结果
    print("📋 Per-Original Results:")
    for original_name, result in original_results.items():
        mean_pred = result['mean_prediction']
        label = result['label']
        sample_count = result['sample_count']
        oil_std = result['oil_std']
        protein_std = result['protein_std']

        print(f"  {original_name} ({sample_count} samples):")
        print(f"    Mean Prediction: Oil={mean_pred[0]:.3f}, Protein={mean_pred[1]:.3f}")
        print(f"    True Label:      Oil={label[0]:.3f}, Protein={label[1]:.3f}")
        print(f"    Prediction Std:  Oil={oil_std:.3f}, Protein={protein_std:.3f}")
        print()

    # 显示整体指标
    print("📈 Overall Original-level Metrics:")
    oil_metrics = original_metrics['oil']
    protein_metrics = original_metrics['protein']

    print(f"  Oil Content:")
    print(f"    R²:   {oil_metrics['R2']:.4f}")
    print(f"    RMSE: {oil_metrics['RMSE']:.4f}")
    print(f"    MAE:  {oil_metrics['MAE']:.4f}")
    print(f"    RPD:  {oil_metrics['RPD']:.4f}")
    print()

    print(f"  Protein Content:")
    print(f"    R²:   {protein_metrics['R2']:.4f}")
    print(f"    RMSE: {protein_metrics['RMSE']:.4f}")
    print(f"    MAE:  {protein_metrics['MAE']:.4f}")
    print(f"    RPD:  {protein_metrics['RPD']:.4f}")
    print()

    # 计算样本级别评估进行对比
    print("🔍 Sample-level vs Original-level Comparison:")
    print("-" * 50)

    # 样本级别R²计算
    from tools.training.validate import calculate_r2

    pred_tensor = torch.tensor(predictions, dtype=torch.float32)
    label_tensor = torch.tensor(labels, dtype=torch.float32)

    sample_oil_r2 = calculate_r2(label_tensor[:, 0], pred_tensor[:, 0])
    sample_protein_r2 = calculate_r2(label_tensor[:, 1], pred_tensor[:, 1])

    print(f"  Oil R²:")
    print(f"    Sample-level:   {sample_oil_r2:.4f}")
    print(f"    Original-level: {oil_metrics['R2']:.4f}")
    print(f"    Difference:     {abs(sample_oil_r2 - oil_metrics['R2']):.4f}")
    print()

    print(f"  Protein R²:")
    print(f"    Sample-level:   {sample_protein_r2:.4f}")
    print(f"    Original-level: {protein_metrics['R2']:.4f}")
    print(f"    Difference:     {abs(sample_protein_r2 - protein_metrics['R2']):.4f}")
    print()

    # 验证逻辑正确性
    print("✅ Validation Results:")
    print("-" * 30)

    # 验证Original A的均值计算
    original_a_preds = predictions[:3]  # 前3个样本
    expected_mean_a = np.mean(original_a_preds, axis=0)
    actual_mean_a = original_results['original_A']['mean_prediction']

    if np.allclose(expected_mean_a, actual_mean_a, rtol=1e-5):
        print("✅ Original A mean calculation correct")
    else:
        print(f"❌ Original A mean calculation error: expected {expected_mean_a}, got {actual_mean_a}")

    # 验证Original B的均值计算
    original_b_preds = predictions[3:7]  # 中间4个样本
    expected_mean_b = np.mean(original_b_preds, axis=0)
    actual_mean_b = original_results['original_B']['mean_prediction']

    if np.allclose(expected_mean_b, actual_mean_b, rtol=1e-5):
        print("✅ Original B mean calculation correct")
    else:
        print(f"❌ Original B mean calculation error: expected {expected_mean_b}, got {actual_mean_b}")

    # 验证Original C的均值计算
    original_c_preds = predictions[7:]  # 最后2个样本
    expected_mean_c = np.mean(original_c_preds, axis=0)
    actual_mean_c = original_results['original_C']['mean_prediction']

    if np.allclose(expected_mean_c, actual_mean_c, rtol=1e-5):
        print("✅ Original C mean calculation correct")
    else:
        print(f"❌ Original C mean calculation error: expected {expected_mean_c}, got {actual_mean_c}")

    print()
    print("🎉 Original-level evaluation test completed successfully!")
    return True

def main():
    """主函数"""
    print("🚀 Starting Original-level Evaluation Test")
    print("=" * 60)

    success = test_original_evaluation()

    if success:
        print("\n✅ All tests passed! Original-level evaluation is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")

    return success

if __name__ == "__main__":
    main()
