"""
分析每个original_image的记录数量分布
生成统计信息和可视化图表
"""

import sys
sys.path.append('E:\Proj\pytorch-model-train')

from utils.db_utils import mysql_connecter
import matplotlib.pyplot as plt
import numpy as np
from collections import defaultdict, Counter
import seaborn as sns
import pandas as pd

# 设置matplotlib支持中文显示（如果需要）
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial']
plt.rcParams['axes.unicode_minus'] = False

# 设置图表样式
plt.style.use('default')
sns.set_palette("husl")

def load_original_distribution():
    """从数据库加载original_image分布数据"""
    print("📥 Loading original_image distribution from database...")
    
    db_name = 'SeedVision'
    table_name = 'yolotrain2'
    
    with mysql_connecter.cursor() as cursor:
        cursor.execute(f"USE {db_name}")
        
        # 查询每个original_image的记录数量
        query = """
        SELECT original_image, type, COUNT(*) as count 
        FROM {} 
        GROUP BY original_image, type 
        ORDER BY original_image, type
        """.format(table_name)
        
        cursor.execute(query)
        results = cursor.fetchall()
    
    print(f"✅ Loaded {len(results)} original_image-type combinations")
    return results

def analyze_distribution(results):
    """分析original_image分布"""
    print("\n📊 Analyzing Original Image Distribution")
    print("=" * 60)
    
    # 按original_image分组统计
    original_stats = defaultdict(lambda: {'train': 0, 'val': 0, 'test': 0, 'total': 0})
    
    for row in results:
        original_image = row['original_image']
        data_type = row['type']
        count = row['count']
        
        original_stats[original_image][data_type] = count
    
    # 计算每个original的总数
    for original, stats in original_stats.items():
        stats['total'] = stats['train'] + stats['val'] + stats['test']
    
    # 提取总数列表用于统计
    total_counts = [stats['total'] for stats in original_stats.values()]
    train_counts = [stats['train'] for stats in original_stats.values()]
    val_counts = [stats['val'] for stats in original_stats.values()]
    test_counts = [stats['test'] for stats in original_stats.values()]
    
    print(f"📈 Basic Statistics:")
    print(f"  - Total unique original images: {len(original_stats)}")
    print(f"  - Total records: {sum(total_counts):,}")
    
    print(f"\n📊 Records per Original Image:")
    print(f"  - Minimum: {min(total_counts)}")
    print(f"  - Maximum: {max(total_counts)}")
    print(f"  - Mean: {np.mean(total_counts):.2f}")
    print(f"  - Median: {np.median(total_counts):.2f}")
    print(f"  - Standard Deviation: {np.std(total_counts):.2f}")
    
    # 百分位数
    percentiles = [25, 50, 75, 90, 95, 99]
    print(f"\n📈 Percentiles:")
    for p in percentiles:
        value = np.percentile(total_counts, p)
        print(f"  - {p}th percentile: {value:.1f}")
    
    # 按数据类型统计
    print(f"\n📋 By Data Type:")
    print(f"  Train - Min: {min(train_counts)}, Max: {max(train_counts)}, Mean: {np.mean(train_counts):.2f}")
    print(f"  Val   - Min: {min(val_counts)}, Max: {max(val_counts)}, Mean: {np.mean(val_counts):.2f}")
    print(f"  Test  - Min: {min(test_counts)}, Max: {max(test_counts)}, Mean: {np.mean(test_counts):.2f}")
    
    # 显示前10个和后10个original
    sorted_originals = sorted(original_stats.items(), key=lambda x: x[1]['total'], reverse=True)
    
    print(f"\n🔝 Top 10 Original Images (Most Records):")
    for i, (original, stats) in enumerate(sorted_originals[:10]):
        print(f"  {i+1:2d}. {original}: {stats['total']} (train:{stats['train']}, val:{stats['val']}, test:{stats['test']})")
    
    print(f"\n🔻 Bottom 10 Original Images (Least Records):")
    for i, (original, stats) in enumerate(sorted_originals[-10:]):
        print(f"  {i+1:2d}. {original}: {stats['total']} (train:{stats['train']}, val:{stats['val']}, test:{stats['test']})")
    
    return original_stats, total_counts, train_counts, val_counts, test_counts

def create_visualizations(original_stats, total_counts, train_counts, val_counts, test_counts):
    """创建可视化图表"""
    print(f"\n🎨 Creating Visualizations...")
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Original Image Distribution Analysis', fontsize=16, fontweight='bold')
    
    # 1. 总记录数分布直方图
    axes[0, 0].hist(total_counts, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('Distribution of Total Records per Original Image')
    axes[0, 0].set_xlabel('Number of Records')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加统计信息
    mean_val = np.mean(total_counts)
    median_val = np.median(total_counts)
    axes[0, 0].axvline(mean_val, color='red', linestyle='--', label=f'Mean: {mean_val:.1f}')
    axes[0, 0].axvline(median_val, color='green', linestyle='--', label=f'Median: {median_val:.1f}')
    axes[0, 0].legend()
    
    # 2. 箱线图
    data_for_box = [train_counts, val_counts, test_counts, total_counts]
    labels = ['Train', 'Val', 'Test', 'Total']
    axes[0, 1].boxplot(data_for_box, labels=labels)
    axes[0, 1].set_title('Box Plot of Records per Original Image')
    axes[0, 1].set_ylabel('Number of Records')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 累积分布函数
    sorted_counts = np.sort(total_counts)
    cumulative = np.arange(1, len(sorted_counts) + 1) / len(sorted_counts)
    axes[0, 2].plot(sorted_counts, cumulative, marker='o', markersize=2)
    axes[0, 2].set_title('Cumulative Distribution Function')
    axes[0, 2].set_xlabel('Number of Records')
    axes[0, 2].set_ylabel('Cumulative Probability')
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. 记录数量分组统计
    bins = [0, 50, 60, 70, 80, 90, 100, float('inf')]
    bin_labels = ['0-49', '50-59', '60-69', '70-79', '80-89', '90-99', '100+']
    bin_counts = []
    
    for i in range(len(bins)-1):
        count = sum(1 for x in total_counts if bins[i] <= x < bins[i+1])
        bin_counts.append(count)
    
    axes[1, 0].bar(bin_labels, bin_counts, alpha=0.7, color='lightcoral')
    axes[1, 0].set_title('Distribution by Record Count Ranges')
    axes[1, 0].set_xlabel('Record Count Range')
    axes[1, 0].set_ylabel('Number of Original Images')
    axes[1, 0].tick_params(axis='x', rotation=45)
    
    # 在柱子上显示数值
    for i, count in enumerate(bin_counts):
        axes[1, 0].text(i, count + 0.5, str(count), ha='center', va='bottom')
    
    # 5. 数据类型比例堆叠图
    # 选择前20个original进行展示
    sorted_originals = sorted(original_stats.items(), key=lambda x: x[1]['total'], reverse=True)
    top_20 = sorted_originals[:20]
    
    original_names = [item[0][:10] + '...' if len(item[0]) > 10 else item[0] for item, _ in top_20]  # 截断长名称
    train_data = [stats['train'] for _, stats in top_20]
    val_data = [stats['val'] for _, stats in top_20]
    test_data = [stats['test'] for _, stats in top_20]
    
    x_pos = np.arange(len(original_names))
    axes[1, 1].bar(x_pos, train_data, label='Train', alpha=0.8)
    axes[1, 1].bar(x_pos, val_data, bottom=train_data, label='Val', alpha=0.8)
    axes[1, 1].bar(x_pos, test_data, bottom=np.array(train_data) + np.array(val_data), label='Test', alpha=0.8)
    
    axes[1, 1].set_title('Top 20 Original Images - Data Type Distribution')
    axes[1, 1].set_xlabel('Original Image')
    axes[1, 1].set_ylabel('Number of Records')
    axes[1, 1].legend()
    axes[1, 1].tick_params(axis='x', rotation=45)
    
    # 6. 统计摘要表格
    axes[1, 2].axis('off')
    
    # 创建统计摘要
    stats_data = [
        ['Metric', 'Value'],
        ['Total Original Images', f"{len(original_stats):,}"],
        ['Total Records', f"{sum(total_counts):,}"],
        ['Min Records/Original', f"{min(total_counts)}"],
        ['Max Records/Original', f"{max(total_counts)}"],
        ['Mean Records/Original', f"{np.mean(total_counts):.2f}"],
        ['Median Records/Original', f"{np.median(total_counts):.2f}"],
        ['Std Dev', f"{np.std(total_counts):.2f}"],
        ['', ''],
        ['Train Records', f"{sum(train_counts):,}"],
        ['Val Records', f"{sum(val_counts):,}"],
        ['Test Records', f"{sum(test_counts):,}"],
    ]
    
    table = axes[1, 2].table(cellText=stats_data[1:], colLabels=stats_data[0], 
                            cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1, 2)
    axes[1, 2].set_title('Summary Statistics')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    output_path = "original_image_distribution_analysis.png"
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"📊 Visualization saved to: {output_path}")
    
    # 显示图表
    plt.show()

def create_detailed_distribution_chart(total_counts):
    """创建详细的分布图表"""
    print(f"\n📈 Creating Detailed Distribution Chart...")
    
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # 1. 更详细的直方图
    axes[0].hist(total_counts, bins=50, alpha=0.7, color='steelblue', edgecolor='black')
    axes[0].set_title('Detailed Distribution of Records per Original Image', fontsize=14)
    axes[0].set_xlabel('Number of Records')
    axes[0].set_ylabel('Frequency')
    axes[0].grid(True, alpha=0.3)
    
    # 添加统计线
    mean_val = np.mean(total_counts)
    median_val = np.median(total_counts)
    std_val = np.std(total_counts)
    
    axes[0].axvline(mean_val, color='red', linestyle='--', linewidth=2, label=f'Mean: {mean_val:.1f}')
    axes[0].axvline(median_val, color='green', linestyle='--', linewidth=2, label=f'Median: {median_val:.1f}')
    axes[0].axvline(mean_val + std_val, color='orange', linestyle=':', alpha=0.7, label=f'Mean + Std: {mean_val + std_val:.1f}')
    axes[0].axvline(mean_val - std_val, color='orange', linestyle=':', alpha=0.7, label=f'Mean - Std: {mean_val - std_val:.1f}')
    axes[0].legend()
    
    # 2. 密度图
    axes[1].hist(total_counts, bins=30, density=True, alpha=0.7, color='lightgreen', edgecolor='black')
    
    # 添加核密度估计
    from scipy import stats
    density = stats.gaussian_kde(total_counts)
    xs = np.linspace(min(total_counts), max(total_counts), 200)
    axes[1].plot(xs, density(xs), 'r-', linewidth=2, label='Kernel Density Estimate')
    
    axes[1].set_title('Probability Density of Records per Original Image', fontsize=14)
    axes[1].set_xlabel('Number of Records')
    axes[1].set_ylabel('Density')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存详细图表
    detailed_output_path = "detailed_original_distribution.png"
    plt.savefig(detailed_output_path, dpi=300, bbox_inches='tight')
    print(f"📊 Detailed chart saved to: {detailed_output_path}")
    
    plt.show()

def export_to_csv(original_stats):
    """导出数据到CSV文件"""
    print(f"\n💾 Exporting data to CSV...")
    
    # 准备数据
    data = []
    for original, stats in original_stats.items():
        data.append({
            'original_image': original,
            'train_count': stats['train'],
            'val_count': stats['val'],
            'test_count': stats['test'],
            'total_count': stats['total']
        })
    
    # 创建DataFrame并排序
    df = pd.DataFrame(data)
    df = df.sort_values('total_count', ascending=False)
    
    # 导出到CSV
    csv_path = "original_image_distribution.csv"
    df.to_csv(csv_path, index=False)
    print(f"📄 Data exported to: {csv_path}")
    
    # 显示前10行
    print(f"\n📋 Preview of exported data:")
    print(df.head(10).to_string(index=False))

def main():
    """主函数"""
    print("📊 Original Image Distribution Analysis")
    print("Analyzing the distribution of records per original_image")
    print("=" * 80)
    
    try:
        # 1. 加载数据
        results = load_original_distribution()
        
        # 2. 分析分布
        original_stats, total_counts, train_counts, val_counts, test_counts = analyze_distribution(results)
        
        # 3. 创建可视化
        create_visualizations(original_stats, total_counts, train_counts, val_counts, test_counts)
        
        # 4. 创建详细分布图
        create_detailed_distribution_chart(total_counts)
        
        # 5. 导出数据
        export_to_csv(original_stats)
        
        print(f"\n🎉 Analysis completed successfully!")
        print(f"📊 Generated files:")
        print(f"  - original_image_distribution_analysis.png")
        print(f"  - detailed_original_distribution.png") 
        print(f"  - original_image_distribution.csv")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
