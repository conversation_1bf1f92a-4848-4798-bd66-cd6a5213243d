#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 项目文件夹整理脚本

功能：
1. 清理冗余文件
2. 整理目录结构
3. 移动文件到合适位置
4. 生成整理报告
"""

import os
import shutil
import glob
from datetime import datetime, timedelta
import json

class ProjectOrganizer:
    """项目文件夹整理器"""
    
    def __init__(self, project_root="."):
        self.project_root = os.path.abspath(project_root)
        self.backup_dir = os.path.join(self.project_root, "backup")
        self.archive_dir = os.path.join(self.project_root, "archive")
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "actions": [],
            "statistics": {},
            "recommendations": []
        }
    
    def analyze_project_structure(self):
        """分析项目结构"""
        print("🔍 Analyzing project structure...")
        
        structure = {}
        total_files = 0
        total_size = 0
        
        for root, dirs, files in os.walk(self.project_root):
            if 'backup' in root or 'archive' in root:
                continue
                
            rel_path = os.path.relpath(root, self.project_root)
            structure[rel_path] = {
                'dirs': dirs.copy(),
                'files': [],
                'size': 0
            }
            
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    file_size = os.path.getsize(file_path)
                    structure[rel_path]['files'].append({
                        'name': file,
                        'size': file_size,
                        'modified': os.path.getmtime(file_path)
                    })
                    structure[rel_path]['size'] += file_size
                    total_files += 1
                    total_size += file_size
                except:
                    pass
        
        self.report['statistics'] = {
            'total_files': total_files,
            'total_size_mb': round(total_size / (1024*1024), 2),
            'directories': len(structure)
        }
        
        print(f"   📊 Found {total_files} files in {len(structure)} directories")
        print(f"   💾 Total size: {self.report['statistics']['total_size_mb']} MB")
        
        return structure
    
    def clean_old_logs(self, days_old=7):
        """清理旧日志文件"""
        print(f"\n🧹 Cleaning log files older than {days_old} days...")
        
        cutoff_time = datetime.now() - timedelta(days=days_old)
        cutoff_timestamp = cutoff_time.timestamp()
        
        log_patterns = [
            "logs/*.log",
            "logs/*/*.log",
            "*.log"
        ]
        
        cleaned_files = []
        
        for pattern in log_patterns:
            for log_file in glob.glob(pattern, recursive=True):
                try:
                    if os.path.getmtime(log_file) < cutoff_timestamp:
                        file_size = os.path.getsize(log_file)
                        os.remove(log_file)
                        cleaned_files.append({
                            'file': log_file,
                            'size': file_size,
                            'age_days': (datetime.now().timestamp() - os.path.getmtime(log_file)) / 86400
                        })
                        print(f"   🗑️  Removed: {log_file}")
                except Exception as e:
                    print(f"   ⚠️  Failed to remove {log_file}: {e}")
        
        self.report['actions'].append({
            'action': 'clean_old_logs',
            'files_removed': len(cleaned_files),
            'size_freed_mb': round(sum(f['size'] for f in cleaned_files) / (1024*1024), 2)
        })
        
        print(f"   ✅ Cleaned {len(cleaned_files)} old log files")
    
    def organize_test_files(self):
        """整理测试文件"""
        print("\n📋 Organizing test files...")
        
        # 移动根目录的测试文件到tests目录
        test_files = [
            "test_*.py",
            "*_test.py",
            "quick_test.py",
            "system_test.py",
            "run_tests.py"
        ]
        
        moved_files = []
        
        for pattern in test_files:
            for test_file in glob.glob(pattern):
                if os.path.isfile(test_file):
                    dest_dir = "tests/scripts"
                    os.makedirs(dest_dir, exist_ok=True)
                    
                    dest_path = os.path.join(dest_dir, os.path.basename(test_file))
                    
                    try:
                        shutil.move(test_file, dest_path)
                        moved_files.append(test_file)
                        print(f"   📁 Moved: {test_file} → {dest_path}")
                    except Exception as e:
                        print(f"   ⚠️  Failed to move {test_file}: {e}")
        
        self.report['actions'].append({
            'action': 'organize_test_files',
            'files_moved': len(moved_files),
            'files': moved_files
        })
    
    def clean_pycache(self):
        """清理__pycache__目录"""
        print("\n🧹 Cleaning __pycache__ directories...")
        
        cleaned_dirs = []
        
        for root, dirs, files in os.walk(self.project_root):
            if '__pycache__' in dirs:
                pycache_path = os.path.join(root, '__pycache__')
                try:
                    shutil.rmtree(pycache_path)
                    cleaned_dirs.append(pycache_path)
                    print(f"   🗑️  Removed: {pycache_path}")
                except Exception as e:
                    print(f"   ⚠️  Failed to remove {pycache_path}: {e}")
        
        self.report['actions'].append({
            'action': 'clean_pycache',
            'directories_removed': len(cleaned_dirs)
        })
        
        print(f"   ✅ Cleaned {len(cleaned_dirs)} __pycache__ directories")
    
    def organize_backup_files(self):
        """整理备份文件"""
        print("\n📦 Organizing backup files...")
        
        backup_patterns = [
            "*.backup",
            "*.bak",
            "*_backup.*",
            "*_old.*"
        ]
        
        os.makedirs(self.backup_dir, exist_ok=True)
        moved_files = []
        
        for pattern in backup_patterns:
            for backup_file in glob.glob(pattern, recursive=True):
                if os.path.isfile(backup_file):
                    dest_path = os.path.join(self.backup_dir, os.path.basename(backup_file))
                    
                    try:
                        shutil.move(backup_file, dest_path)
                        moved_files.append(backup_file)
                        print(f"   📁 Moved: {backup_file} → {dest_path}")
                    except Exception as e:
                        print(f"   ⚠️  Failed to move {backup_file}: {e}")
        
        self.report['actions'].append({
            'action': 'organize_backup_files',
            'files_moved': len(moved_files)
        })
    
    def create_standard_directories(self):
        """创建标准目录结构"""
        print("\n📁 Creating standard directory structure...")
        
        standard_dirs = [
            "output/training/models",
            "output/training/results", 
            "output/training/logs",
            "output/training/visualizations",
            "output/training/checkpoints",
            "output/testing/results",
            "output/testing/logs",
            "output/testing/visualizations",
            "output/testing/reports",
            "output/scheduler/logs",
            "output/scheduler/reports",
            "output/scheduler/tasks",
            "output/analysis/data",
            "output/analysis/visualizations",
            "output/analysis/reports",
            "output/temp/configs",
            "output/temp/cache",
            "output/temp/logs",
            "scripts/training",
            "scripts/testing",
            "scripts/analysis",
            "scripts/utilities"
        ]
        
        created_dirs = []
        
        for dir_path in standard_dirs:
            try:
                os.makedirs(dir_path, exist_ok=True)
                if not os.path.exists(dir_path):
                    created_dirs.append(dir_path)
                    print(f"   📁 Created: {dir_path}")
            except Exception as e:
                print(f"   ⚠️  Failed to create {dir_path}: {e}")
        
        self.report['actions'].append({
            'action': 'create_standard_directories',
            'directories_created': len(created_dirs)
        })
    
    def move_scripts_to_scripts_dir(self):
        """移动脚本文件到scripts目录"""
        print("\n📜 Moving scripts to scripts directory...")
        
        script_mappings = {
            "main.py": "scripts/training/",
            "main_scheduler.py": "scripts/training/",
            "model_loader.py": "scripts/utilities/",
            "fix_print_to_logger.py": "scripts/utilities/",
            "training_test.py": "scripts/testing/",
            "organize_project.py": "scripts/utilities/"
        }
        
        moved_files = []
        
        for script_file, dest_dir in script_mappings.items():
            if os.path.exists(script_file):
                os.makedirs(dest_dir, exist_ok=True)
                dest_path = os.path.join(dest_dir, script_file)
                
                try:
                    shutil.move(script_file, dest_path)
                    moved_files.append((script_file, dest_path))
                    print(f"   📁 Moved: {script_file} → {dest_path}")
                except Exception as e:
                    print(f"   ⚠️  Failed to move {script_file}: {e}")
        
        self.report['actions'].append({
            'action': 'move_scripts',
            'files_moved': len(moved_files)
        })
    
    def generate_recommendations(self):
        """生成整理建议"""
        print("\n💡 Generating recommendations...")
        
        recommendations = []
        
        # 检查大文件
        large_files = []
        for root, dirs, files in os.walk(self.project_root):
            for file in files:
                file_path = os.path.join(root, file)
                try:
                    size = os.path.getsize(file_path)
                    if size > 10 * 1024 * 1024:  # 10MB
                        large_files.append((file_path, size))
                except:
                    pass
        
        if large_files:
            recommendations.append({
                'type': 'large_files',
                'message': f"Found {len(large_files)} files larger than 10MB",
                'files': [(f, round(s/(1024*1024), 2)) for f, s in large_files]
            })
        
        # 检查空目录
        empty_dirs = []
        for root, dirs, files in os.walk(self.project_root):
            if not dirs and not files:
                empty_dirs.append(root)
        
        if empty_dirs:
            recommendations.append({
                'type': 'empty_directories',
                'message': f"Found {len(empty_dirs)} empty directories",
                'directories': empty_dirs
            })
        
        self.report['recommendations'] = recommendations
        
        for rec in recommendations:
            print(f"   💡 {rec['message']}")
    
    def save_report(self):
        """保存整理报告"""
        report_file = f"organization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 Organization report saved to: {report_file}")
    
    def run_full_organization(self):
        """运行完整的文件夹整理"""
        print("🎯 SeedVision v1 Project Organization")
        print("=" * 50)
        
        # 分析项目结构
        self.analyze_project_structure()
        
        # 清理操作
        self.clean_old_logs(days_old=7)
        self.clean_pycache()
        
        # 整理操作
        self.organize_test_files()
        self.organize_backup_files()
        self.create_standard_directories()
        self.move_scripts_to_scripts_dir()
        
        # 生成建议
        self.generate_recommendations()
        
        # 保存报告
        self.save_report()
        
        print("\n🎉 Project organization completed!")
        print(f"📊 Summary:")
        print(f"   - Files processed: {self.report['statistics']['total_files']}")
        print(f"   - Actions performed: {len(self.report['actions'])}")
        print(f"   - Recommendations: {len(self.report['recommendations'])}")

def main():
    """主函数"""
    organizer = ProjectOrganizer()
    organizer.run_full_organization()

if __name__ == "__main__":
    main()
