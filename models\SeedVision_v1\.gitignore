# SeedVision v1 - Git Ignore File

# Output directories
output/
logs/
backup/
archive/

# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# PyTorch
*.pth
*.pt
checkpoints/

# Tensorboard
runs/
tb_logs/

# Data files
*.csv
*.json
*.pkl
*.pickle
*.h5
*.hdf5

# Image files (except examples)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.svg
!examples/*.png
!docs/*.png

# Video files
*.mp4
*.avi
*.mov
*.mkv

# Compressed files
*.zip
*.tar.gz
*.rar
*.7z

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*_backup.*
*_old.*

# Reports and analysis
*.html
*.pdf
organization_report_*.json
cleanup_report_*.json

# Model files
models/*.pth
models/*.pt
*.model

# Large data directories
dataset/
data/
raw_data/
processed_data/

# Configuration overrides
config_local.yaml
config_override.yaml
local_config.yaml

# Test outputs
test_output/
test_results/
visualization_results/

# Profiling
*.prof
*.profile

# Memory dumps
*.dump
*.dmp

# Lock files
*.lock
Pipfile.lock
poetry.lock
