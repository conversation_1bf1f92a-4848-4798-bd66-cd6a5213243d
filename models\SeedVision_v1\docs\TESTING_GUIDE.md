# SeedVision v1 - 测试指南

## 🎯 概述

SeedVision v1提供了完整的测试套件，确保系统在移植后能够正常工作。测试套件包含多个层次的测试，从快速验证到完整功能测试。

## 📋 测试套件结构

```
测试脚本/
├── run_tests.py              # 🎯 测试套件主入口
├── quick_test.py             # ⚡ 快速测试（2-3分钟）
├── system_test.py            # 🔧 系统完整测试（15-20分钟）
├── training_test.py          # 🏃 训练功能测试（10-15分钟）
└── tests/examples/           # 📚 示例测试脚本
    ├── scheduler_example.py  # 调度器示例
    ├── test_original_evaluation.py
    └── final_verification.py
```

## 🚀 快速开始

### 1. 移植后第一次验证
```bash
# 快速测试 - 验证基础功能是否正常
python run_tests.py quick
```

### 2. 基础功能验证
```bash
# 基础测试 - 验证核心功能
python run_tests.py basic
```

### 3. 完整功能验证
```bash
# 完整测试 - 验证所有功能
python run_tests.py full
```

## 📊 测试类型详解

### ⚡ 快速测试 (quick)
**耗时**: 2-3分钟  
**适用场景**: 移植后第一次验证、快速检查

**测试内容**:
- ✅ 模块导入测试
- ✅ 配置系统测试
- ✅ 文件结构验证
- ✅ 调度器基础功能
- ✅ 模型创建测试

**运行方式**:
```bash
python run_tests.py quick
# 或直接运行
python quick_test.py
```

### 🧪 基础测试 (basic)
**耗时**: 5-10分钟  
**适用场景**: 日常开发验证、功能检查

**测试内容**:
- ✅ 快速测试的所有内容
- ✅ 调度器资源预估
- ✅ 系统资源监控

**运行方式**:
```bash
python run_tests.py basic
```

### 🏃 训练测试 (training)
**耗时**: 10-15分钟  
**适用场景**: 训练功能验证、数据流程检查

**测试内容**:
- ✅ 数据加载和采样
- ✅ Original级别评估
- ✅ 可视化功能
- ✅ 快速训练流程（1 epoch）
- ✅ 调度器训练功能

**运行方式**:
```bash
python run_tests.py training
# 或直接运行
python training_test.py --full
```

### 📋 调度器测试 (scheduler)
**耗时**: 3-5分钟  
**适用场景**: 调度器功能验证

**测试内容**:
- ✅ 资源预估功能
- ✅ 任务调度逻辑
- ✅ 系统监控功能
- ✅ 调度器示例运行

**运行方式**:
```bash
python run_tests.py scheduler
```

### 🎯 完整测试 (full)
**耗时**: 15-30分钟  
**适用场景**: 发布前验证、完整功能检查

**测试内容**:
- ✅ 系统完整测试的所有内容
- ✅ 训练功能测试的所有内容
- ✅ 详细的错误报告和日志

**运行方式**:
```bash
python run_tests.py full
# 或直接运行
python system_test.py
```

## 🔧 单独测试脚本

### 1. 快速测试
```bash
python quick_test.py
```
验证基础功能，适合快速检查。

### 2. 系统完整测试
```bash
python system_test.py
```
最全面的测试，生成详细报告。

### 3. 训练功能测试
```bash
# 基础训练测试
python training_test.py

# 完整训练测试（包含实际训练）
python training_test.py --full
```

### 4. 调度器示例
```bash
python tests/examples/scheduler_example.py
```

### 5. Original评估测试
```bash
python tests/examples/test_original_evaluation.py
```

## 📈 测试结果解读

### 成功示例
```
🎯 测试结论:
✅ 所有测试通过！系统基础功能正常。
🚀 可以继续进行完整测试或开始使用系统。

💡 下一步建议:
   1. 运行完整测试: python system_test.py
   2. 测试调度器: python main_scheduler.py --mode estimate
   3. 开始训练: python main.py --sequential
```

### 失败示例
```
⚠️  有 2 个测试失败。
🔧 请先修复失败的测试项，然后重新运行测试。

💡 故障排除建议:
   1. 检查Python路径设置
   2. 确认所有文件已正确移动
   3. 检查依赖包是否安装
```

## 🛠️ 故障排除

### 常见问题

#### 1. 导入错误
```
❌ tools.training.train - 导入失败: No module named 'tools'
```
**解决方案**:
- 检查当前工作目录是否为 `models/SeedVision_v1/`
- 确认文件结构是否正确
- 检查 `sys.path` 设置

#### 2. 配置文件错误
```
❌ Configuration - 配置加载失败
```
**解决方案**:
- 检查 `config/training_config.yaml` 是否存在
- 验证YAML文件格式是否正确
- 确认至少有一个配置的 `enable: true`

#### 3. 数据加载错误
```
❌ Data Loading - 数据加载失败或为空
```
**解决方案**:
- 检查数据集路径是否正确
- 确认数据集文件是否存在
- 验证数据格式是否正确

#### 4. GPU相关错误
```
❌ CUDA out of memory
```
**解决方案**:
- 减小测试的批次大小
- 使用CPU进行测试
- 清理GPU显存

### 调试技巧

#### 1. 详细错误信息
```bash
# 运行单独的测试脚本获取详细错误
python quick_test.py
python system_test.py
```

#### 2. 检查日志文件
```bash
# 查看生成的测试报告
ls output/system_test_report_*.json
```

#### 3. 逐步测试
```bash
# 按顺序运行测试
python run_tests.py quick      # 先运行快速测试
python run_tests.py basic      # 再运行基础测试
python run_tests.py training   # 最后运行训练测试
```

## 📋 测试检查清单

### 移植后必须检查的项目

- [ ] **文件结构**: 所有文件是否正确移动到新位置
- [ ] **导入路径**: 模块导入是否正常
- [ ] **配置文件**: 配置是否正确加载
- [ ] **数据路径**: 数据集路径是否正确
- [ ] **依赖包**: 所需的Python包是否安装
- [ ] **GPU环境**: CUDA环境是否正常（如果使用GPU）

### 功能验证检查清单

- [ ] **基础功能**: 快速测试通过
- [ ] **配置系统**: 配置加载和解析正常
- [ ] **数据加载**: 数据采样和加载正常
- [ ] **模型功能**: 模型创建和前向传播正常
- [ ] **训练流程**: 训练流程完整运行
- [ ] **调度器**: 资源预估和任务调度正常
- [ ] **可视化**: 图表生成正常
- [ ] **Original评估**: Original级别评估正常

## 🎯 推荐测试流程

### 新环境部署
1. **快速验证**: `python run_tests.py quick`
2. **基础检查**: `python run_tests.py basic`
3. **功能验证**: `python run_tests.py training`
4. **完整测试**: `python run_tests.py full`

### 日常开发
1. **代码修改后**: `python run_tests.py quick`
2. **功能开发后**: `python run_tests.py basic`
3. **发布前**: `python run_tests.py full`

### 问题排查
1. **运行快速测试**: 定位基础问题
2. **查看详细日志**: 分析具体错误
3. **逐个功能测试**: 隔离问题范围
4. **修复后重测**: 验证修复效果

## 💡 最佳实践

1. **定期测试**: 建议每次代码修改后运行快速测试
2. **完整验证**: 重要更新后运行完整测试
3. **保存报告**: 保留测试报告用于问题追踪
4. **环境一致**: 确保测试环境与生产环境一致
5. **及时修复**: 发现问题及时修复，避免累积

通过这套完整的测试体系，您可以确保SeedVision v1在任何环境下都能稳定运行！
