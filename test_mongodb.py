#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB 连接器测试脚本

测试 MongoDB 连接器的功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mongodb_import():
    """测试 MongoDB 连接器导入"""
    try:
        from utils.db_utils import database_connecter, get_mongodb_database, test_mongodb_connection
        print("[SUCCESS] MongoDB 连接器导入成功")
        return True
    except ImportError as e:
        print(f"[ERROR] MongoDB 连接器导入失败: {e}")
        print("[INFO] 请安装 pymongo: pip install pymongo")
        return False
    except Exception as e:
        print(f"[ERROR] 导入异常: {e}")
        return False

def test_mongodb_usage():
    """测试 MongoDB 连接器使用"""
    try:
        from utils.db_utils import database_connecter, get_mongodb_database

        # 获取连接器实例
        db_conn = database_connecter

        if db_conn is None:
            print("[ERROR] 数据库连接器实例为空")
            return False

        # 列出数据库
        databases = db_conn.list_mongodb_databases()
        print(f"[INFO] 可用数据库: {databases}")

        # 测试获取数据库
        test_db = db_conn.get_mongodb_database('test_db')
        print(f"[INFO] 获取数据库成功: {test_db.name}")

        # 测试集合操作
        test_collection = test_db['test_collection']

        # 插入测试数据
        test_doc = {
            'name': 'test_document',
            'value': 123,
            'description': 'MongoDB 连接器测试文档'
        }

        result = test_collection.insert_one(test_doc)
        print(f"[INFO] 插入文档成功, ID: {result.inserted_id}")

        # 查询测试数据
        found_doc = test_collection.find_one({'name': 'test_document'})
        if found_doc:
            print(f"[INFO] 查询文档成功: {found_doc}")

        # 删除测试数据
        delete_result = test_collection.delete_one({'name': 'test_document'})
        print(f"[INFO] 删除文档成功, 删除数量: {delete_result.deleted_count}")

        # 测试便捷函数
        test_db2 = get_mongodb_database('test_db2')
        print(f"[INFO] 便捷函数获取数据库成功: {test_db2.name}")

        print("[SUCCESS] MongoDB 连接器功能测试通过")
        return True

    except Exception as e:
        print(f"[ERROR] MongoDB 连接器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("[MONGODB TEST] MongoDB 连接器测试")
    print("=" * 60)

    # 测试导入
    if not test_mongodb_import():
        print("\n[FAILED] 导入测试失败，退出")
        return

    print("\n" + "-" * 40)
    print("[TEST] 测试 MongoDB 连接器功能")
    print("-" * 40)

    # 测试功能
    if test_mongodb_usage():
        print("\n[SUCCESS] 所有测试通过")
    else:
        print("\n[FAILED] 功能测试失败")

    print("\n" + "=" * 60)
    print("[COMPLETE] 测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
