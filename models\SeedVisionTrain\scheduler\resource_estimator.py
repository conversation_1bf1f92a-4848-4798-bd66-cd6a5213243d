#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源预估模块 - Resource Estimator

功能：
1. 预估训练任务的GPU显存占用
2. 预估训练时间
3. 评估系统资源可用性
4. 为任务调度提供决策依据
"""

import math
import psutil
import torch
from typing import Dict, Tuple, Optional
import logging

class ResourceEstimator:
    """资源预估器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # GPU显存基础占用 (MB)
        self.base_gpu_memory = 500  # PyTorch基础占用
        
        # 模型参数显存系数 (MB per million parameters)
        self.param_memory_factor = 4.0  # FP32: 4 bytes per parameter
        
        # 激活值显存系数 (MB per sample)
        self.activation_memory_factor = {
            224: 12.0,  # 224x224输入的激活值显存
            112: 3.0,   # 112x112输入的激活值显存
            56: 0.8     # 56x56输入的激活值显存
        }
        
        # 训练时间基准 (seconds per epoch per 1000 samples)
        self.time_base_factors = {
            224: 120,   # 224x224输入的训练时间基准
            112: 60,    # 112x112输入的训练时间基准
            56: 30      # 56x56输入的训练时间基准
        }
    
    def estimate_model_parameters(self, model_config: Dict) -> float:
        """
        估算模型参数数量 (百万)
        
        参数:
            model_config: 模型配置字典
            
        返回:
            参数数量 (百万)
        """
        embed_dim = model_config.get('embed_dim', 192)
        depths = model_config.get('depths', [3, 4, 18, 3])
        mlp_ratio = model_config.get('mlp_ratio', 2.0)
        
        # FasterNet参数估算公式 (简化版)
        # 主要包括：embedding层 + 各stage的参数
        
        # Embedding层参数
        patch_size = model_config.get('patch_size', 4)
        embedding_params = 3 * patch_size * patch_size * embed_dim
        
        # 各stage参数估算
        stage_params = 0
        current_dim = embed_dim
        
        for i, depth in enumerate(depths):
            # 每个block的参数估算
            block_params = (
                current_dim * current_dim * 2 +  # DWConv + PWConv
                current_dim * int(current_dim * mlp_ratio) * 2  # MLP
            )
            stage_params += block_params * depth
            
            # 下采样层参数
            if i < len(depths) - 1:
                next_dim = current_dim * 2
                stage_params += current_dim * next_dim
                current_dim = next_dim
        
        # 分类头参数
        head_params = current_dim * 2  # 回归任务，输出2个值
        
        total_params = embedding_params + stage_params + head_params
        return total_params / 1e6  # 转换为百万
    
    def estimate_gpu_memory(self, config: Dict) -> Dict[str, float]:
        """
        估算GPU显存占用
        
        参数:
            config: 训练配置字典
            
        返回:
            显存占用估算结果 (GB)
        """
        model_config = config.get('model', {})
        resources_config = config.get('resources', {})
        transform_config = config.get('transform_config', '224x224_norm')
        
        # 解析输入尺寸
        input_size = 224
        if '112x112' in transform_config:
            input_size = 112
        elif '56x56' in transform_config:
            input_size = 56
        
        batch_size = resources_config.get('batch_size', 32)
        
        # 1. 模型参数显存
        model_params = self.estimate_model_parameters(model_config)
        param_memory = model_params * self.param_memory_factor
        
        # 2. 激活值显存 (前向传播)
        activation_factor = self.activation_memory_factor.get(input_size, 12.0)
        activation_memory = activation_factor * batch_size
        
        # 3. 梯度显存 (反向传播，约等于参数显存)
        gradient_memory = param_memory
        
        # 4. 优化器状态显存 (Adam: 2x参数显存)
        optimizer_memory = param_memory * 2
        
        # 5. 基础显存占用
        base_memory = self.base_gpu_memory
        
        # 总显存占用
        total_memory_mb = (
            base_memory + 
            param_memory + 
            activation_memory + 
            gradient_memory + 
            optimizer_memory
        )
        
        return {
            'total_gb': total_memory_mb / 1024,
            'base_mb': base_memory,
            'parameters_mb': param_memory,
            'activations_mb': activation_memory,
            'gradients_mb': gradient_memory,
            'optimizer_mb': optimizer_memory,
            'model_params_millions': model_params
        }
    
    def estimate_training_time(self, config: Dict) -> Dict[str, float]:
        """
        估算训练时间
        
        参数:
            config: 训练配置字典
            
        返回:
            训练时间估算结果
        """
        # 解析配置
        training_config = config.get('training', {})
        dataset_config = config.get('dataset_config', {})
        transform_config = config.get('transform_config', '224x224_norm')
        
        num_epochs = training_config.get('num_epochs', 50)
        
        # 解析输入尺寸
        input_size = 224
        if '112x112' in transform_config:
            input_size = 112
        elif '56x56' in transform_config:
            input_size = 56
        
        # 获取样本数量
        strategy_params = dataset_config.get('strategy_parameters', {})
        total_samples = strategy_params.get('total_samples', 2400)
        
        # 训练时间基准
        time_factor = self.time_base_factors.get(input_size, 120)
        
        # 估算每个epoch时间 (秒)
        epoch_time = (total_samples / 1000) * time_factor
        
        # 总训练时间
        total_time = epoch_time * num_epochs
        
        return {
            'total_hours': total_time / 3600,
            'total_minutes': total_time / 60,
            'epoch_minutes': epoch_time / 60,
            'samples_per_epoch': total_samples,
            'estimated_epochs': num_epochs
        }
    
    def get_system_resources(self) -> Dict[str, float]:
        """
        获取系统资源状态
        
        返回:
            系统资源信息
        """
        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_total_gb = memory.total / (1024**3)
        memory_used_gb = memory.used / (1024**3)
        memory_available_gb = memory.available / (1024**3)
        
        # GPU信息
        gpu_info = {}
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                gpu_memory_total = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                gpu_memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
                gpu_memory_cached = torch.cuda.memory_reserved(i) / (1024**3)
                gpu_memory_free = gpu_memory_total - gpu_memory_cached
                
                gpu_info[f'gpu_{i}'] = {
                    'name': torch.cuda.get_device_name(i),
                    'total_gb': gpu_memory_total,
                    'allocated_gb': gpu_memory_allocated,
                    'cached_gb': gpu_memory_cached,
                    'free_gb': gpu_memory_free,
                    'utilization_percent': (gpu_memory_cached / gpu_memory_total) * 100
                }
        
        return {
            'cpu': {
                'usage_percent': cpu_percent,
                'core_count': cpu_count
            },
            'memory': {
                'total_gb': memory_total_gb,
                'used_gb': memory_used_gb,
                'available_gb': memory_available_gb,
                'usage_percent': memory.percent
            },
            'gpu': gpu_info
        }
    
    def can_run_task(self, config: Dict, max_gpu_memory: float = 8.0) -> Tuple[bool, str]:
        """
        判断是否可以运行任务
        
        参数:
            config: 训练配置
            max_gpu_memory: 最大GPU显存限制 (GB)
            
        返回:
            (是否可运行, 原因说明)
        """
        # 估算资源需求
        memory_estimate = self.estimate_gpu_memory(config)
        estimated_gpu_gb = memory_estimate['total_gb']
        
        # 获取系统资源
        system_resources = self.get_system_resources()
        
        # 检查GPU显存
        if estimated_gpu_gb > max_gpu_memory:
            return False, f"估算GPU显存需求 {estimated_gpu_gb:.1f}GB 超过限制 {max_gpu_memory}GB"
        
        # 检查可用GPU显存
        if system_resources['gpu']:
            gpu_0 = system_resources['gpu'].get('gpu_0', {})
            available_gpu_memory = gpu_0.get('free_gb', 0)
            
            if estimated_gpu_gb > available_gpu_memory:
                return False, f"估算GPU显存需求 {estimated_gpu_gb:.1f}GB 超过可用显存 {available_gpu_memory:.1f}GB"
        
        # 检查系统内存
        available_memory = system_resources['memory']['available_gb']
        if available_memory < 2.0:  # 至少需要2GB系统内存
            return False, f"系统可用内存不足: {available_memory:.1f}GB < 2.0GB"
        
        return True, "资源充足，可以运行"
    
    def generate_resource_report(self, config: Dict) -> Dict:
        """
        生成完整的资源评估报告
        
        参数:
            config: 训练配置
            
        返回:
            完整的资源评估报告
        """
        memory_estimate = self.estimate_gpu_memory(config)
        time_estimate = self.estimate_training_time(config)
        system_resources = self.get_system_resources()
        can_run, reason = self.can_run_task(config)
        
        return {
            'task_name': config.get('name', 'unknown'),
            'memory_estimate': memory_estimate,
            'time_estimate': time_estimate,
            'system_resources': system_resources,
            'can_run': can_run,
            'run_reason': reason,
            'recommendations': self._generate_recommendations(memory_estimate, time_estimate)
        }
    
    def _generate_recommendations(self, memory_estimate: Dict, time_estimate: Dict) -> list:
        """生成优化建议"""
        recommendations = []
        
        # 显存优化建议
        if memory_estimate['total_gb'] > 6.0:
            recommendations.append("显存占用较高，建议减小batch_size或模型参数")
        
        if memory_estimate['total_gb'] > 4.0:
            recommendations.append("考虑使用混合精度训练(FP16)以减少显存占用")
        
        # 训练时间建议
        if time_estimate['total_hours'] > 24:
            recommendations.append("训练时间较长，建议分阶段训练或减少epoch数")
        
        if time_estimate['total_hours'] > 12:
            recommendations.append("建议使用学习率调度器加速收敛")
        
        return recommendations
