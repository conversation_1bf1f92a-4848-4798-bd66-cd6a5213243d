"""
数据重新分配脚本
按照每个original_image的数据8:1:1分配到train、val、test集
确保数据分布的平衡性
"""

import sys
sys.path.append('E:\Proj\pytorch-model-train')

from utils.db_utils import mysql_connecter
from collections import defaultdict
import random
import math

def analyze_current_distribution():
    """分析当前数据分布"""
    print("📊 Analyzing Current Data Distribution")
    print("=" * 60)
    
    db_name = 'SeedVision'
    table_name = 'yolotrain2'
    
    with mysql_connecter.cursor() as cursor:
        cursor.execute(f"USE {db_name}")
        
        # 统计各类型数据量
        for data_type in ['train', 'val', 'test']:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name} WHERE type = %s", (data_type,))
            result = cursor.fetchone()
            print(f"  - {data_type}: {result['count']:,} records")
        
        # 统计总数
        cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
        total = cursor.fetchone()['count']
        print(f"  - Total: {total:,} records")
        
        # 分析original_image分布
        cursor.execute(f"""
            SELECT original_image, type, COUNT(*) as count 
            FROM {table_name} 
            GROUP BY original_image, type 
            ORDER BY original_image, type
        """)
        results = cursor.fetchall()
        
        # 按original_image分组
        original_stats = defaultdict(lambda: {'train': 0, 'val': 0, 'test': 0})
        for row in results:
            original_stats[row['original_image']][row['type']] = row['count']
        
        print(f"\n📈 Original Image Distribution:")
        print(f"  - Unique original images: {len(original_stats)}")
        
        # 显示前10个original的分布
        print(f"  - Sample distribution (first 10):")
        for i, (orig, stats) in enumerate(list(original_stats.items())[:10]):
            total_orig = sum(stats.values())
            print(f"    {i+1}. {orig}: train={stats['train']}, val={stats['val']}, test={stats['test']}, total={total_orig}")
        
        return original_stats, total

def load_all_data():
    """加载所有数据"""
    print("\n📥 Loading All Data")
    print("=" * 60)
    
    db_name = 'SeedVision'
    table_name = 'yolotrain2'
    
    with mysql_connecter.cursor() as cursor:
        cursor.execute(f"USE {db_name}")
        cursor.execute(f"SELECT * FROM {table_name}")
        data = cursor.fetchall()
    
    print(f"✅ Loaded {len(data):,} records")
    return data

def redistribute_data(data, train_ratio=0.8, val_ratio=0.1, test_ratio=0.1, seed=42):
    """
    重新分配数据
    
    参数:
        data: 所有数据
        train_ratio: 训练集比例
        val_ratio: 验证集比例  
        test_ratio: 测试集比例
        seed: 随机种子
    """
    print(f"\n🔄 Redistributing Data (Train:{train_ratio}, Val:{val_ratio}, Test:{test_ratio})")
    print("=" * 60)
    
    random.seed(seed)
    
    # 按original_image分组
    original_groups = defaultdict(list)
    for item in data:
        original_image = item['original_image']
        original_groups[original_image].append(item)
    
    print(f"📊 Found {len(original_groups)} unique original images")
    
    # 统计信息
    redistribution_stats = {
        'train': 0,
        'val': 0, 
        'test': 0
    }
    
    updates = []  # 存储需要更新的记录
    
    # 对每个original_image进行重新分配
    for original_name, items in original_groups.items():
        total_items = len(items)
        
        # 计算各集合的数量
        train_count = math.floor(total_items * train_ratio)
        val_count = math.floor(total_items * val_ratio)
        test_count = total_items - train_count - val_count  # 剩余的都给test
        
        # 随机打乱数据
        random.shuffle(items)
        
        # 分配数据
        train_items = items[:train_count]
        val_items = items[train_count:train_count + val_count]
        test_items = items[train_count + val_count:]
        
        # 记录更新信息
        for item in train_items:
            if item['type'] != 'train':
                updates.append((item['id'], 'train'))
        
        for item in val_items:
            if item['type'] != 'val':
                updates.append((item['id'], 'val'))
        
        for item in test_items:
            if item['type'] != 'test':
                updates.append((item['id'], 'test'))
        
        # 更新统计
        redistribution_stats['train'] += len(train_items)
        redistribution_stats['val'] += len(val_items)
        redistribution_stats['test'] += len(test_items)
        
        # 显示详细信息（仅前10个）
        if len([k for k in original_groups.keys() if k <= original_name]) <= 10:
            print(f"  {original_name}: {total_items} -> train:{len(train_items)}, val:{len(val_items)}, test:{len(test_items)}")
    
    print(f"\n📈 New Distribution:")
    for data_type, count in redistribution_stats.items():
        percentage = (count / len(data)) * 100
        print(f"  - {data_type}: {count:,} records ({percentage:.1f}%)")
    
    print(f"\n🔧 Updates needed: {len(updates):,} records")
    
    return updates, redistribution_stats

def apply_updates(updates, dry_run=True):
    """
    应用更新到数据库
    
    参数:
        updates: 更新列表 [(id, new_type), ...]
        dry_run: 是否为试运行（不实际更新数据库）
    """
    print(f"\n{'🧪 DRY RUN - ' if dry_run else '💾 '}Applying Updates")
    print("=" * 60)
    
    if not updates:
        print("✅ No updates needed!")
        return
    
    db_name = 'SeedVision'
    table_name = 'yolotrain2'
    
    if dry_run:
        print(f"📋 Would update {len(updates):,} records:")
        
        # 统计更新类型
        update_stats = defaultdict(int)
        for _, new_type in updates:
            update_stats[new_type] += 1
        
        for data_type, count in update_stats.items():
            print(f"  - Set to '{data_type}': {count:,} records")
        
        print(f"\n💡 To apply changes, run with dry_run=False")
        
    else:
        print(f"🔄 Updating {len(updates):,} records in database...")
        
        with mysql_connecter.cursor() as cursor:
            cursor.execute(f"USE {db_name}")
            
            # 批量更新
            batch_size = 1000
            updated_count = 0
            
            for i in range(0, len(updates), batch_size):
                batch = updates[i:i + batch_size]
                
                for record_id, new_type in batch:
                    cursor.execute(
                        f"UPDATE {table_name} SET type = %s WHERE id = %s",
                        (new_type, record_id)
                    )
                
                updated_count += len(batch)
                print(f"  ✅ Updated {updated_count:,}/{len(updates):,} records")
            
            # 提交更改
            mysql_connecter.commit()
            print(f"✅ All updates committed to database!")

def verify_redistribution():
    """验证重新分配的结果"""
    print(f"\n🔍 Verifying Redistribution Results")
    print("=" * 60)
    
    db_name = 'SeedVision'
    table_name = 'yolotrain2'
    
    with mysql_connecter.cursor() as cursor:
        cursor.execute(f"USE {db_name}")
        
        # 统计新的分布
        print("📊 New Distribution:")
        total = 0
        for data_type in ['train', 'val', 'test']:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table_name} WHERE type = %s", (data_type,))
            count = cursor.fetchone()['count']
            total += count
            percentage = (count / total * 100) if total > 0 else 0
            print(f"  - {data_type}: {count:,} records")
        
        # 验证每个original_image的分布
        cursor.execute(f"""
            SELECT original_image, type, COUNT(*) as count 
            FROM {table_name} 
            GROUP BY original_image, type 
            ORDER BY original_image, type
        """)
        results = cursor.fetchall()
        
        # 检查分布是否合理
        original_stats = defaultdict(lambda: {'train': 0, 'val': 0, 'test': 0})
        for row in results:
            original_stats[row['original_image']][row['type']] = row['count']
        
        print(f"\n✅ Verification completed for {len(original_stats)} original images")
        
        # 检查比例是否接近8:1:1
        ratio_issues = []
        for orig, stats in original_stats.items():
            total_orig = sum(stats.values())
            if total_orig > 0:
                train_ratio = stats['train'] / total_orig
                val_ratio = stats['val'] / total_orig
                test_ratio = stats['test'] / total_orig
                
                # 检查是否接近期望比例（允许一定误差）
                if abs(train_ratio - 0.8) > 0.15 or abs(val_ratio - 0.1) > 0.1 or abs(test_ratio - 0.1) > 0.1:
                    ratio_issues.append((orig, train_ratio, val_ratio, test_ratio))
        
        if ratio_issues:
            print(f"⚠️  Found {len(ratio_issues)} original images with ratio issues:")
            for orig, tr, vr, ter in ratio_issues[:5]:  # 只显示前5个
                print(f"    {orig}: train={tr:.2f}, val={vr:.2f}, test={ter:.2f}")
        else:
            print(f"✅ All original images have reasonable 8:1:1 distribution!")

def main():
    """主函数"""
    print("🚀 Data Redistribution Script")
    print("Redistributing data by original_image with 8:1:1 ratio")
    print("=" * 80)
    
    try:
        # 1. 分析当前分布
        current_stats, total_records = analyze_current_distribution()
        
        # 2. 加载所有数据
        all_data = load_all_data()
        
        # 3. 重新分配数据
        updates, new_stats = redistribute_data(all_data)
        
        # 4. 试运行更新
        apply_updates(updates, dry_run=True)
        
        # 5. 询问是否真正执行
        print(f"\n" + "=" * 60)
        print("⚠️  CONFIRMATION REQUIRED")
        print("=" * 60)
        print("This will modify the database. Current distribution:")
        for data_type in ['train', 'val', 'test']:
            current = sum(stats.get(data_type, 0) for stats in current_stats.values())
            new = new_stats[data_type]
            print(f"  {data_type}: {current:,} -> {new:,}")
        
        response = input("\nDo you want to apply these changes? (yes/no): ").strip().lower()
        
        if response in ['yes', 'y']:
            # 6. 应用更新
            apply_updates(updates, dry_run=False)
            
            # 7. 验证结果
            verify_redistribution()
            
            print(f"\n🎉 Data redistribution completed successfully!")
        else:
            print(f"\n❌ Operation cancelled by user.")
            
    except Exception as e:
        print(f"❌ Error during redistribution: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
