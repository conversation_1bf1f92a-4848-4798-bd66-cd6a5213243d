#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启用所有32个用户配置的工具脚本

使用方法：
python enable_all_configs.py
"""

import yaml
import os

def enable_all_user_configs():
    """启用所有用户配置"""
    config_path = "config/training_config.yaml"
    
    print("[INFO] 正在加载配置文件...")
    
    # 读取配置文件
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"[ERROR] 无法读取配置文件: {e}")
        return False
    
    # 统计信息
    user_configs_count = 0
    enabled_count = 0
    
    print("[INFO] 正在启用所有用户配置...")
    
    # 遍历所有训练配置
    if 'training_configs' in config:
        for config_name, config_data in config['training_configs'].items():
            if config_name.startswith('user_'):
                user_configs_count += 1
                if not config_data.get('enable', False):
                    config_data['enable'] = True
                    enabled_count += 1
                    print(f"[ENABLED] {config_name}")
                else:
                    print(f"[ALREADY ENABLED] {config_name}")
    
    # 保存配置文件
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        print(f"\n[SUCCESS] 配置文件已保存")
        print(f"[SUMMARY] 找到 {user_configs_count} 个用户配置，新启用 {enabled_count} 个")
        print(f"[INFO] 现在可以运行 python main.py 开始训练所有32个配置")
        return True
    except Exception as e:
        print(f"[ERROR] 无法保存配置文件: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("[CONFIG TOOL] 批量启用32个用户配置")
    print("=" * 60)
    
    enable_all_user_configs()
    
    print("\n" + "=" * 60)
    print("[COMPLETE] 配置更新完成！")
    print("=" * 60)
