#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志清理脚本

功能：
1. 清理旧的日志文件
2. 压缩大日志文件
3. 维护日志目录结构
4. 生成清理报告
"""

import os
import glob
import gzip
import shutil
from datetime import datetime, timedelta
import json

class LogCleaner:
    """日志清理器"""
    
    def __init__(self, project_root="../../"):
        self.project_root = os.path.abspath(project_root)
        self.report = {
            "timestamp": datetime.now().isoformat(),
            "cleaned_files": [],
            "compressed_files": [],
            "total_space_freed": 0,
            "errors": []
        }
    
    def clean_old_logs(self, days_old=7, compress_days=3):
        """
        清理旧日志文件
        
        参数:
            days_old: 删除多少天前的日志
            compress_days: 压缩多少天前的日志
        """
        print(f"🧹 Cleaning logs older than {days_old} days...")
        print(f"📦 Compressing logs older than {compress_days} days...")
        
        cutoff_delete = datetime.now() - timedelta(days=days_old)
        cutoff_compress = datetime.now() - timedelta(days=compress_days)
        
        log_patterns = [
            os.path.join(self.project_root, "logs", "*.log"),
            os.path.join(self.project_root, "logs", "*", "*.log"),
            os.path.join(self.project_root, "logs", "*", "*", "*.log"),
            os.path.join(self.project_root, "output", "*", "logs", "*", "*.log"),
            os.path.join(self.project_root, "output", "*", "logs", "*", "*", "*.log")
        ]
        
        for pattern in log_patterns:
            for log_file in glob.glob(pattern, recursive=True):
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                    file_size = os.path.getsize(log_file)
                    
                    if file_time < cutoff_delete:
                        # 删除旧文件
                        os.remove(log_file)
                        self.report["cleaned_files"].append({
                            "file": log_file,
                            "size": file_size,
                            "age_days": (datetime.now() - file_time).days
                        })
                        self.report["total_space_freed"] += file_size
                        print(f"   🗑️  Deleted: {os.path.basename(log_file)} ({file_size/1024:.1f}KB)")
                        
                    elif file_time < cutoff_compress and not log_file.endswith('.gz'):
                        # 压缩中等旧的文件
                        compressed_file = log_file + '.gz'
                        with open(log_file, 'rb') as f_in:
                            with gzip.open(compressed_file, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
                        
                        compressed_size = os.path.getsize(compressed_file)
                        space_saved = file_size - compressed_size
                        
                        os.remove(log_file)
                        
                        self.report["compressed_files"].append({
                            "original_file": log_file,
                            "compressed_file": compressed_file,
                            "original_size": file_size,
                            "compressed_size": compressed_size,
                            "space_saved": space_saved
                        })
                        self.report["total_space_freed"] += space_saved
                        print(f"   📦 Compressed: {os.path.basename(log_file)} ({space_saved/1024:.1f}KB saved)")
                        
                except Exception as e:
                    error_msg = f"Failed to process {log_file}: {e}"
                    self.report["errors"].append(error_msg)
                    print(f"   ⚠️  {error_msg}")
    
    def clean_empty_directories(self):
        """清理空目录"""
        print("\n🧹 Cleaning empty directories...")
        
        cleaned_dirs = []
        
        # 从最深层开始清理
        for root, dirs, files in os.walk(self.project_root, topdown=False):
            # 跳过重要目录
            if any(important in root for important in ['.git', '__pycache__', 'backup']):
                continue
                
            try:
                # 如果目录为空，删除它
                if not dirs and not files:
                    os.rmdir(root)
                    cleaned_dirs.append(root)
                    print(f"   🗑️  Removed empty directory: {os.path.relpath(root, self.project_root)}")
            except Exception as e:
                error_msg = f"Failed to remove directory {root}: {e}"
                self.report["errors"].append(error_msg)
                print(f"   ⚠️  {error_msg}")
        
        self.report["cleaned_directories"] = len(cleaned_dirs)
    
    def organize_log_structure(self):
        """整理日志目录结构"""
        print("\n📁 Organizing log directory structure...")
        
        # 确保标准日志目录存在
        standard_log_dirs = [
            "logs/training",
            "logs/testing", 
            "logs/scheduler",
            "logs/analysis",
            "logs/system",
            "logs/archive"
        ]
        
        created_dirs = []
        
        for log_dir in standard_log_dirs:
            full_path = os.path.join(self.project_root, log_dir)
            if not os.path.exists(full_path):
                os.makedirs(full_path, exist_ok=True)
                created_dirs.append(log_dir)
                print(f"   📁 Created: {log_dir}")
        
        self.report["created_log_directories"] = created_dirs
    
    def archive_old_logs(self, archive_days=30):
        """归档旧日志"""
        print(f"\n📦 Archiving logs older than {archive_days} days...")
        
        cutoff_archive = datetime.now() - timedelta(days=archive_days)
        archive_dir = os.path.join(self.project_root, "logs", "archive")
        os.makedirs(archive_dir, exist_ok=True)
        
        archived_files = []
        
        log_patterns = [
            os.path.join(self.project_root, "logs", "*.log"),
            os.path.join(self.project_root, "logs", "*", "*.log")
        ]
        
        for pattern in log_patterns:
            for log_file in glob.glob(pattern, recursive=True):
                # 跳过已经在archive目录中的文件
                if "archive" in log_file:
                    continue
                    
                try:
                    file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                    
                    if file_time < cutoff_archive:
                        # 移动到归档目录
                        filename = os.path.basename(log_file)
                        archive_path = os.path.join(archive_dir, filename)
                        
                        # 如果文件已存在，添加时间戳
                        if os.path.exists(archive_path):
                            name, ext = os.path.splitext(filename)
                            timestamp = file_time.strftime("%Y%m%d_%H%M%S")
                            archive_path = os.path.join(archive_dir, f"{name}_{timestamp}{ext}")
                        
                        shutil.move(log_file, archive_path)
                        archived_files.append({
                            "original": log_file,
                            "archived": archive_path
                        })
                        print(f"   📦 Archived: {filename}")
                        
                except Exception as e:
                    error_msg = f"Failed to archive {log_file}: {e}"
                    self.report["errors"].append(error_msg)
                    print(f"   ⚠️  {error_msg}")
        
        self.report["archived_files"] = archived_files
    
    def generate_summary(self):
        """生成清理总结"""
        print("\n📊 Cleanup Summary:")
        print(f"   🗑️  Files deleted: {len(self.report['cleaned_files'])}")
        print(f"   📦 Files compressed: {len(self.report['compressed_files'])}")
        print(f"   📁 Directories cleaned: {self.report.get('cleaned_directories', 0)}")
        print(f"   📦 Files archived: {len(self.report.get('archived_files', []))}")
        print(f"   💾 Space freed: {self.report['total_space_freed'] / (1024*1024):.2f} MB")
        
        if self.report["errors"]:
            print(f"   ⚠️  Errors: {len(self.report['errors'])}")
    
    def save_report(self):
        """保存清理报告"""
        report_file = os.path.join(self.project_root, f"cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 Cleanup report saved to: {os.path.basename(report_file)}")
    
    def run_full_cleanup(self, delete_days=14, compress_days=7, archive_days=30):
        """运行完整的日志清理"""
        print("🧹 SeedVision v1 Log Cleanup")
        print("=" * 40)
        
        # 清理旧日志
        self.clean_old_logs(delete_days, compress_days)
        
        # 归档旧日志
        self.archive_old_logs(archive_days)
        
        # 整理目录结构
        self.organize_log_structure()
        
        # 清理空目录
        self.clean_empty_directories()
        
        # 生成总结
        self.generate_summary()
        
        # 保存报告
        self.save_report()
        
        print("\n🎉 Log cleanup completed!")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="SeedVision v1 Log Cleanup Tool")
    parser.add_argument("--delete-days", type=int, default=14, help="Delete logs older than N days")
    parser.add_argument("--compress-days", type=int, default=7, help="Compress logs older than N days")
    parser.add_argument("--archive-days", type=int, default=30, help="Archive logs older than N days")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be done without actually doing it")
    
    args = parser.parse_args()
    
    if args.dry_run:
        print("🔍 DRY RUN MODE - No files will be modified")
        # TODO: 实现dry run模式
        return
    
    cleaner = LogCleaner()
    cleaner.run_full_cleanup(args.delete_days, args.compress_days, args.archive_days)

if __name__ == "__main__":
    main()
