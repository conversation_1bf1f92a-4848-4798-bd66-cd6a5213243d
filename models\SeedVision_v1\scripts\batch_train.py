#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量训练脚本

功能：
1. 自动依次训练所有用户配置
2. 支持配置过滤和优先级排序
3. 自动保存训练参数和结果
4. 智能错误处理和恢复

使用方法：
python batch_train.py --all                    # 训练所有用户配置
python batch_train.py --filter 224x224         # 只训练224x224配置
python batch_train.py --filter norm            # 只训练带归一化的配置
python batch_train.py --filter high_lr         # 只训练高学习率配置
python batch_train.py --start-from 5           # 从第5个配置开始
python batch_train.py --dry-run               # 预览不执行
"""

import sys
import os
import yaml
import argparse
import time
import subprocess
from datetime import datetime
import json

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def load_config():
    """加载训练配置文件"""
    config_path = "config/training_config.yaml"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"{Colors.RED}[ERROR]{Colors.END} 无法加载配置文件: {e}")
        return None

def save_config(config):
    """保存训练配置文件"""
    config_path = "config/training_config.yaml"
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        return True
    except Exception as e:
        print(f"{Colors.RED}[ERROR]{Colors.END} 无法保存配置文件: {e}")
        return False

def get_user_configs(config, filter_text=None):
    """获取用户配置列表"""
    if not config or 'training_configs' not in config:
        return []
    
    training_configs = config['training_configs']
    user_configs = []
    
    for name, cfg in training_configs.items():
        if name.startswith('user_'):
            if not filter_text or filter_text.lower() in name.lower():
                user_configs.append((name, cfg))
    
    return user_configs

def estimate_training_time(config_name):
    """估算训练时间"""
    # 基础时间估算 (小时)
    base_time = 4.0
    
    # 根据输入尺寸调整
    if '224x224' in config_name:
        time_multiplier = 1.0
    elif '112x112' in config_name:
        time_multiplier = 0.7
    elif '80x80' in config_name:
        time_multiplier = 0.5
    elif '56x56' in config_name:
        time_multiplier = 0.3
    else:
        time_multiplier = 1.0
    
    # 根据学习率调整
    if 'very_low_lr' in config_name:
        lr_multiplier = 1.2
    elif 'low_lr' in config_name:
        lr_multiplier = 1.1
    else:
        lr_multiplier = 1.0
    
    return base_time * time_multiplier * lr_multiplier

def sort_configs_by_speed(user_configs):
    """按训练速度排序配置 (快的先执行)"""
    def sort_key(config_item):
        name, cfg = config_item
        estimated_time = estimate_training_time(name)
        
        # 优先级权重
        priority_weight = 0
        if '56x56' in name:
            priority_weight = 1000  # 最快的先执行
        elif '80x80' in name:
            priority_weight = 800
        elif '112x112' in name:
            priority_weight = 600
        elif '224x224' in name:
            priority_weight = 400
        
        # 学习率权重 (高学习率通常收敛更快)
        if 'high_lr' in name:
            lr_weight = 100
        elif 'mid_lr' in name:
            lr_weight = 80
        elif 'low_lr' in name:
            lr_weight = 60
        else:
            lr_weight = 40
        
        return -(priority_weight + lr_weight - estimated_time * 10)
    
    return sorted(user_configs, key=sort_key)

def enable_config(config, config_name):
    """启用指定配置"""
    training_configs = config['training_configs']
    
    # 禁用所有配置
    for name, cfg in training_configs.items():
        cfg['enable'] = False
    
    # 启用指定配置
    if config_name in training_configs:
        training_configs[config_name]['enable'] = True
        return True
    
    return False

def run_training(config_name):
    """运行训练"""
    print(f"\n{Colors.BLUE}[TRAINING]{Colors.END} 开始训练: {config_name}")
    print("=" * 80)
    
    start_time = datetime.now()
    
    try:
        # 运行训练脚本
        result = subprocess.run(
            [sys.executable, "train.py"],
            capture_output=True,
            text=True,
            timeout=28800  # 8小时超时
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds() / 3600
        
        if result.returncode == 0:
            print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 训练完成: {config_name}")
            print(f"耗时: {duration:.1f} 小时")
            return True, duration, None
        else:
            error_msg = result.stderr if result.stderr else "未知错误"
            print(f"{Colors.RED}[FAILED]{Colors.END} 训练失败: {config_name}")
            print(f"错误: {error_msg[:200]}...")
            return False, duration, error_msg
            
    except subprocess.TimeoutExpired:
        print(f"{Colors.YELLOW}[TIMEOUT]{Colors.END} 训练超时: {config_name}")
        return False, 8.0, "训练超时"
    except Exception as e:
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds() / 3600
        print(f"{Colors.RED}[ERROR]{Colors.END} 执行错误: {e}")
        return False, duration, str(e)

def save_batch_log(log_data, log_file="batch_training_log.json"):
    """保存批量训练日志"""
    log_dir = "logs/batch_training"
    os.makedirs(log_dir, exist_ok=True)
    
    log_path = os.path.join(log_dir, log_file)
    
    try:
        with open(log_path, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False, default=str)
        return log_path
    except Exception as e:
        print(f"{Colors.YELLOW}[WARNING]{Colors.END} 无法保存日志: {e}")
        return None

def print_training_plan(user_configs):
    """打印训练计划"""
    print(f"\n{Colors.BOLD}[TRAINING PLAN]{Colors.END} 批量训练计划")
    print("=" * 100)
    
    total_time = sum(estimate_training_time(name) for name, _ in user_configs)
    
    print(f"{Colors.CYAN}[SUMMARY]{Colors.END}")
    print(f"  总配置数: {len(user_configs)}")
    print(f"  预估总时间: {total_time:.1f} 小时 ({total_time/24:.1f} 天)")
    print(f"  平均每配置: {total_time/len(user_configs):.1f} 小时")
    print()
    
    print(f"{Colors.YELLOW}[EXECUTION ORDER]{Colors.END}")
    print(f"{'序号':<4} {'配置名':<35} {'预估时间':<10} {'描述':<40}")
    print("-" * 100)
    
    cumulative_time = 0
    for i, (name, cfg) in enumerate(user_configs, 1):
        estimated_time = estimate_training_time(name)
        cumulative_time += estimated_time
        
        description = cfg.get('description', '')[:38] + '...' if len(cfg.get('description', '')) > 40 else cfg.get('description', '')
        
        print(f"{i:<4} {name:<35} {estimated_time:<10.1f} {description:<40}")
    
    print("-" * 100)
    print(f"预估完成时间: {cumulative_time:.1f} 小时")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量训练用户配置')
    parser.add_argument('--all', action='store_true', help='训练所有用户配置')
    parser.add_argument('--filter', type=str, help='过滤配置 (例如: 224x224, norm, high_lr)')
    parser.add_argument('--start-from', type=int, default=1, help='从第N个配置开始 (1-based)')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际训练')
    parser.add_argument('--continue-on-error', action='store_true', help='出错时继续执行')
    
    args = parser.parse_args()
    
    # 检查参数
    if not args.all and not args.filter:
        print(f"{Colors.RED}[ERROR]{Colors.END} 请指定 --all 或 --filter 参数")
        return
    
    print(f"{Colors.BOLD}[BATCH TRAINER]{Colors.END} 批量训练系统")
    print("=" * 80)
    
    # 加载配置
    print(f"{Colors.BLUE}[LOADING]{Colors.END} 加载配置...")
    config = load_config()
    if not config:
        return
    
    # 获取用户配置
    user_configs = get_user_configs(config, args.filter)
    if not user_configs:
        print(f"{Colors.YELLOW}[WARNING]{Colors.END} 没有找到匹配的用户配置")
        return
    
    print(f"找到 {len(user_configs)} 个用户配置")
    
    # 排序配置
    user_configs = sort_configs_by_speed(user_configs)
    
    # 应用起始位置
    if args.start_from > 1:
        user_configs = user_configs[args.start_from-1:]
        print(f"从第 {args.start_from} 个配置开始")
    
    # 显示计划
    print_training_plan(user_configs)
    
    if args.dry_run:
        print(f"\n{Colors.YELLOW}[DRY RUN]{Colors.END} 预览模式，未实际执行训练")
        return
    
    # 确认执行
    print(f"\n{Colors.YELLOW}[CONFIRM]{Colors.END} 是否开始批量训练？")
    confirm = input("确认开始？ (y/N): ").strip().lower()
    if confirm != 'y':
        print(f"{Colors.YELLOW}[CANCELLED]{Colors.END} 操作已取消")
        return
    
    # 开始批量训练
    print(f"\n{Colors.GREEN}[STARTING]{Colors.END} 开始批量训练...")
    
    batch_start_time = datetime.now()
    results = []
    successful_count = 0
    failed_count = 0
    
    for i, (config_name, cfg) in enumerate(user_configs, 1):
        print(f"\n{Colors.BOLD}[PROGRESS]{Colors.END} 进度: {i}/{len(user_configs)}")
        print(f"当前配置: {config_name}")
        
        # 启用配置
        if not enable_config(config, config_name):
            print(f"{Colors.RED}[ERROR]{Colors.END} 无法启用配置: {config_name}")
            continue
        
        if not save_config(config):
            print(f"{Colors.RED}[ERROR]{Colors.END} 无法保存配置文件")
            continue
        
        # 运行训练
        success, duration, error_msg = run_training(config_name)
        
        # 记录结果
        result = {
            'config_name': config_name,
            'success': success,
            'duration_hours': duration,
            'error_message': error_msg,
            'timestamp': datetime.now().isoformat()
        }
        results.append(result)
        
        if success:
            successful_count += 1
        else:
            failed_count += 1
            if not args.continue_on_error:
                print(f"{Colors.RED}[STOPPING]{Colors.END} 训练失败，停止执行")
                break
        
        # 保存中间结果
        log_data = {
            'batch_start_time': batch_start_time.isoformat(),
            'total_configs': len(user_configs),
            'completed': i,
            'successful': successful_count,
            'failed': failed_count,
            'results': results
        }
        save_batch_log(log_data, f"batch_log_{batch_start_time.strftime('%Y%m%d_%H%M%S')}.json")
    
    # 最终统计
    batch_end_time = datetime.now()
    total_duration = (batch_end_time - batch_start_time).total_seconds() / 3600
    
    print(f"\n{Colors.BOLD}[COMPLETE]{Colors.END} 批量训练完成")
    print("=" * 80)
    print(f"总配置数: {len(user_configs)}")
    print(f"成功: {Colors.GREEN}{successful_count}{Colors.END}")
    print(f"失败: {Colors.RED}{failed_count}{Colors.END}")
    print(f"总耗时: {total_duration:.1f} 小时")
    print(f"平均耗时: {total_duration/len(results):.1f} 小时/配置" if results else "N/A")
    
    # 保存最终日志
    final_log_data = {
        'batch_start_time': batch_start_time.isoformat(),
        'batch_end_time': batch_end_time.isoformat(),
        'total_duration_hours': total_duration,
        'total_configs': len(user_configs),
        'successful': successful_count,
        'failed': failed_count,
        'results': results
    }
    
    log_path = save_batch_log(final_log_data, f"final_batch_log_{batch_start_time.strftime('%Y%m%d_%H%M%S')}.json")
    if log_path:
        print(f"\n详细日志已保存到: {log_path}")

if __name__ == "__main__":
    main()
