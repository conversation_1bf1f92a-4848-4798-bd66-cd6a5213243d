# 用户训练配置文档

## 📋 概述

根据用户需求，已创建完整的训练配置系统，支持以下规格：

- **数据采集**: 20张原图，每个图60个样本 (总计1200个样本)
- **Transform尺寸**: 224×224, 112×112, 80×80, 56×56
- **归一化选项**: 带归一化 / 不带归一化
- **学习率范围**: 0.01 ~ 0.00001 (四个级别)
- **优化器**: Adam
- **参数保存**: 每次训练自动保存参数到输出目录

## 🎯 配置总览

### 已创建的配置数量
- **总配置数**: 32个 (4尺寸 × 2归一化 × 4学习率)
- **224×224配置**: 8个 (已手动创建)
- **112×112配置**: 8个 (已自动生成)
- **80×80配置**: 8个 (已自动生成)
- **56×56配置**: 8个 (已自动生成)

### 配置命名规则
```
user_{尺寸}_{归一化}_{学习率级别}

例如:
- user_224x224_norm_high_lr     (224×224, 带归一化, 高学习率0.01)
- user_112x112_no_norm_low_lr   (112×112, 不带归一化, 低学习率0.0001)
```

## 📊 详细配置参数

### 数据采样配置
```yaml
dataset_config:
  sampling_strategy: "original_level_sampling"
  strategy_parameters:
    target_originals: 20      # 20张原图
    samples_per_original: 60  # 每个原图60个样本
    total_samples: 1200       # 总计1200个样本
    cross_subset_sampling: true
    train_ratio: 0.8          # 训练集比例 (48样本/original)
    val_ratio: 0.1            # 验证集比例 (6样本/original)
    test_ratio: 0.1           # 测试集比例 (6样本/original)
```

### Transform配置
| 尺寸 | 带归一化 | 不带归一化 |
|------|----------|------------|
| 224×224 | `224x224_norm` | `224x224_no_norm` |
| 112×112 | `112x112_norm` | `112x112_no_norm` |
| 80×80 | `80x80_norm` | `80x80_no_norm` |
| 56×56 | `56x56_norm` | `56x56_no_norm` |

### 学习率配置
| 级别 | 学习率 | 配置名 | 调度器 |
|------|--------|--------|--------|
| 高 | 0.01 | `user_high_lr` | ReduceLROnPlateau |
| 中 | 0.001 | `user_mid_lr` | StepLR |
| 低 | 0.0001 | `user_low_lr` | CosineAnnealingLR |
| 极低 | 0.00001 | `user_very_low_lr` | ReduceLROnPlateau |

### 模型参数 (根据输入尺寸自适应)
| 尺寸 | embed_dim | depths | patch_size | batch_size | 显存估计 |
|------|-----------|--------|------------|------------|----------|
| 224×224 | 192 | [3,4,18,3] | 4 | 32 | 1.2GB |
| 112×112 | 128 | [2,3,12,2] | 2 | 48 | 0.8GB |
| 80×80 | 96 | [2,3,8,2] | 2 | 64 | 0.6GB |
| 56×56 | 64 | [2,2,6,2] | 1 | 80 | 0.4GB |

## 🛠️ 使用方法

### 1. 查看可用配置
```bash
# 查看所有用户配置
python scripts/config_manager.py list-user

# 查看特定配置详情
python scripts/config_manager.py show user_224x224_norm_high_lr
```

### 2. 启用配置
```bash
# 启用特定配置 (会自动禁用其他配置)
python scripts/config_manager.py enable user_224x224_norm_high_lr
```

### 3. 运行训练
```bash
# 使用启用的配置运行训练
python train.py

# 或使用调度器运行
python main_scheduler.py
```

### 4. 查看训练结果
训练完成后，结果保存在 `output/training/results/` 目录下，包括：
- **模型文件**: `best_model.pth`, `final_model.pth`
- **训练参数**: `training_params/training_params_YYYYMMDD_HHMMSS.json`
- **可视化结果**: 损失曲线、R²曲线、回归图
- **日志文件**: 完整的训练日志

## 📁 输出目录结构
```
output/training/results/{task_name}/
├── best_model.pth                    # 最佳模型
├── final_model.pth                   # 最终模型
├── training_params/                  # 训练参数目录
│   ├── training_params_YYYYMMDD_HHMMSS.json
│   └── training_params_YYYYMMDD_HHMMSS.yaml
├── visualizations/                   # 可视化结果
│   ├── loss_curve_YYYYMMDD_HHMMSS.png
│   ├── oil_r2_curve_YYYYMMDD_HHMMSS.png
│   ├── protein_r2_curve_YYYYMMDD_HHMMSS.png
│   ├── oil_regression_YYYYMMDD_HHMMSS.png
│   └── protein_regression_YYYYMMDD_HHMMSS.png
└── logs/                            # 日志文件
    └── training_YYYYMMDD_HHMMSS.log
```

## 🔧 配置管理工具

### config_manager.py 功能
- `list`: 列出所有配置
- `list-user`: 列出用户配置
- `show <config_name>`: 显示配置详情
- `enable <config_name>`: 启用配置
- `disable <config_name>`: 禁用配置

### 示例配置组合推荐
1. **快速测试**: `user_56x56_no_norm_high_lr` (最快训练)
2. **标准训练**: `user_224x224_norm_mid_lr` (平衡性能)
3. **精细调优**: `user_224x224_norm_very_low_lr` (最佳质量)
4. **资源受限**: `user_80x80_norm_low_lr` (中等资源)

## 📈 训练参数自动保存

每次训练时，如果配置中设置了 `save_params: true`，系统会自动保存：
- 完整的训练配置参数
- 模型架构参数
- 超参数设置
- 数据集配置
- Transform配置
- 训练元信息 (时间戳、任务名等)

保存格式：JSON 和 YAML 两种格式，便于查看和复现实验。

## 🎯 下一步操作

1. 选择合适的配置并启用
2. 运行训练验证配置是否正常工作
3. 根据需要调整参数或创建新配置
4. 使用调度器进行批量训练实验
