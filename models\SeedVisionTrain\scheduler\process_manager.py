#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进程管理器 - Process Manager

功能：
1. 启动和管理训练进程
2. 监控进程状态和资源使用
3. 处理进程异常和重启
4. 提供进程间通信机制
"""

import os
import sys
import subprocess
import psutil
import signal
import time
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import logging

class ProcessStatus(Enum):
    """进程状态枚举"""
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    STOPPED = "stopped"
    FAILED = "failed"
    KILLED = "killed"

@dataclass
class ProcessInfo:
    """进程信息数据类"""
    process_id: int
    task_id: str
    command: List[str]
    status: ProcessStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    
    # 资源使用
    cpu_percent: float = 0.0
    memory_mb: float = 0.0
    gpu_memory_mb: float = 0.0
    
    # 进程对象
    process: Optional[subprocess.Popen] = None
    psutil_process: Optional[psutil.Process] = None
    
    # 输出文件
    stdout_file: Optional[str] = None
    stderr_file: Optional[str] = None
    
    # 回调函数
    on_status_change: Optional[Callable] = None

class ProcessManager:
    """进程管理器"""
    
    def __init__(self, log_dir: str = "logs", max_processes: int = 4):
        """
        初始化进程管理器
        
        参数:
            log_dir: 日志目录
            max_processes: 最大进程数
        """
        self.log_dir = log_dir
        self.max_processes = max_processes
        self.logger = logging.getLogger(__name__)
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 进程字典
        self.processes: Dict[int, ProcessInfo] = {}
        self.task_to_process: Dict[str, int] = {}
        
        # 监控线程
        self.monitor_thread: Optional[threading.Thread] = None
        self.is_monitoring = False
        self.lock = threading.Lock()
        
        # 统计信息
        self.total_processes_started = 0
        self.total_processes_completed = 0
        self.total_processes_failed = 0
    
    def start_training_process(self, task_id: str, config: Dict, 
                             working_dir: str = None) -> Optional[int]:
        """
        启动训练进程
        
        参数:
            task_id: 任务ID
            config: 训练配置
            working_dir: 工作目录
            
        返回:
            进程ID或None
        """
        try:
            # 检查进程数量限制
            if len(self.processes) >= self.max_processes:
                self.logger.error(f"Process limit reached: {self.max_processes}")
                return None
            
            # 构建命令
            command = self._build_training_command(task_id, config)
            
            # 设置工作目录
            if working_dir is None:
                working_dir = os.getcwd()
            
            # 创建输出文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            stdout_file = os.path.join(self.log_dir, f"{task_id}_{timestamp}.out")
            stderr_file = os.path.join(self.log_dir, f"{task_id}_{timestamp}.err")
            
            # 启动进程
            with open(stdout_file, 'w') as stdout, open(stderr_file, 'w') as stderr:
                process = subprocess.Popen(
                    command,
                    cwd=working_dir,
                    stdout=stdout,
                    stderr=stderr,
                    env=os.environ.copy()
                )
            
            # 创建进程信息
            process_info = ProcessInfo(
                process_id=process.pid,
                task_id=task_id,
                command=command,
                status=ProcessStatus.STARTING,
                start_time=datetime.now(),
                process=process,
                stdout_file=stdout_file,
                stderr_file=stderr_file
            )
            
            # 创建psutil进程对象
            try:
                process_info.psutil_process = psutil.Process(process.pid)
            except psutil.NoSuchProcess:
                self.logger.warning(f"Could not create psutil process for PID {process.pid}")
            
            # 添加到管理字典
            with self.lock:
                self.processes[process.pid] = process_info
                self.task_to_process[task_id] = process.pid
                self.total_processes_started += 1
            
            # 启动监控
            if not self.is_monitoring:
                self.start_monitoring()
            
            self.logger.info(f"Started training process for task {task_id}, PID: {process.pid}")
            return process.pid
            
        except Exception as e:
            self.logger.error(f"Failed to start training process for task {task_id}: {e}")
            return None
    
    def _build_training_command(self, task_id: str, config: Dict) -> List[str]:
        """
        构建训练命令
        
        参数:
            task_id: 任务ID
            config: 训练配置
            
        返回:
            命令列表
        """
        # 基础命令
        command = [sys.executable, "main.py"]
        
        # 添加任务ID
        command.extend(["--task_id", task_id])
        
        # 添加配置文件路径
        config_file = f"temp_config_{task_id}.json"
        config_path = os.path.join(self.log_dir, config_file)
        
        # 保存临时配置文件
        with open(config_path, 'w') as f:
            json.dump(config, f, indent=2)
        
        command.extend(["--config", config_path])
        
        # 添加其他参数
        if config.get('sequential', False):
            command.append("--sequential")
        
        max_memory = config.get('max_memory', 8.0)
        command.extend(["--max_memory", str(max_memory)])
        
        return command
    
    def stop_process(self, process_id: int, force: bool = False) -> bool:
        """
        停止进程
        
        参数:
            process_id: 进程ID
            force: 是否强制终止
            
        返回:
            是否成功停止
        """
        with self.lock:
            if process_id not in self.processes:
                self.logger.warning(f"Process {process_id} not found")
                return False
            
            process_info = self.processes[process_id]
            
            try:
                if process_info.psutil_process and process_info.psutil_process.is_running():
                    if force:
                        process_info.psutil_process.kill()
                        process_info.status = ProcessStatus.KILLED
                    else:
                        process_info.psutil_process.terminate()
                        process_info.status = ProcessStatus.STOPPING
                    
                    self.logger.info(f"Process {process_id} {'killed' if force else 'terminated'}")
                    return True
                else:
                    self.logger.warning(f"Process {process_id} is not running")
                    return False
                    
            except psutil.NoSuchProcess:
                process_info.status = ProcessStatus.STOPPED
                self.logger.info(f"Process {process_id} already stopped")
                return True
            except Exception as e:
                self.logger.error(f"Failed to stop process {process_id}: {e}")
                return False
    
    def stop_task(self, task_id: str, force: bool = False) -> bool:
        """
        停止任务对应的进程
        
        参数:
            task_id: 任务ID
            force: 是否强制终止
            
        返回:
            是否成功停止
        """
        with self.lock:
            if task_id not in self.task_to_process:
                self.logger.warning(f"Task {task_id} not found")
                return False
            
            process_id = self.task_to_process[task_id]
            return self.stop_process(process_id, force)
    
    def get_process_status(self, process_id: int) -> Optional[ProcessInfo]:
        """
        获取进程状态
        
        参数:
            process_id: 进程ID
            
        返回:
            进程信息或None
        """
        with self.lock:
            return self.processes.get(process_id)
    
    def get_task_process(self, task_id: str) -> Optional[ProcessInfo]:
        """
        获取任务对应的进程信息
        
        参数:
            task_id: 任务ID
            
        返回:
            进程信息或None
        """
        with self.lock:
            if task_id in self.task_to_process:
                process_id = self.task_to_process[task_id]
                return self.processes.get(process_id)
            return None
    
    def list_processes(self) -> List[ProcessInfo]:
        """
        列出所有进程
        
        返回:
            进程信息列表
        """
        with self.lock:
            return list(self.processes.values())
    
    def start_monitoring(self):
        """启动进程监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("Process monitoring started")
    
    def stop_monitoring(self):
        """停止进程监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        self.logger.info("Process monitoring stopped")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                self._update_process_status()
                time.sleep(5)  # 每5秒更新一次
            except Exception as e:
                self.logger.error(f"Monitor loop error: {e}")
                time.sleep(10)
    
    def _update_process_status(self):
        """更新进程状态"""
        with self.lock:
            completed_processes = []
            
            for process_id, process_info in self.processes.items():
                try:
                    if process_info.psutil_process:
                        # 检查进程是否还在运行
                        if process_info.psutil_process.is_running():
                            # 更新资源使用情况
                            process_info.cpu_percent = process_info.psutil_process.cpu_percent()
                            memory_info = process_info.psutil_process.memory_info()
                            process_info.memory_mb = memory_info.rss / (1024 * 1024)
                            
                            # 更新状态
                            if process_info.status == ProcessStatus.STARTING:
                                process_info.status = ProcessStatus.RUNNING
                                if process_info.on_status_change:
                                    process_info.on_status_change(process_info)
                        else:
                            # 进程已结束
                            process_info.end_time = datetime.now()
                            return_code = process_info.process.poll()
                            
                            if return_code == 0:
                                process_info.status = ProcessStatus.STOPPED
                                self.total_processes_completed += 1
                            else:
                                process_info.status = ProcessStatus.FAILED
                                self.total_processes_failed += 1
                            
                            completed_processes.append(process_id)
                            
                            if process_info.on_status_change:
                                process_info.on_status_change(process_info)
                    
                except psutil.NoSuchProcess:
                    # 进程不存在
                    process_info.status = ProcessStatus.STOPPED
                    process_info.end_time = datetime.now()
                    completed_processes.append(process_id)
                    
                except Exception as e:
                    self.logger.error(f"Error monitoring process {process_id}: {e}")
            
            # 清理已完成的进程
            for process_id in completed_processes:
                process_info = self.processes[process_id]
                task_id = process_info.task_id
                
                # 从字典中移除
                del self.processes[process_id]
                if task_id in self.task_to_process:
                    del self.task_to_process[task_id]
                
                self.logger.info(f"Process {process_id} (task {task_id}) completed with status {process_info.status.value}")
    
    def cleanup_logs(self, days_old: int = 7):
        """
        清理旧日志文件
        
        参数:
            days_old: 删除多少天前的日志
        """
        try:
            cutoff_time = time.time() - (days_old * 24 * 3600)
            
            for filename in os.listdir(self.log_dir):
                filepath = os.path.join(self.log_dir, filename)
                if os.path.isfile(filepath) and os.path.getmtime(filepath) < cutoff_time:
                    os.remove(filepath)
                    self.logger.info(f"Removed old log file: {filename}")
                    
        except Exception as e:
            self.logger.error(f"Error cleaning up logs: {e}")
    
    def get_system_info(self) -> Dict:
        """
        获取系统信息
        
        返回:
            系统信息字典
        """
        return {
            'total_processes': len(self.processes),
            'max_processes': self.max_processes,
            'monitoring': self.is_monitoring,
            'statistics': {
                'total_started': self.total_processes_started,
                'total_completed': self.total_processes_completed,
                'total_failed': self.total_processes_failed
            }
        }
    
    def generate_process_report(self) -> Dict:
        """
        生成进程报告
        
        返回:
            进程报告
        """
        processes_info = []
        
        with self.lock:
            for process_info in self.processes.values():
                duration = None
                if process_info.end_time:
                    duration = (process_info.end_time - process_info.start_time).total_seconds()
                else:
                    duration = (datetime.now() - process_info.start_time).total_seconds()
                
                processes_info.append({
                    'process_id': process_info.process_id,
                    'task_id': process_info.task_id,
                    'status': process_info.status.value,
                    'start_time': process_info.start_time.isoformat(),
                    'duration_seconds': duration,
                    'cpu_percent': process_info.cpu_percent,
                    'memory_mb': process_info.memory_mb,
                    'command': ' '.join(process_info.command)
                })
        
        return {
            'timestamp': datetime.now().isoformat(),
            'system_info': self.get_system_info(),
            'processes': processes_info
        }
