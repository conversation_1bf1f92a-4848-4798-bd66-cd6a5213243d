'''
FasterNet多进程训练启动脚本
'''

import os
import sys
import argparse

def main():
    parser = argparse.ArgumentParser(description='启动FasterNet多进程训练')
    parser.add_argument('--gpu', type=int, default=0, help='GPU ID')
    parser.add_argument('--max_memory', type=float, default=8.0, help='最大GPU显存限制(GB)')
    parser.add_argument('--num_models', type=int, default=8, help='要训练的模型数量')
    args = parser.parse_args()
    
    # 运行训练脚本
    cmd = f"python models/SeedVision_v1/main.py --gpu {args.gpu} --max_memory {args.max_memory}"
    print(f"执行命令: {cmd}")
    os.system(cmd)

if __name__ == "__main__":
    main()
