# 🤖 新AI助手接手指南

## 🚨 **首次接手必做事项**

### 1. 环境验证 (必须执行)
```bash
# 确认工作目录
cd models/SeedVision_v1

# 快速系统验证 (30秒)
python quick_test.py quick

# 如果失败，运行完整诊断
python quick_test.py full
```

### 2. 项目状态了解
```bash
# 查看项目结构
ls -la

# 检查配置状态
python -c "from config.config_loader import ConfigLoader; cl=ConfigLoader(); configs=cl.get_enabled_training_configs(); print(f'启用配置: {len(configs)}个'); [print(f'- {c[\"name\"]}') for c in configs]"

# 检查数据连接
python -c "from tools.data.load_data import load_data; data=load_data(); print(f'数据库: {len(data)} 条记录')"
```

## 📋 **用户常见请求处理**

### "运行一个训练"
```bash
# 默认操作
python train.py
# 结果自动保存在 output/training/
```

### "查看训练结果"
```bash
# 查看最新结果
ls -la output/training/results/ | tail -5
ls -la output/training/visualizations/ | tail -5
```

### "修改配置"
```bash
# 查看配置
cat config/training_config.yaml | head -50

# 编辑配置
nano config/training_config.yaml

# 验证配置
python -c "from config.config_loader import ConfigLoader; ConfigLoader().validate_config(); print('配置正确')"
```

### "系统有问题"
```bash
# 标准诊断流程
python quick_test.py quick
nvidia-smi
python -c "from tools.data.load_data import load_data; load_data()"
```

## 🎯 **项目核心信息**

### 项目状态
- ✅ **生产就绪**: 100%测试通过
- 🎯 **核心功能**: 智能调度、Original级别评估、高质量可视化
- 📍 **最新修复**: 可视化文本位置固定在(0.02, 0.85)，无重叠
- 📊 **数据要求**: 20张原图，每图60个样本

### 重要文件位置
- **配置**: `config/training_config.yaml`
- **训练**: `train.py`, `train_scheduler.py`
- **测试**: `quick_test.py`
- **结果**: `output/training/`
- **日志**: `logs/`

### 核心功能
1. **智能训练调度**: 多任务并行，GPU内存管理
2. **Original级别评估**: 原图级别的预测准确性评估
3. **双级别可视化**: Sample级别和Original级别回归图
4. **配置化训练**: YAML配置文件管理所有参数

## 🔧 **故障排除**

### 系统无法启动
```bash
python quick_test.py quick
python -c "import sys; print(sys.path)"
```

### 训练失败
```bash
nvidia-smi
python -c "from tools.data.load_data import load_data; load_data()"
```

### 配置错误
```bash
python -c "from config.config_loader import ConfigLoader; ConfigLoader().validate_config()"
```

## 💡 **开发原则**

### 修改代码前
1. 运行 `python quick_test.py quick`
2. 确认当前功能正常
3. 小步骤修改，立即测试

### 测试策略
1. **功能测试**: `quick_test.py`
2. **集成测试**: 完整训练流程
3. **回归测试**: 确保不破坏现有功能

### 调试技巧
1. 查看 `logs/` 目录日志
2. 使用 `nvidia-smi` 监控GPU
3. 分步验证各个模块

## 🚨 **重要提醒**

### 必须知道的事实
- 项目已完成，功能稳定
- 所有测试100%通过
- 可视化问题已修复
- Original级别评估正常工作
- 支持多种数据采样策略

### 紧急情况
- **系统崩溃**: `python quick_test.py quick`
- **GPU问题**: `nvidia-smi`
- **数据问题**: 检查数据库连接
- **配置问题**: 验证YAML语法

---

**🎯 记住: 任何操作前先运行 `python quick_test.py quick` 验证系统状态！**
