# 📁 SeedVision v1 项目结构

## 🎯 项目概览

SeedVision v1 是一个企业级深度学习训练管理系统，具备智能任务调度、多策略数据采样、Original级别评估和完整的可视化功能。

## 📂 目录结构

```
SeedVision_v1/
├── 📄 README.md                    # 项目主文档
├── 📄 PROJECT_COMPLETE.md          # 项目完成总结
├── 📄 PROJECT_STRUCTURE.md         # 本文档
├── 📄 organization_report_*.json   # 项目整理报告
│
├── 📁 config/                      # 配置文件
│   ├── 📄 training_config.yaml     # 训练配置
│   ├── 📄 config_loader.py         # 配置加载器
│   ├── 📄 output_config.py         # 输出路径配置
│   ├── 📄 CONFIG_GUIDE.md          # 配置指南
│   └── 📄 SAMPLING_STRATEGIES.md   # 采样策略文档
│
├── 📁 models/                      # 模型定义
│   ├── 📄 __init__.py
│   ├── 📄 FasterNet.py             # FasterNet模型
│   ├── 📄 Mixed_YOLO_FasterNet.py  # 混合模型
│   ├── 📄 Simple_Mixed.py          # 简化混合模型
│   └── 📄 fasternet_blocks.py      # FasterNet组件
│
├── 📁 tools/                       # 工具模块
│   ├── 📄 train_utils.py           # 训练工具
│   ├── 📁 data/                    # 数据处理
│   │   ├── 📄 __init__.py
│   │   ├── 📄 load_data.py         # 数据加载
│   │   ├── 📄 data_process1.py     # 数据处理1
│   │   ├── 📄 data_process2.py     # 数据处理2
│   │   └── 📄 redistribute_data.py # 数据重分布
│   ├── 📁 training/                # 训练模块
│   │   ├── 📄 __init__.py
│   │   ├── 📄 train.py             # 主训练脚本
│   │   ├── 📄 train.py.backup      # 训练脚本备份
│   │   ├── 📄 validate.py          # 验证模块
│   │   └── 📄 visualize.py         # 可视化模块
│   └── 📁 analysis/                # 分析工具
│       ├── 📄 __init__.py
│       ├── 📄 analyze_original_distribution.py
│       └── 📁 visualization_results/
│
├── 📁 scheduler/                   # 任务调度系统
│   ├── 📄 __init__.py
│   ├── 📄 README.md                # 调度器文档
│   ├── 📄 task_scheduler.py        # 任务调度器
│   ├── 📄 enhanced_scheduler.py    # 增强调度器
│   ├── 📄 process_manager.py       # 进程管理
│   ├── 📄 resource_estimator.py    # 资源估算
│   └── 📄 example_configs.json     # 示例配置
│
├── 📁 tests/                       # 测试系统
│   ├── 📄 __init__.py
│   ├── 📄 README.md                # 测试文档
│   ├── 📁 unit/                    # 单元测试
│   │   ├── 📄 __init__.py
│   │   ├── 📄 test_config.py
│   │   ├── 📄 test_data_loading.py
│   │   └── 📄 test_validation_fix.py
│   ├── 📁 integration/             # 集成测试
│   │   ├── 📄 __init__.py
│   │   ├── 📄 test_cross_subset_sampling.py
│   │   ├── 📄 test_data_format_fix.py
│   │   ├── 📄 test_text_position_fix.py
│   │   └── 📄 test_visualization_fix.py
│   ├── 📁 examples/                # 示例测试
│   │   ├── 📄 __init__.py
│   │   ├── 📄 final_verification.py
│   │   ├── 📄 scheduler_example.py
│   │   ├── 📄 simple_original_test.py
│   │   ├── 📄 test_original_evaluation.py
│   │   └── 📄 test_training_with_original_eval.py
│   ├── 📁 debug/                   # 调试测试
│   │   ├── 📄 __init__.py
│   │   └── 📄 debug_data_format.py
│   └── 📁 scripts/                 # 测试脚本
│       ├── 📄 quick_test.py
│       ├── 📄 system_test.py
│       ├── 📄 training_test.py
│       ├── 📄 run_tests.py
│       ├── 📄 test_basic_scheduler.py
│       ├── 📄 test_enhanced_scheduler.py
│       ├── 📄 test_improved_visualization.py
│       ├── 📄 test_organization.py
│       └── 📄 test_visualization.py
│
├── 📁 scripts/                     # 执行脚本
│   ├── 📁 training/                # 训练脚本
│   │   ├── 📄 main.py              # 主训练入口
│   │   └── 📄 main_scheduler.py    # 调度器入口
│   ├── 📁 testing/                 # 测试脚本
│   ├── 📁 analysis/                # 分析脚本
│   └── 📁 utilities/               # 工具脚本
│       ├── 📄 model_loader.py      # 模型加载器
│       ├── 📄 fix_print_to_logger.py
│       └── 📄 organize_project.py  # 项目整理脚本
│
├── 📁 docs/                        # 文档目录
│   ├── 📄 README.md                # 文档索引
│   ├── 📄 design.md                # 设计文档
│   ├── 📄 PROJECT_STATUS.md        # 项目状态
│   ├── 📄 FILE_ORGANIZATION.md     # 文件组织
│   ├── 📄 FILE_ORGANIZATION_COMPLETE.md
│   ├── 📄 OPTIMIZATION_SUMMARY.md  # 优化总结
│   ├── 📄 ORIGINAL_LEVEL_EVALUATION.md
│   ├── 📄 SAMPLING_STRATEGY_REFACTOR.md
│   ├── 📄 SCHEDULER_SUMMARY.md     # 调度器总结
│   ├── 📄 TESTING_COMPLETE.md      # 测试完成文档
│   └── 📄 TESTING_GUIDE.md         # 测试指南
│
├── 📁 output/                      # 标准化输出目录
│   ├── 📁 training/                # 训练输出
│   │   ├── 📁 models/              # 训练模型
│   │   ├── 📁 results/             # 训练结果
│   │   ├── 📁 logs/                # 训练日志
│   │   ├── 📁 visualizations/      # 训练可视化
│   │   └── 📁 checkpoints/         # 训练检查点
│   ├── 📁 testing/                 # 测试输出
│   │   ├── 📁 results/             # 测试结果
│   │   ├── 📁 logs/                # 测试日志
│   │   ├── 📁 visualizations/      # 测试可视化
│   │   └── 📁 reports/             # 测试报告
│   ├── 📁 scheduler/               # 调度器输出
│   │   ├── 📁 logs/                # 调度器日志
│   │   ├── 📁 reports/             # 调度器报告
│   │   └── 📁 tasks/               # 任务日志
│   ├── 📁 analysis/                # 分析输出
│   │   ├── 📁 data/                # 分析数据
│   │   ├── 📁 visualizations/      # 分析可视化
│   │   └── 📁 reports/             # 分析报告
│   └── 📁 temp/                    # 临时文件
│       ├── 📁 configs/             # 临时配置
│       ├── 📁 cache/               # 缓存文件
│       └── 📁 logs/                # 临时日志
│
├── 📁 logs/                        # 系统日志
│   ├── 📄 deep_learning_framework_*.log
│   ├── 📁 scheduler/               # 调度器日志
│   ├── 📁 tasks/                   # 任务日志
│   └── 📁 test/                    # 测试日志
│
└── 📁 backup/                      # 备份文件
```

## 🎯 核心功能模块

### 1. **训练系统** (`tools/training/`)
- **train.py**: 主训练脚本，支持多种配置和模式
- **validate.py**: 验证和评估模块
- **visualize.py**: 可视化生成模块

### 2. **任务调度系统** (`scheduler/`)
- **task_scheduler.py**: 基础任务调度器
- **enhanced_scheduler.py**: 增强调度器管理
- **process_manager.py**: 进程管理
- **resource_estimator.py**: 资源估算

### 3. **数据处理系统** (`tools/data/`)
- **load_data.py**: 数据加载和采样
- **redistribute_data.py**: 数据重分布
- **data_process*.py**: 数据预处理

### 4. **配置系统** (`config/`)
- **training_config.yaml**: 训练配置文件
- **config_loader.py**: 配置加载器
- **output_config.py**: 输出路径管理

### 5. **测试系统** (`tests/`)
- **unit/**: 单元测试
- **integration/**: 集成测试
- **examples/**: 示例和验证
- **scripts/**: 测试脚本

## 🚀 快速开始

### 基础训练
```bash
cd scripts/training
python main.py
```

### 调度器训练
```bash
cd scripts/training
python main_scheduler.py
```

### 运行测试
```bash
cd tests/scripts
python run_tests.py
```

### 项目整理
```bash
cd scripts/utilities
python organize_project.py
```

## 📊 文件统计

- **总文件数**: 121个
- **总大小**: 1.72 MB
- **目录数**: 25个
- **Python文件**: 85个
- **文档文件**: 15个
- **配置文件**: 3个

## 🎉 项目特色

### ✅ **企业级架构**
- 清晰的模块分离
- 标准化的输出管理
- 完整的测试覆盖

### ✅ **智能调度系统**
- 自动任务测试
- 动态资源管理
- 智能任务过滤

### ✅ **高质量可视化**
- 智能文本位置
- 标准化输出路径
- 多种图表类型

### ✅ **灵活配置系统**
- YAML配置文件
- 多策略数据采样
- 可扩展的超参数

### ✅ **完整测试体系**
- 单元测试
- 集成测试
- 系统测试
- 性能测试

## 📝 维护说明

### 定期清理
- 运行 `organize_project.py` 清理临时文件
- 定期清理旧日志文件
- 备份重要的训练结果

### 添加新功能
1. 在相应模块目录添加代码
2. 在 `tests/` 目录添加测试
3. 更新配置文件
4. 更新文档

### 版本管理
- 重要更改前创建备份
- 使用语义化版本号
- 维护更新日志

---

**SeedVision v1** - 企业级深度学习训练管理系统 🚀
