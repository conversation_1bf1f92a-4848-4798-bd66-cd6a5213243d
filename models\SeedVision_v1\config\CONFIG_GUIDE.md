# SeedVision v1 配置指南

## 📋 概述

本文档详细说明 `training_config.yaml` 中所有可配置的选项，帮助您根据需求选择和调整训练配置。

## 🎯 快速开始

### 当前推荐配置
```yaml
# 启用Original级别采样配置
config_original_balanced_224x224:
  enable: true  # ✅ 当前启用
```

### 运行训练
```bash
python main.py --sequential --max_memory 8.0
```

## 📊 采样策略配置

### 1. **Original级别采样** (推荐) ⭐⭐⭐⭐⭐

```yaml
original_level_sampling:
  strategy_type: "original_based"
  parameters:
    target_originals: 40      # 选择40个original
    samples_per_original: 60  # 每个original采样60张图片
    total_samples: 2400       # 总计2400张图片
    cross_subset_sampling: true  # 跨子集采样
    train_ratio: 0.8          # 训练集80% (48张/original)
    val_ratio: 0.1            # 验证集10% (6张/original)
    test_ratio: 0.1           # 测试集10% (6张/original)
```

**特点**：
- ✅ 完全实现，支持Original级别评估
- 🎯 确保每个original有相同数量的样本
- 📊 提供样本级别和Original级别双重R²评估
- 🌐 支持跨子集采样，数据分布更均匀

**适用场景**：
- 需要Original级别评估的场景
- 数据分布不均匀的情况
- 需要确保模型对不同original的泛化能力

### 2. **平衡采样** ⭐⭐⭐⭐

```yaml
balanced_sampling:
  strategy_type: "balanced"
  parameters:
    samples_per_group: 100    # 每组最大样本数
    max_groups: 45           # 最大组数
    total_samples: 4500      # 总样本数
```

**特点**：
- ✅ 完全实现
- 🎯 从每个original组中平衡采样
- 📊 自动计算每个组的采样数量

**适用场景**：
- 快速训练和实验
- 不需要Original级别评估的场景
- 数据量较大的情况

### 3. **随机采样** ⭐⭐⭐

```yaml
random_sampling:
  strategy_type: "random"
  parameters:
    sample_size: 4500        # 采样数量
    seed: 42                 # 随机种子
```

**特点**：
- ⚠️ 基础实现（需要完善）
- 🎲 完全随机选择样本
- ⚡ 简单快速

**适用场景**：
- 快速原型验证
- 基准测试
- 数据分布相对均匀的情况

### 4. **分层采样** ⭐⭐

```yaml
stratified_sampling:
  strategy_type: "stratified"
  parameters:
    maintain_distribution: true    # 保持原始分布
    total_samples: 4500           # 总样本数
    min_samples_per_stratum: 10   # 每层最小样本数
```

**特点**：
- ❌ 待实现
- 📊 保持原始数据的标签分布
- 📈 适合不平衡数据集

### 5. **时间序列采样** ⭐⭐

```yaml
temporal_sampling:
  strategy_type: "temporal"
  parameters:
    time_window: "recent"     # recent, random, sequential
    window_size: 30          # 时间窗口大小（天）
    total_samples: 3000      # 总样本数
```

**特点**：
- ❌ 待实现
- ⏰ 基于时间顺序采样
- 🔄 适合时序相关的数据

## 🔧 超参数配置

### 1. **保守型配置** (推荐新手)

```yaml
conservative:
  learning_rate: 0.0001        # 很小的学习率
  weight_decay: 0.0001         # 轻微正则化
  optimizer: "Adam"            # Adam优化器
  scheduler: "ReduceLROnPlateau"  # 自适应学习率
  gradient_clip: 1.0           # 梯度裁剪
```

**适用场景**：
- 初始训练
- 模型不稳定时
- 数据质量不确定时

### 2. **标准型配置** (推荐一般使用)

```yaml
standard:
  learning_rate: 0.001         # 标准学习率
  weight_decay: 0.00001        # 很轻的正则化
  optimizer: "Adam"            # Adam优化器
  scheduler: "StepLR"          # 阶梯式学习率
  gradient_clip: null          # 不使用梯度裁剪
```

**适用场景**：
- 大多数训练场景
- 数据质量较好时
- 平衡收敛速度和稳定性

### 3. **激进型配置** (快速收敛)

```yaml
aggressive:
  learning_rate: 0.01          # 较大学习率
  weight_decay: 0.001          # 较强正则化
  optimizer: "SGD"             # SGD优化器
  scheduler: "CosineAnnealingLR"  # 余弦退火
  gradient_clip: 0.5           # 严格梯度控制
```

**适用场景**：
- 快速实验
- 数据质量很好时
- 有经验的用户

### 4. **精细调优配置** (微调专用)

```yaml
fine_tune:
  learning_rate: 0.00001       # 极小学习率
  weight_decay: 0.000001       # 极轻正则化
  optimizer: "AdamW"           # AdamW优化器
  scheduler: "CosineAnnealingWarmRestarts"  # 余弦重启
```

**适用场景**：
- 预训练模型微调
- 模型已接近收敛时
- 精细调整阶段

## 🖼️ 数据变换配置

### 输入尺寸选择

| 尺寸 | 优点 | 缺点 | 推荐场景 |
|------|------|------|----------|
| **224x224** | 标准分辨率，效果好 | 计算量大 | 最终训练，追求精度 |
| **112x112** | 平衡性能和速度 | 分辨率中等 | 快速实验，资源有限 |
| **56x56** | 计算快速 | 分辨率低，可能丢失细节 | 原型验证，快速测试 |

### 归一化选择

```yaml
# 带归一化 (推荐)
"224x224_norm":
  normalize: true
  mean: [0.485, 0.456, 0.406]    # ImageNet均值
  std: [0.229, 0.224, 0.225]     # ImageNet标准差

# 不带归一化 (对比实验)
"224x224_no_norm":
  normalize: false               # 原始像素值[0,1]
```

**推荐**：使用归一化版本，特别是使用预训练权重时。

## 🎮 模型参数配置

### FasterNet架构参数

```yaml
model:
  embed_dim: 192               # 嵌入维度 - 影响模型容量
  depths: [3, 4, 18, 3]        # 各阶段深度
  mlp_ratio: 2.0               # MLP扩展比例
  n_div: 4                     # 分组数量
  drop_path_rate: 0.3          # DropPath率
  layer_scale_init_value: 0    # 层缩放初始值
  patch_size: 4                # 补丁大小
  patch_stride: 4              # 补丁步长
```

### 参数调整建议

| 参数 | 增大效果 | 减小效果 | 调整建议 |
|------|----------|----------|----------|
| `embed_dim` | 模型容量增大，可能过拟合 | 模型容量减小，可能欠拟合 | 根据数据量调整 |
| `depths` | 模型更深，表达能力强 | 模型较浅，训练快 | 平衡精度和速度 |
| `drop_path_rate` | 正则化更强 | 正则化较弱 | 根据过拟合情况调整 |

## 💾 资源配置

### 显存估算

| 配置 | 输入尺寸 | embed_dim | 批次大小 | 估计显存 |
|------|----------|-----------|----------|----------|
| 小型 | 56x56 | 64 | 256 | 0.5GB |
| 中型 | 112x112 | 128 | 128 | 0.8GB |
| 标准 | 224x224 | 192 | 64 | 1.2GB |
| 大型 | 224x224 | 256 | 60 | 2.0GB |

### 批次大小建议

```yaml
resources:
  estimated_memory: 1.2        # 估计显存占用
  batch_size: 40               # 批次大小
```

**Original级别采样建议**：
- 批次大小 = original数量
- 确保每个batch包含不同original的数据

## 🚀 使用示例

### 1. 启用不同配置

```yaml
# 启用Original级别采样 (推荐)
config_original_balanced_224x224:
  enable: true

# 启用小规模测试
config_balanced_112x112_small:
  enable: true

# 启用大规模训练
config_balanced_224x224_large:
  enable: true
```

### 2. 自定义配置

```yaml
my_custom_config:
  enable: true
  name: "my_custom"
  description: "自定义配置"
  
  # 自定义采样策略
  dataset_config:
    sampling_strategy: "original_level_sampling"
    strategy_parameters:
      target_originals: 30      # 自定义original数量
      samples_per_original: 80  # 自定义每个original样本数
  
  # 自定义模型参数
  model:
    embed_dim: 256              # 更大的模型
    depths: [4, 6, 20, 4]       # 更深的网络
  
  # 引用配置
  transform_config: "224x224_norm"
  hyperparameter_config: "standard"
```

## ⚠️ 注意事项

1. **同时只能启用一个配置**：确保只有一个 `enable: true`
2. **显存限制**：根据您的GPU调整批次大小和模型参数
3. **采样策略选择**：推荐使用Original级别采样获得更好的评估
4. **超参数调整**：从保守配置开始，逐步调整
5. **数据变换**：推荐使用归一化版本

## 🔍 故障排除

### 常见问题

1. **显存不足**：
   - 减小批次大小
   - 降低输入分辨率
   - 减小模型参数

2. **训练不稳定**：
   - 使用保守型超参数
   - 启用梯度裁剪
   - 降低学习率

3. **收敛太慢**：
   - 使用标准或激进型超参数
   - 增大学习率
   - 调整学习率调度器

4. **过拟合**：
   - 增大权重衰减
   - 增大DropPath率
   - 使用更多数据

这个配置指南涵盖了所有主要的配置选项和使用建议，帮助您根据具体需求选择和调整训练配置。
