#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练配置管理脚本

功能：
1. 查看所有可用的训练配置
2. 启用/禁用特定配置
3. 查看配置详情
4. 批量管理配置

使用方法：
python config_manager.py list                    # 列出所有配置
python config_manager.py show <config_name>      # 显示配置详情
python config_manager.py enable <config_name>    # 启用配置
python config_manager.py disable <config_name>   # 禁用配置
python config_manager.py list-user              # 列出用户配置
"""

import sys
import os
import yaml
import argparse

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def load_config():
    """加载训练配置文件"""
    config_path = "config/training_config.yaml"
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"{Colors.RED}[ERROR]{Colors.END} 无法加载配置文件: {e}")
        return None

def save_config(config):
    """保存训练配置文件"""
    config_path = "config/training_config.yaml"
    try:
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        return True
    except Exception as e:
        print(f"{Colors.RED}[ERROR]{Colors.END} 无法保存配置文件: {e}")
        return False

def list_configs(config, filter_user=False):
    """列出所有训练配置"""
    if not config or 'training_configs' not in config:
        print(f"{Colors.RED}[ERROR]{Colors.END} 配置文件格式错误")
        return

    training_configs = config['training_configs']
    
    print(f"{Colors.BOLD}[CONFIG LIST]{Colors.END} 训练配置列表")
    print("=" * 80)
    
    enabled_count = 0
    disabled_count = 0
    user_configs = []
    
    for name, cfg in training_configs.items():
        if filter_user and not name.startswith('user_'):
            continue
            
        is_enabled = cfg.get('enable', False)
        status = f"{Colors.GREEN}[ENABLED]{Colors.END}" if is_enabled else f"{Colors.YELLOW}[DISABLED]{Colors.END}"
        
        if is_enabled:
            enabled_count += 1
        else:
            disabled_count += 1
            
        if name.startswith('user_'):
            user_configs.append(name)
        
        description = cfg.get('description', '无描述')
        
        # 提取关键信息
        transform_config = cfg.get('transform_config', 'N/A')
        hyperparameter_config = cfg.get('hyperparameter_config', 'N/A')
        
        print(f"{status} {name}")
        print(f"    描述: {description}")
        print(f"    Transform: {transform_config}")
        print(f"    超参数: {hyperparameter_config}")
        print()
    
    print("=" * 80)
    print(f"{Colors.CYAN}[SUMMARY]{Colors.END} 统计信息:")
    print(f"  总配置数: {len(training_configs)}")
    print(f"  已启用: {enabled_count}")
    print(f"  已禁用: {disabled_count}")
    print(f"  用户配置: {len(user_configs)}")
    
    if filter_user:
        print(f"\n{Colors.BLUE}[INFO]{Colors.END} 显示的是用户自定义配置 (user_* 开头)")

def show_config(config, config_name):
    """显示特定配置的详细信息"""
    if not config or 'training_configs' not in config:
        print(f"{Colors.RED}[ERROR]{Colors.END} 配置文件格式错误")
        return

    training_configs = config['training_configs']
    
    if config_name not in training_configs:
        print(f"{Colors.RED}[ERROR]{Colors.END} 配置 '{config_name}' 不存在")
        return
    
    cfg = training_configs[config_name]
    is_enabled = cfg.get('enable', False)
    status = f"{Colors.GREEN}[ENABLED]{Colors.END}" if is_enabled else f"{Colors.YELLOW}[DISABLED]{Colors.END}"
    
    print(f"{Colors.BOLD}[CONFIG DETAILS]{Colors.END} {config_name}")
    print("=" * 80)
    print(f"状态: {status}")
    print(f"描述: {cfg.get('description', '无描述')}")
    print()
    
    # 显示各个配置部分
    sections = [
        ('training', '训练配置'),
        ('dataset_config', '数据集配置'),
        ('model', '模型配置'),
        ('resources', '资源配置'),
        ('transform_config', 'Transform配置'),
        ('hyperparameter_config', '超参数配置')
    ]
    
    for section_key, section_name in sections:
        if section_key in cfg:
            print(f"{Colors.CYAN}[{section_name.upper()}]{Colors.END}")
            section_data = cfg[section_key]
            if isinstance(section_data, dict):
                for key, value in section_data.items():
                    print(f"  {key}: {value}")
            else:
                print(f"  {section_data}")
            print()

def enable_config(config, config_name):
    """启用指定配置"""
    if not config or 'training_configs' not in config:
        print(f"{Colors.RED}[ERROR]{Colors.END} 配置文件格式错误")
        return False

    training_configs = config['training_configs']
    
    if config_name not in training_configs:
        print(f"{Colors.RED}[ERROR]{Colors.END} 配置 '{config_name}' 不存在")
        return False
    
    # 先禁用所有其他配置
    for name, cfg in training_configs.items():
        if name != config_name:
            cfg['enable'] = False
    
    # 启用指定配置
    training_configs[config_name]['enable'] = True
    
    if save_config(config):
        print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 配置 '{config_name}' 已启用")
        print(f"{Colors.BLUE}[INFO]{Colors.END} 其他配置已自动禁用")
        return True
    else:
        return False

def disable_config(config, config_name):
    """禁用指定配置"""
    if not config or 'training_configs' not in config:
        print(f"{Colors.RED}[ERROR]{Colors.END} 配置文件格式错误")
        return False

    training_configs = config['training_configs']
    
    if config_name not in training_configs:
        print(f"{Colors.RED}[ERROR]{Colors.END} 配置 '{config_name}' 不存在")
        return False
    
    training_configs[config_name]['enable'] = False
    
    if save_config(config):
        print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 配置 '{config_name}' 已禁用")
        return True
    else:
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='训练配置管理工具')
    parser.add_argument('action', choices=['list', 'show', 'enable', 'disable', 'list-user'],
                       help='操作类型')
    parser.add_argument('config_name', nargs='?', help='配置名称')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config()
    if config is None:
        return
    
    # 执行操作
    if args.action == 'list':
        list_configs(config)
    elif args.action == 'list-user':
        list_configs(config, filter_user=True)
    elif args.action == 'show':
        if not args.config_name:
            print(f"{Colors.RED}[ERROR]{Colors.END} 请指定配置名称")
            return
        show_config(config, args.config_name)
    elif args.action == 'enable':
        if not args.config_name:
            print(f"{Colors.RED}[ERROR]{Colors.END} 请指定配置名称")
            return
        enable_config(config, args.config_name)
    elif args.action == 'disable':
        if not args.config_name:
            print(f"{Colors.RED}[ERROR]{Colors.END} 请指定配置名称")
            return
        disable_config(config, args.config_name)

if __name__ == "__main__":
    main()
