
  # 用户配置 - 112x112带归一化，学习率0.01，20原图×60样本
  user_112x112_norm_high_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.01，20原图×60样本
    enable: false
    hyperparameter_config: user_high_lr
    model:
        depths:
        - 2
        - 3
        - 12
        - 2
        drop_path_rate: 0.2
        embed_dim: 128
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_112x112_norm_high_lr
    resources:
        batch_size: 48
        estimated_memory: 0.8
    training:
        num_epochs: 100
        save_params: true
    transform_config: 112x112_norm

  # 用户配置 - 112x112带归一化，学习率0.001，20原图×60样本
  user_112x112_norm_mid_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.001，20原图×60样本
    enable: false
    hyperparameter_config: user_mid_lr
    model:
        depths:
        - 2
        - 3
        - 12
        - 2
        drop_path_rate: 0.2
        embed_dim: 128
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_112x112_norm_mid_lr
    resources:
        batch_size: 48
        estimated_memory: 0.8
    training:
        num_epochs: 100
        save_params: true
    transform_config: 112x112_norm

  # 用户配置 - 112x112带归一化，学习率0.0001，20原图×60样本
  user_112x112_norm_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.0001，20原图×60样本
    enable: false
    hyperparameter_config: user_low_lr
    model:
        depths:
        - 2
        - 3
        - 12
        - 2
        drop_path_rate: 0.2
        embed_dim: 128
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_112x112_norm_low_lr
    resources:
        batch_size: 48
        estimated_memory: 0.8
    training:
        num_epochs: 100
        save_params: true
    transform_config: 112x112_norm

  # 用户配置 - 112x112带归一化，学习率0.00001，20原图×60样本
  user_112x112_norm_very_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 112x112带归一化，学习率0.00001，20原图×60样本
    enable: false
    hyperparameter_config: user_very_low_lr
    model:
        depths:
        - 2
        - 3
        - 12
        - 2
        drop_path_rate: 0.2
        embed_dim: 128
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_112x112_norm_very_low_lr
    resources:
        batch_size: 48
        estimated_memory: 0.8
    training:
        num_epochs: 100
        save_params: true
    transform_config: 112x112_norm

  # 用户配置 - 112x112不带归一化，学习率0.01，20原图×60样本
  user_112x112_no_norm_high_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.01，20原图×60样本
    enable: false
    hyperparameter_config: user_high_lr
    model:
        depths:
        - 2
        - 3
        - 12
        - 2
        drop_path_rate: 0.2
        embed_dim: 128
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_112x112_no_norm_high_lr
    resources:
        batch_size: 48
        estimated_memory: 0.8
    training:
        num_epochs: 100
        save_params: true
    transform_config: 112x112_no_norm

  # 用户配置 - 112x112不带归一化，学习率0.001，20原图×60样本
  user_112x112_no_norm_mid_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.001，20原图×60样本
    enable: false
    hyperparameter_config: user_mid_lr
    model:
        depths:
        - 2
        - 3
        - 12
        - 2
        drop_path_rate: 0.2
        embed_dim: 128
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_112x112_no_norm_mid_lr
    resources:
        batch_size: 48
        estimated_memory: 0.8
    training:
        num_epochs: 100
        save_params: true
    transform_config: 112x112_no_norm

  # 用户配置 - 112x112不带归一化，学习率0.0001，20原图×60样本
  user_112x112_no_norm_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.0001，20原图×60样本
    enable: false
    hyperparameter_config: user_low_lr
    model:
        depths:
        - 2
        - 3
        - 12
        - 2
        drop_path_rate: 0.2
        embed_dim: 128
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_112x112_no_norm_low_lr
    resources:
        batch_size: 48
        estimated_memory: 0.8
    training:
        num_epochs: 100
        save_params: true
    transform_config: 112x112_no_norm

  # 用户配置 - 112x112不带归一化，学习率0.00001，20原图×60样本
  user_112x112_no_norm_very_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 112x112不带归一化，学习率0.00001，20原图×60样本
    enable: false
    hyperparameter_config: user_very_low_lr
    model:
        depths:
        - 2
        - 3
        - 12
        - 2
        drop_path_rate: 0.2
        embed_dim: 128
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_112x112_no_norm_very_low_lr
    resources:
        batch_size: 48
        estimated_memory: 0.8
    training:
        num_epochs: 100
        save_params: true
    transform_config: 112x112_no_norm

  # 用户配置 - 80x80带归一化，学习率0.01，20原图×60样本
  user_80x80_norm_high_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.01，20原图×60样本
    enable: false
    hyperparameter_config: user_high_lr
    model:
        depths:
        - 2
        - 3
        - 8
        - 2
        drop_path_rate: 0.2
        embed_dim: 96
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_80x80_norm_high_lr
    resources:
        batch_size: 64
        estimated_memory: 0.6
    training:
        num_epochs: 100
        save_params: true
    transform_config: 80x80_norm

  # 用户配置 - 80x80带归一化，学习率0.001，20原图×60样本
  user_80x80_norm_mid_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.001，20原图×60样本
    enable: false
    hyperparameter_config: user_mid_lr
    model:
        depths:
        - 2
        - 3
        - 8
        - 2
        drop_path_rate: 0.2
        embed_dim: 96
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_80x80_norm_mid_lr
    resources:
        batch_size: 64
        estimated_memory: 0.6
    training:
        num_epochs: 100
        save_params: true
    transform_config: 80x80_norm

  # 用户配置 - 80x80带归一化，学习率0.0001，20原图×60样本
  user_80x80_norm_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.0001，20原图×60样本
    enable: false
    hyperparameter_config: user_low_lr
    model:
        depths:
        - 2
        - 3
        - 8
        - 2
        drop_path_rate: 0.2
        embed_dim: 96
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_80x80_norm_low_lr
    resources:
        batch_size: 64
        estimated_memory: 0.6
    training:
        num_epochs: 100
        save_params: true
    transform_config: 80x80_norm

  # 用户配置 - 80x80带归一化，学习率0.00001，20原图×60样本
  user_80x80_norm_very_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 80x80带归一化，学习率0.00001，20原图×60样本
    enable: false
    hyperparameter_config: user_very_low_lr
    model:
        depths:
        - 2
        - 3
        - 8
        - 2
        drop_path_rate: 0.2
        embed_dim: 96
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_80x80_norm_very_low_lr
    resources:
        batch_size: 64
        estimated_memory: 0.6
    training:
        num_epochs: 100
        save_params: true
    transform_config: 80x80_norm

  # 用户配置 - 80x80不带归一化，学习率0.01，20原图×60样本
  user_80x80_no_norm_high_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.01，20原图×60样本
    enable: false
    hyperparameter_config: user_high_lr
    model:
        depths:
        - 2
        - 3
        - 8
        - 2
        drop_path_rate: 0.2
        embed_dim: 96
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_80x80_no_norm_high_lr
    resources:
        batch_size: 64
        estimated_memory: 0.6
    training:
        num_epochs: 100
        save_params: true
    transform_config: 80x80_no_norm

  # 用户配置 - 80x80不带归一化，学习率0.001，20原图×60样本
  user_80x80_no_norm_mid_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.001，20原图×60样本
    enable: false
    hyperparameter_config: user_mid_lr
    model:
        depths:
        - 2
        - 3
        - 8
        - 2
        drop_path_rate: 0.2
        embed_dim: 96
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_80x80_no_norm_mid_lr
    resources:
        batch_size: 64
        estimated_memory: 0.6
    training:
        num_epochs: 100
        save_params: true
    transform_config: 80x80_no_norm

  # 用户配置 - 80x80不带归一化，学习率0.0001，20原图×60样本
  user_80x80_no_norm_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.0001，20原图×60样本
    enable: false
    hyperparameter_config: user_low_lr
    model:
        depths:
        - 2
        - 3
        - 8
        - 2
        drop_path_rate: 0.2
        embed_dim: 96
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_80x80_no_norm_low_lr
    resources:
        batch_size: 64
        estimated_memory: 0.6
    training:
        num_epochs: 100
        save_params: true
    transform_config: 80x80_no_norm

  # 用户配置 - 80x80不带归一化，学习率0.00001，20原图×60样本
  user_80x80_no_norm_very_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 80x80不带归一化，学习率0.00001，20原图×60样本
    enable: false
    hyperparameter_config: user_very_low_lr
    model:
        depths:
        - 2
        - 3
        - 8
        - 2
        drop_path_rate: 0.2
        embed_dim: 96
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 2
        patch_stride: 2
    name: user_80x80_no_norm_very_low_lr
    resources:
        batch_size: 64
        estimated_memory: 0.6
    training:
        num_epochs: 100
        save_params: true
    transform_config: 80x80_no_norm

  # 用户配置 - 56x56带归一化，学习率0.01，20原图×60样本
  user_56x56_norm_high_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.01，20原图×60样本
    enable: false
    hyperparameter_config: user_high_lr
    model:
        depths:
        - 2
        - 2
        - 6
        - 2
        drop_path_rate: 0.2
        embed_dim: 64
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 1
        patch_stride: 1
    name: user_56x56_norm_high_lr
    resources:
        batch_size: 80
        estimated_memory: 0.4
    training:
        num_epochs: 100
        save_params: true
    transform_config: 56x56_norm

  # 用户配置 - 56x56带归一化，学习率0.001，20原图×60样本
  user_56x56_norm_mid_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.001，20原图×60样本
    enable: false
    hyperparameter_config: user_mid_lr
    model:
        depths:
        - 2
        - 2
        - 6
        - 2
        drop_path_rate: 0.2
        embed_dim: 64
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 1
        patch_stride: 1
    name: user_56x56_norm_mid_lr
    resources:
        batch_size: 80
        estimated_memory: 0.4
    training:
        num_epochs: 100
        save_params: true
    transform_config: 56x56_norm

  # 用户配置 - 56x56带归一化，学习率0.0001，20原图×60样本
  user_56x56_norm_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.0001，20原图×60样本
    enable: false
    hyperparameter_config: user_low_lr
    model:
        depths:
        - 2
        - 2
        - 6
        - 2
        drop_path_rate: 0.2
        embed_dim: 64
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 1
        patch_stride: 1
    name: user_56x56_norm_low_lr
    resources:
        batch_size: 80
        estimated_memory: 0.4
    training:
        num_epochs: 100
        save_params: true
    transform_config: 56x56_norm

  # 用户配置 - 56x56带归一化，学习率0.00001，20原图×60样本
  user_56x56_norm_very_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 56x56带归一化，学习率0.00001，20原图×60样本
    enable: false
    hyperparameter_config: user_very_low_lr
    model:
        depths:
        - 2
        - 2
        - 6
        - 2
        drop_path_rate: 0.2
        embed_dim: 64
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 1
        patch_stride: 1
    name: user_56x56_norm_very_low_lr
    resources:
        batch_size: 80
        estimated_memory: 0.4
    training:
        num_epochs: 100
        save_params: true
    transform_config: 56x56_norm

  # 用户配置 - 56x56不带归一化，学习率0.01，20原图×60样本
  user_56x56_no_norm_high_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.01，20原图×60样本
    enable: false
    hyperparameter_config: user_high_lr
    model:
        depths:
        - 2
        - 2
        - 6
        - 2
        drop_path_rate: 0.2
        embed_dim: 64
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 1
        patch_stride: 1
    name: user_56x56_no_norm_high_lr
    resources:
        batch_size: 80
        estimated_memory: 0.4
    training:
        num_epochs: 100
        save_params: true
    transform_config: 56x56_no_norm

  # 用户配置 - 56x56不带归一化，学习率0.001，20原图×60样本
  user_56x56_no_norm_mid_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.001，20原图×60样本
    enable: false
    hyperparameter_config: user_mid_lr
    model:
        depths:
        - 2
        - 2
        - 6
        - 2
        drop_path_rate: 0.2
        embed_dim: 64
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 1
        patch_stride: 1
    name: user_56x56_no_norm_mid_lr
    resources:
        batch_size: 80
        estimated_memory: 0.4
    training:
        num_epochs: 100
        save_params: true
    transform_config: 56x56_no_norm

  # 用户配置 - 56x56不带归一化，学习率0.0001，20原图×60样本
  user_56x56_no_norm_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.0001，20原图×60样本
    enable: false
    hyperparameter_config: user_low_lr
    model:
        depths:
        - 2
        - 2
        - 6
        - 2
        drop_path_rate: 0.2
        embed_dim: 64
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 1
        patch_stride: 1
    name: user_56x56_no_norm_low_lr
    resources:
        batch_size: 80
        estimated_memory: 0.4
    training:
        num_epochs: 100
        save_params: true
    transform_config: 56x56_no_norm

  # 用户配置 - 56x56不带归一化，学习率0.00001，20原图×60样本
  user_56x56_no_norm_very_low_lr:
    dataset_config:
        sampling_strategy: original_level_sampling
        strategy_parameters:
            cross_subset_sampling: true
            samples_per_original: 60
            target_originals: 20
            test_ratio: 0.1
            total_samples: 1200
            train_ratio: 0.8
            val_ratio: 0.1
    description: 用户配置 - 56x56不带归一化，学习率0.00001，20原图×60样本
    enable: false
    hyperparameter_config: user_very_low_lr
    model:
        depths:
        - 2
        - 2
        - 6
        - 2
        drop_path_rate: 0.2
        embed_dim: 64
        layer_scale_init_value: 0
        mlp_ratio: 2.0
        n_div: 4
        patch_size: 1
        patch_stride: 1
    name: user_56x56_no_norm_very_low_lr
    resources:
        batch_size: 80
        estimated_memory: 0.4
    training:
        num_epochs: 100
        save_params: true
    transform_config: 56x56_no_norm