#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础调度器测试脚本

测试功能：
1. 任务队列管理
2. 日志记录
3. 基础调度功能
"""

import sys
import os
import time
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_task_scheduler_basic():
    """测试基础任务调度器功能"""
    print("🧪 Testing Basic Task Scheduler...")
    
    try:
        from scheduler.task_scheduler import TaskScheduler, TrainingTask, TaskPriority
        
        # 创建调度器
        scheduler = TaskScheduler(max_gpu_memory=8.0, max_concurrent_tasks=1)
        print("✅ Task scheduler created")
        
        # 创建测试任务
        test_config = {
            'name': 'basic_test',
            'model': {
                'embed_dim': 32,
                'depths': [1, 1, 2, 1],
                'mlp_ratio': 2.0,
                'n_div': 2,
                'drop_path_rate': 0.1
            },
            'resources': {'batch_size': 5},
            'transform_config': '224x224_norm',
            'training': {'num_epochs': 1, 'learning_rate': 0.001},
            'sampling_strategy_config': {
                'strategy_type': 'random',
                'parameters': {'sample_size': 50, 'seed': 42}
            }
        }
        
        task = TrainingTask(
            task_id="test_001",
            name="Basic Test Task",
            config=test_config,
            priority=TaskPriority.NORMAL
        )
        
        # 提交任务
        task_id = scheduler.add_task(task)
        print(f"✅ Task submitted: {task_id}")
        
        # 获取任务状态
        task_status = scheduler.get_task_status(task_id)
        if task_status:
            print(f"✅ Task status retrieved: {task_status.status.value}")
        
        # 获取队列状态
        queue_status = scheduler.get_queue_status()
        print(f"✅ Queue status: {queue_status['pending_count']} pending")
        
        # 获取测试摘要
        test_summary = scheduler.get_test_summary()
        print(f"✅ Test summary: {test_summary}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic task scheduler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logger_integration():
    """测试日志集成"""
    print("\n🧪 Testing Logger Integration...")
    
    try:
        from tools.training.train import setup_training_logger
        
        # 测试日志设置
        log_file = "logs/test/test_logger.log"
        logger = setup_training_logger(log_file, "TestTask", test_mode=True)
        
        # 测试日志记录
        logger.info("This is a test info message")
        logger.warning("This is a test warning message")
        logger.error("This is a test error message")
        
        # 检查日志文件是否创建
        if os.path.exists(log_file):
            print("✅ Log file created successfully")
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "test info message" in content:
                    print("✅ Log content verified")
                else:
                    print("❌ Log content not found")
                    return False
        else:
            print("❌ Log file not created")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Logger integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_with_logger():
    """测试带日志的训练功能"""
    print("\n🧪 Testing Training with Logger...")
    
    try:
        from tools.training.train import train_model
        import torch
        
        # 创建简单的测试配置
        model_config = {
            'embed_dim': 32,
            'depths': [1, 1, 2, 1],
            'mlp_ratio': 2.0,
            'n_div': 2,
            'drop_path_rate': 0.1
        }
        
        hyperparam_config = {
            'learning_rate': 0.001,
            'weight_decay': 1e-4,
            'optimizer': 'Adam',
            'scheduler': 'ReduceLROnPlateau',
            'scheduler_params': {'mode': 'min', 'factor': 0.5, 'patience': 2}
        }
        
        sampling_strategy_config = {
            'strategy_type': 'random',
            'parameters': {
                'sample_size': 50,
                'seed': 42
            }
        }
        
        # 设置日志文件
        log_file = "logs/test/test_training.log"
        
        # 运行训练（只训练1个epoch用于测试）
        print("Starting test training...")
        history, model = train_model(
            model_config=model_config,
            hyperparam_config=hyperparam_config,
            num_epochs=1,
            batch_size=10,
            task_name="TestTraining",
            sampling_strategy_config=sampling_strategy_config,
            log_file_path=log_file,
            test_mode=True
        )
        
        print("✅ Training completed successfully")
        
        # 检查日志文件
        if os.path.exists(log_file):
            print("✅ Training log file created")
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
                if "TEST MODE STARTED" in content and "epoch completed" in content.lower():
                    print("✅ Training log content verified")
                else:
                    print("⚠️  Training log content incomplete")
        
        return True
        
    except Exception as e:
        print(f"❌ Training with logger test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n🧪 Testing Config Loading...")
    
    try:
        from config.config_loader import ConfigLoader
        
        # 加载配置
        config_loader = ConfigLoader()
        configs = config_loader.get_enabled_training_configs()
        
        if configs:
            print(f"✅ Loaded {len(configs)} configurations")
            for config in configs:
                print(f"  - {config.get('name', 'unnamed')}")
        else:
            print("⚠️  No configurations loaded")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Basic Scheduler Test Suite")
    print("=" * 50)
    
    # 创建日志目录
    os.makedirs("logs/test", exist_ok=True)
    
    tests = [
        ("Config Loading", test_config_loading),
        ("Task Scheduler Basic", test_task_scheduler_basic),
        ("Logger Integration", test_logger_integration),
        ("Training with Logger", test_training_with_logger),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 显示结果
    print("\n" + "="*60)
    print("📊 Test Results Summary")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! Basic scheduler functionality is working.")
    else:
        print(f"⚠️  {total-passed} tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    main()
