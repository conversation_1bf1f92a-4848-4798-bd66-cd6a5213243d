import torch
import torch.nn as nn
import os
import copy
from torch import Tensor
from functools import partial
from .fasternet_blocks import Partial_conv3, MLP<PERSON>lock, BasicStage, PatchEmbed, PatchMerging

class FasterNet(nn.Module):
    """
    V0版本的FasterNet完整实现

    largest
    model_name: fasternet
    mlp_ratio: 2
    embed_dim: 192
    depths: [3, 4, 18, 3]
    feature_dim: 1280
    patch_size: 4
    patch_stride: 4
    patch_size2: 2
    patch_stride2: 2
    layer_scale_init_value: 0 # no layer scale
    drop_path_rate: 0.3
    norm_layer:  BN
    act_layer: RELU
    n_div: 4
    """
    def __init__(self,
                 in_chans=3,
                 num_classes=2,
                 embed_dim=192,
                 depths=(3, 4, 18, 3),
                 mlp_ratio=2.,
                 n_div=4,
                 patch_size=4,
                 patch_stride=4,
                 patch_size2=2,  # for subsequent layers
                 patch_stride2=2,
                 patch_norm=True,
                 feature_dim=1280,
                 drop_path_rate=0.3,
                 layer_scale_init_value=0,
                 norm_layer='BN',
                 act_layer='RELU',
                 fork_feat=False,
                 init_cfg=None,
                 pretrained=None,
                 pconv_fw_type='split_cat',
                 **kwargs):
        super().__init__()

        if norm_layer == 'BN':
            norm_layer = nn.BatchNorm2d
        else:
            raise NotImplementedError

        if act_layer == 'GELU':
            act_layer = nn.GELU
        elif act_layer == 'RELU':
            act_layer = partial(nn.ReLU, inplace=True)
        else:
            raise NotImplementedError

        if not fork_feat:
            self.num_classes = num_classes
        self.num_stages = len(depths)
        self.embed_dim = embed_dim
        self.patch_norm = patch_norm
        self.num_features = int(embed_dim * 2 ** (self.num_stages - 1))
        self.mlp_ratio = mlp_ratio
        self.depths = depths

        # split image into non-overlapping patches
        self.patch_embed = PatchEmbed(
            patch_size=patch_size,
            patch_stride=patch_stride,
            in_chans=in_chans,
            embed_dim=embed_dim,
            norm_layer=norm_layer if self.patch_norm else None
        )

        # stochastic depth decay rule
        dpr = [x.item()
               for x in torch.linspace(0, drop_path_rate, sum(depths))]

        # build layers
        stages_list = []
        for i_stage in range(self.num_stages):
            stage = BasicStage(dim=int(embed_dim * 2 ** i_stage),
                               n_div=n_div,
                               depth=depths[i_stage],
                               mlp_ratio=self.mlp_ratio,
                               drop_path=dpr[sum(depths[:i_stage]):sum(depths[:i_stage + 1])],
                               layer_scale_init_value=layer_scale_init_value,
                               norm_layer=norm_layer,
                               act_layer=act_layer,
                               pconv_fw_type=pconv_fw_type
                               )
            stages_list.append(stage)

            # patch merging layer
            if i_stage < self.num_stages - 1:
                stages_list.append(
                    PatchMerging(patch_size2=patch_size2,
                                 patch_stride2=patch_stride2,
                                 dim=int(embed_dim * 2 ** i_stage),
                                 norm_layer=norm_layer)
                )

        self.stages = nn.Sequential(*stages_list)

        self.fork_feat = fork_feat

        if self.fork_feat:
            self.forward = self.forward_det
            # add a norm layer for each output
            self.out_indices = [0, 2, 4, 6]
            for i_emb, i_layer in enumerate(self.out_indices):
                if i_emb == 0 and os.environ.get('FORK_LAST3', None):
                    raise NotImplementedError
                else:
                    layer = norm_layer(int(embed_dim * 2 ** i_emb))
                layer_name = f'norm{i_layer}'
                self.add_module(layer_name, layer)
        else:
            self.forward = self.forward_cls
            # Classifier head
            self.avgpool_pre_head = nn.Sequential(
                nn.AdaptiveAvgPool2d(1),
                nn.Conv2d(self.num_features, feature_dim, 1, bias=False),
                act_layer()
            )
            self.head = nn.Linear(feature_dim, num_classes) \
                if num_classes > 0 else nn.Identity()

        self.apply(self.cls_init_weights)
        self.init_cfg = copy.deepcopy(init_cfg)

    def cls_init_weights(self, m):
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, (nn.Conv1d, nn.Conv2d)):
            nn.init.trunc_normal_(m.weight, std=.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, (nn.LayerNorm, nn.GroupNorm)):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def func_transition(self, outputs):
        # outputs = outputs.view(-1)
        outputs[:,0] = (torch.sigmoid(outputs[:,0]) * 100)
        outputs[:,1] = (torch.sigmoid(outputs[:,1]) * 100)
        return outputs

    def forward_cls(self, x):
        # output only the features of last layer for image classification
        x = self.patch_embed(x)
        x = self.stages(x)
        x = self.avgpool_pre_head(x)  # B C 1 1
        x = torch.flatten(x, 1)
        x = self.head(x)
        x = self.func_transition(x)
        return [x]

    def forward_det(self, x: Tensor) -> Tensor:
        # output the features of four stages for dense prediction
        x = self.patch_embed(x)
        outs = []
        for idx, stage in enumerate(self.stages):
            x = stage(x)
            if self.fork_feat and idx in self.out_indices:
                norm_layer = getattr(self, f'norm{idx}')
                x_out = norm_layer(x)
                outs.append(x_out)
        return outs

class model(nn.Module):
    """V0版本的模型包装类"""
    def __init__(self,
                 num_classes=2,
                 device='cpu',
                 embed_dim=192,
                 depths=(3, 4, 18, 3),
                 mlp_ratio=2.,
                 n_div=4,
                 drop_path_rate=0.3,
                 layer_scale_init_value=0,
                 **kwargs):
        """
        初始化模型，允许配置FasterNet的关键参数

        Args:
            num_classes: 分类数量
            device: 运行设备
            embed_dim: 嵌入维度
            depths: 每个阶段的层数
            mlp_ratio: MLP扩展比例
            n_div: 分组卷积的分组数
            drop_path_rate: 随机深度衰减率
            layer_scale_init_value: 层缩放初始值
            **kwargs: 其他传递给FasterNet的参数
        """
        self.device = device
        super(model, self).__init__()

        # 参数自检
        self._validate_parameters(
            num_classes=num_classes,
            embed_dim=embed_dim,
            depths=depths,
            mlp_ratio=mlp_ratio,
            n_div=n_div,
            drop_path_rate=drop_path_rate,
            layer_scale_init_value=layer_scale_init_value,
            **kwargs
        )

        # 初始化FasterNet，传递所有指定参数和额外参数
        self.model = FasterNet(
            num_classes=num_classes,
            embed_dim=embed_dim,
            depths=depths,
            mlp_ratio=mlp_ratio,
            n_div=n_div,
            drop_path_rate=drop_path_rate,
            layer_scale_init_value=layer_scale_init_value,
            **kwargs
        )

    def _validate_parameters(self, **params):
        """
        验证FasterNet参数的有效性和关联性

        Args:
            **params: 所有需要验证的参数

        Raises:
            ValueError: 当参数无效或参数间关联性不满足要求时
        """
        # 检查num_classes
        if params['num_classes'] <= 0:
            raise ValueError(f"num_classes必须为正整数，当前值: {params['num_classes']}")

        # 检查embed_dim
        if params['embed_dim'] <= 0 or params['embed_dim'] % 4 != 0:
            raise ValueError(f"embed_dim必须为正的4的倍数，当前值: {params['embed_dim']}")

        # 检查depths
        if len(params['depths']) != 4:
            raise ValueError(f"depths必须包含4个值，对应4个阶段，当前长度: {len(params['depths'])}")

        for i, d in enumerate(params['depths']):
            if d <= 0:
                raise ValueError(f"depths中的每个值必须为正整数，第{i+1}个值: {d}")

        # 检查mlp_ratio
        if params['mlp_ratio'] <= 0:
            raise ValueError(f"mlp_ratio必须为正数，当前值: {params['mlp_ratio']}")

        # 检查n_div (分组卷积的分组数)
        if params['n_div'] <= 0 or params['embed_dim'] % params['n_div'] != 0:
            raise ValueError(
                f"n_div必须为正整数，且embed_dim必须能被n_div整除，"
                f"当前n_div: {params['n_div']}, embed_dim: {params['embed_dim']}"
            )

        # 检查drop_path_rate
        if not 0 <= params['drop_path_rate'] <= 1:
            raise ValueError(f"drop_path_rate必须在0到1之间，当前值: {params['drop_path_rate']}")

        # 检查patch_size和patch_stride (如果提供)
        patch_size = params.get('patch_size', 4)
        patch_stride = params.get('patch_stride', 4)
        if patch_size <= 0 or patch_stride <= 0:
            raise ValueError(f"patch_size和patch_stride必须为正整数，当前值: {patch_size}, {patch_stride}")

        # 检查patch_size2和patch_stride2 (如果提供)
        patch_size2 = params.get('patch_size2', 2)
        patch_stride2 = params.get('patch_stride2', 2)
        if patch_size2 <= 0 or patch_stride2 <= 0:
            raise ValueError(f"patch_size2和patch_stride2必须为正整数，当前值: {patch_size2}, {patch_stride2}")

        # 检查feature_dim (如果提供)
        feature_dim = params.get('feature_dim', 1280)
        if feature_dim <= 0:
            raise ValueError(f"feature_dim必须为正整数，当前值: {feature_dim}")

    def forward(self, x):
        out = self.model(x)
        out = out[0]
        # 确保输出是二维的
        if out.dim() == 1:
            out = out.unsqueeze(0)

        # 只有当输出维度足够时才进行索引操作
        if out.shape[-1] >= 2:
            out[:, 1] = out[:, 1] * (29.1-17.4) / 100 + 17.4
            out[:, 0] = out[:, 0] * (50.5 - 32.5) / 100 + 32.5

        return out

if __name__ == '__main__':
    # 实例化模型
    m = model(num_classes=2, device='cpu')
    f = FasterNet(num_classes=2, device='cpu')
    print(f)
    # print(m)