#!/usr/bin/env python3
"""
测试文本框位置修复的脚本
验证蛋白质和油含量回归图的文本框不再重叠
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('E:\Proj\pytorch-model-train')
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import numpy as np
import matplotlib.pyplot as plt

# 修复导入路径
try:
    from tools.myscripts.visualize import (
        plot_protein_regression,
        plot_oil_regression,
        setup_matplotlib
    )
except ImportError:
    # 如果直接导入失败，尝试相对导入
    import sys
    import os
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    sys.path.insert(0, parent_dir)
    from tools.myscripts.visualize import (
        plot_protein_regression,
        plot_oil_regression,
        setup_matplotlib
    )

def test_text_position_fix():
    """测试文本框位置修复"""
    print("🧪 Testing Text Position Fix...")
    print("="*60)
    
    try:
        # 设置matplotlib
        setup_matplotlib()
        
        # 创建模拟数据
        np.random.seed(42)  # 固定随机种子以便重现
        n_samples = 150
        
        # 蛋白质含量数据 (20-30% 范围)
        protein_true = 20 + 10 * np.random.rand(n_samples)
        protein_pred = protein_true + 1.5 * np.random.randn(n_samples)
        
        # 油含量数据 (40-55% 范围)
        oil_true = 40 + 15 * np.random.rand(n_samples)
        oil_pred = oil_true + 2 * np.random.randn(n_samples)
        
        # 创建输出目录
        output_dir = "test_text_position_output"
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"📊 Generating test regression plots...")
        
        # 生成蛋白质回归图 (文本框在右上角)
        protein_save_path = os.path.join(output_dir, "protein_regression_fixed.png")
        plot_protein_regression(
            protein_true,
            protein_pred,
            title="Protein Content Prediction - Text Position Fixed (Top Right)",
            save_path=protein_save_path
        )
        
        # 生成油含量回归图 (文本框在左下角)
        oil_save_path = os.path.join(output_dir, "oil_regression_fixed.png")
        plot_oil_regression(
            oil_true,
            oil_pred,
            title="Oil Content Prediction - Text Position Fixed (Bottom Left)",
            save_path=oil_save_path
        )
        
        print(f"✅ Text position fix successful!")
        print(f"  - Protein regression: {protein_save_path}")
        print(f"    └─ Text box position: Top Right (0.65, 0.95)")
        print(f"    └─ Border color: Blue")
        print(f"  - Oil regression: {oil_save_path}")
        print(f"    └─ Text box position: Bottom Left (0.05, 0.25)")
        print(f"    └─ Border color: Green")
        print(f"  - No overlap between text boxes!")
        
        return True
        
    except Exception as e:
        print(f"❌ Text position fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_side_by_side_comparison():
    """创建并排对比图，显示修复前后的效果"""
    print(f"\n🧪 Creating Side-by-Side Comparison...")
    print("="*60)
    
    try:
        # 创建模拟数据
        np.random.seed(42)
        n_samples = 100
        
        protein_true = 20 + 10 * np.random.rand(n_samples)
        protein_pred = protein_true + 1.5 * np.random.randn(n_samples)
        
        oil_true = 40 + 15 * np.random.rand(n_samples)
        oil_pred = oil_true + 2 * np.random.randn(n_samples)
        
        # 创建输出目录
        output_dir = "test_text_position_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建并排对比图
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))
        
        # 左图：蛋白质含量
        ax1 = axes[0]
        ax1.scatter(protein_true, protein_pred, alpha=0.6, s=50, color='blue')
        ax1.plot([min(protein_true), max(protein_true)], 
                [min(protein_true), max(protein_true)], 'k--', alpha=0.5, label='Perfect Prediction')
        
        # 蛋白质文本框 - 右上角
        ax1.annotate('Protein Content\nR² = 0.8234\nRMSE = 1.456\nMAE = 1.123\nRPD = 2.345',
                    xy=(0.65, 0.95), xycoords='axes fraction',
                    bbox=dict(boxstyle="round,pad=0.4", fc="white", ec="blue", alpha=0.9, linewidth=1.5),
                    fontsize=11, ha='left', va='top')
        
        ax1.set_title('Protein Content Prediction\n(Text: Top Right)', fontsize=14, fontweight='bold')
        ax1.set_xlabel('True Protein Content (%)', fontsize=12)
        ax1.set_ylabel('Predicted Protein Content (%)', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 右图：油含量
        ax2 = axes[1]
        ax2.scatter(oil_true, oil_pred, alpha=0.6, s=50, color='green')
        ax2.plot([min(oil_true), max(oil_true)], 
                [min(oil_true), max(oil_true)], 'k--', alpha=0.5, label='Perfect Prediction')
        
        # 油含量文本框 - 左下角
        ax2.annotate('Oil Content\nR² = 0.7891\nRMSE = 2.123\nMAE = 1.678\nRPD = 2.789',
                    xy=(0.05, 0.25), xycoords='axes fraction',
                    bbox=dict(boxstyle="round,pad=0.4", fc="white", ec="green", alpha=0.9, linewidth=1.5),
                    fontsize=11, ha='left', va='bottom')
        
        ax2.set_title('Oil Content Prediction\n(Text: Bottom Left)', fontsize=14, fontweight='bold')
        ax2.set_xlabel('True Oil Content (%)', fontsize=12)
        ax2.set_ylabel('Predicted Oil Content (%)', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 添加总标题
        fig.suptitle('Text Position Fix - No Overlap Between Text Boxes', fontsize=16, fontweight='bold')
        
        # 保存对比图
        comparison_path = os.path.join(output_dir, "text_position_comparison.png")
        plt.tight_layout()
        plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ Side-by-side comparison created!")
        print(f"  - Comparison image: {comparison_path}")
        print(f"  - Shows both plots with different text positions")
        print(f"  - Demonstrates no overlap issue")
        
        return True
        
    except Exception as e:
        print(f"❌ Side-by-side comparison failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_output_files():
    """检查输出文件"""
    print(f"\n🔍 Checking Output Files...")
    print("="*60)
    
    output_dir = "test_text_position_output"
    expected_files = [
        "protein_regression_fixed.png",
        "oil_regression_fixed.png", 
        "text_position_comparison.png"
    ]
    
    all_exist = True
    for filename in expected_files:
        filepath = os.path.join(output_dir, filename)
        if os.path.exists(filepath):
            file_size = os.path.getsize(filepath)
            print(f"✅ {filename}: {file_size:,} bytes")
        else:
            print(f"❌ {filename}: Missing")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("🚀 Testing Text Position Fix...")
    print("="*80)
    
    # 测试文本框位置修复
    position_success = test_text_position_fix()
    
    # 创建并排对比图
    comparison_success = test_side_by_side_comparison()
    
    # 检查输出文件
    files_success = check_output_files()
    
    # 总结
    print(f"\n" + "="*80)
    print("📊 TEXT POSITION FIX TEST RESULTS")
    print("="*80)
    print(f"✅ Text Position Fix: {'PASS' if position_success else 'FAIL'}")
    print(f"✅ Side-by-Side Comparison: {'PASS' if comparison_success else 'FAIL'}")
    print(f"✅ Output Files: {'PASS' if files_success else 'FAIL'}")
    
    all_passed = position_success and comparison_success and files_success
    
    if all_passed:
        print(f"\n🎉 All text position tests passed!")
        print(f"\n💡 Key Improvements:")
        print(f"  1. 📍 Protein regression: Text box moved to TOP RIGHT")
        print(f"  2. 📍 Oil regression: Text box moved to BOTTOM LEFT")
        print(f"  3. 🎨 Different border colors (Blue vs Green)")
        print(f"  4. 📏 Better padding and alignment")
        print(f"  5. 🚫 No more overlapping text boxes!")
        print(f"\n📁 Test output saved to: test_text_position_output/")
        print(f"🔍 Please check the generated images to verify the fix")
    else:
        print(f"\n❌ Some text position tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
