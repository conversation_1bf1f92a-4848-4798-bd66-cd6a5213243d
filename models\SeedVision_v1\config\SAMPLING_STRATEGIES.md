# SeedVision v1 - 采样策略详细对比

## 📊 采样策略总览


| 策略名称             | 策略类型         | 实现状态      | 推荐度     | 适用场景             |
| -------------------- | ---------------- | ------------- | ---------- | -------------------- |
| **Original级别采样** | `original_based` | ✅ 完全实现   | ⭐⭐⭐⭐⭐ | 需要Original级别评估 |
| **平衡采样**         | `balanced`       | ✅ 完全实现   | ⭐⭐⭐⭐   | 一般训练场景         |
| **随机采样**         | `random`         | ⚠️ 基础实现 | ⭐⭐⭐     | 快速实验             |
| **分层采样**         | `stratified`     | ❌ 待实现     | ⭐⭐       | 不平衡数据集         |
| **时间序列采样**     | `temporal`       | ❌ 待实现     | ⭐⭐       | 时序相关数据         |

## 🎯 1. Original级别采样 (推荐)

### 配置示例

```yaml
original_level_sampling:
  strategy_type: "original_based"
  parameters:
    target_originals: 40      # 选择40个original
    samples_per_original: 60  # 每个original采样60张图片
    total_samples: 2400       # 总计2400张图片
    cross_subset_sampling: true  # 跨子集采样
    train_ratio: 0.8          # 训练集比例
    val_ratio: 0.1            # 验证集比例
    test_ratio: 0.1           # 测试集比例
```

### 核心特性

- ✅ **Original级别评估**：提供样本级别和Original级别双重R²评估
- 🎯 **数据平衡**：确保每个original有相同数量的样本
- 🌐 **跨子集采样**：忽略原有train/val/test划分，重新分配
- 📊 **详细统计**：提供每个original的预测一致性分析

### 实现函数

- `fixed_sample_by_original_cross_subset()` - 跨子集采样（推荐）
- `fixed_sample_by_original()` - 子集内采样
- `evaluate_original_level()` - Original级别评估

### 输出示例

```
📊 Sample-level R²:
  Oil: Training=0.8234, Validation=0.7891
  Protein: Training=0.8567, Validation=0.8123

🎯 Original-level R²:
  Oil: 0.8456
  Protein: 0.8789

📊 Original-level evaluation completed:
  - Total originals: 40
  - Oil R²: Sample=0.7891, Original=0.8456
  - Protein R²: Sample=0.8123, Original=0.8789
```

### 适用场景

- Original指的是每个单个油菜籽对应的原始图片
- 主要推荐场景**：需要Original级别评估的所有情况
- **数据特点**：同一original下样本标签相同（代表original平均值）
- **评估需求**：需要了解模型对original平均值的预测能力
- **数据分布**：original间数据量不均匀的情况

### 参数调整建议

```yaml
# 小规模测试
target_originals: 20
samples_per_original: 30
total_samples: 600

# 标准配置
target_originals: 40
samples_per_original: 60
total_samples: 2400

# 大规模训练
target_originals: 60
samples_per_original: 80
total_samples: 4800
```

## 🔍 2. 平衡采样

### 配置示例

```yaml
balanced_sampling:
  strategy_type: "balanced"
  parameters:
    samples_per_group: 100    # 每组最大样本数
    max_groups: 45           # 最大组数
    total_samples: 4500      # 总样本数
```

### 核心特性

- ✅ **组间平衡**：从每个original组中平衡采样
- 📊 **自动计算**：自动计算每个组的采样数量
- ⚡ **快速实现**：简单高效的采样策略

### 实现函数

- `balanced_sample_by_original()` - 平衡采样主函数

### 适用场景

- **快速训练**：不需要Original级别评估的场景
- **数据探索**：初步了解数据分布和模型性能
- **资源有限**：计算资源有限，需要快速训练

### 参数调整建议

```yaml
# 快速测试
total_samples: 1000
samples_per_group: 50

# 标准训练
total_samples: 4500
samples_per_group: 100

# 大规模训练
total_samples: 9000
samples_per_group: 200
```

## 🎲 3. 随机采样

### 配置示例

```yaml
random_sampling:
  strategy_type: "random"
  parameters:
    sample_size: 4500        # 采样数量
    seed: 42                 # 随机种子
```

### 核心特性

- 🎲 **完全随机**：不考虑数据分布，完全随机选择
- 🔢 **可重现**：通过随机种子确保结果可重现
- ⚡ **简单快速**：最简单的采样策略

### 当前状态

- ⚠️ **基础实现**：参数解析已完成，需要添加专门的随机采样函数
- 📝 **待完善**：需要实现 `random_sample()` 函数

### 适用场景

- **基准测试**：作为其他采样策略的对比基准
- **快速原型**：快速验证模型架构和训练流程
- **数据均匀**：数据分布相对均匀的情况

### 需要实现的功能

```python
def random_sample(data, sample_size, seed=42):
    """随机采样函数 - 待实现"""
    random.seed(seed)
    return random.sample(data, min(sample_size, len(data)))
```

## 📊 4. 分层采样 (待实现)

### 配置示例

```yaml
stratified_sampling:
  strategy_type: "stratified"
  parameters:
    maintain_distribution: true    # 保持原始分布
    total_samples: 4500           # 总样本数
    min_samples_per_stratum: 10   # 每层最小样本数
```

### 设计特性

- 📊 **保持分布**：保持原始数据的标签分布
- 🎯 **分层控制**：确保每个层级有最小样本数
- 📈 **适合不平衡**：特别适合标签分布不均匀的数据集

### 适用场景

- **不平衡数据**：标签分布极不均匀的情况
- **分类任务**：需要保持类别分布的分类问题
- **统计分析**：需要保持原始数据统计特性

### 需要实现的功能

```python
def stratified_sample_by_labels(data, total_samples, min_samples_per_stratum=10):
    """分层采样函数 - 待实现"""
    # 1. 按标签值分层
    # 2. 计算每层采样比例
    # 3. 确保最小样本数
    # 4. 按比例采样
    pass
```

## ⏰ 5. 时间序列采样 (待实现)

### 配置示例

```yaml
temporal_sampling:
  strategy_type: "temporal"
  parameters:
    time_window: "recent"     # recent, random, sequential
    window_size: 30          # 时间窗口大小（天）
    total_samples: 3000      # 总样本数
```

### 设计特性

- ⏰ **时间感知**：考虑数据的时间顺序
- 🪟 **窗口采样**：支持不同的时间窗口策略
- 🔄 **多种模式**：最近、随机、顺序三种时间窗口

### 时间窗口类型

- **recent**：选择最近的数据
- **random**：随机选择时间段
- **sequential**：按时间顺序选择

### 适用场景

- **时序数据**：数据具有明显的时间特征
- **趋势分析**：需要分析时间趋势的场景
- **季节性数据**：具有季节性变化的数据

### 需要实现的功能

```python
def temporal_sample_by_time(data, time_window, window_size, total_samples):
    """时间序列采样函数 - 待实现"""
    # 1. 解析时间字段
    # 2. 根据时间窗口类型选择数据
    # 3. 在时间窗口内采样
    pass
```

## 🔧 采样策略选择指南

### 根据需求选择


| 需求                 | 推荐策略         | 原因                           |
| -------------------- | ---------------- | ------------------------------ |
| **Original级别评估** | Original级别采样 | 唯一支持Original级别评估的策略 |
| **快速实验**         | 平衡采样         | 实现完整，速度快               |
| **基准测试**         | 随机采样         | 简单无偏，适合对比             |
| **不平衡数据**       | 分层采样         | 保持数据分布特性               |
| **时序数据**         | 时间序列采样     | 考虑时间因素                   |

### 根据数据特点选择


| 数据特点             | 推荐策略           | 配置建议                |
| -------------------- | ------------------ | ----------------------- |
| **Original数量充足** | Original级别采样   | target_originals: 40-60 |
| **Original数量有限** | Original级别采样   | target_originals: 20-30 |
| **数据分布均匀**     | 平衡采样或随机采样 | 根据需求选择            |
| **数据分布不均**     | Original级别采样   | 确保平衡性              |
| **标签不平衡**       | 分层采样           | 待实现                  |
| **有时间特征**       | 时间序列采样       | 待实现                  |

### 根据资源选择


| 资源情况     | 推荐策略         | 样本数建议    |
| ------------ | ---------------- | ------------- |
| **资源充足** | Original级别采样 | 4000-6000样本 |
| **资源中等** | Original级别采样 | 2000-4000样本 |
| **资源有限** | 平衡采样         | 1000-2000样本 |
| **快速测试** | 随机采样         | 500-1000样本  |

## 🚀 实际使用建议

### 1. 新项目启动

```yaml
# 第一步：使用Original级别采样了解数据
config_original_balanced_224x224:
  enable: true
  dataset_config:
    sampling_strategy: "original_level_sampling"
    strategy_parameters:
      target_originals: 20
      samples_per_original: 30
      total_samples: 600
```

### 2. 正式训练

```yaml
# 第二步：扩大规模进行正式训练
config_original_balanced_224x224:
  enable: true
  dataset_config:
    sampling_strategy: "original_level_sampling"
    strategy_parameters:
      target_originals: 40
      samples_per_original: 60
      total_samples: 2400
```

### 3. 大规模训练

```yaml
# 第三步：资源充足时的大规模训练
config_balanced_224x224_large:
  enable: true
  dataset_config:
    sampling_strategy: "original_level_sampling"
    strategy_parameters:
      target_originals: 60
      samples_per_original: 80
      total_samples: 4800
```

## 📈 性能对比


| 策略             | 训练速度 | 评估质量 | 内存占用 | 实现复杂度 |
| ---------------- | -------- | -------- | -------- | ---------- |
| **Original级别** | 中等     | 最高     | 中等     | 高         |
| **平衡采样**     | 快       | 高       | 低       | 中         |
| **随机采样**     | 最快     | 中等     | 最低     | 低         |
| **分层采样**     | 中等     | 高       | 中等     | 中         |
| **时间序列**     | 中等     | 中等     | 中等     | 中         |

**总结**：Original级别采样是当前最推荐的策略，提供最全面的评估能力，特别适合您的数据特点（同一original下样本标签相同）。
