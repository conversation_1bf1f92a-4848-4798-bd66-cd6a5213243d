#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置MongoDB工具测试脚本

测试配置管理工具的各项功能
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

def test_mongo_connection():
    """测试MongoDB连接"""
    print("[TEST 1] 测试MongoDB连接")
    print("-" * 40)

    try:
        from utils.db_utils import database_connecter

        db_conn = database_connecter
        if db_conn is None:
            print("[FAILED] 无法获取数据库连接器")
            return False

        # 测试MongoDB连接
        databases = db_conn.list_mongodb_databases()
        print(f"[SUCCESS] MongoDB连接成功，可用数据库: {len(databases)}")
        print(f"数据库列表: {databases}")

        return True

    except Exception as e:
        print(f"[FAILED] MongoDB连接测试失败: {e}")
        return False

def test_yaml_loading():
    """测试YAML配置加载"""
    print("\n[TEST 2] 测试YAML配置加载")
    print("-" * 40)

    try:
        from tools.config.config_mongo_tool import ConfigMongoTool

        tool = ConfigMongoTool()
        config = tool.load_yaml_config()

        if config is None:
            print("[FAILED] 无法加载YAML配置")
            return False

        training_configs = config.get('training_configs', {})
        user_configs = [name for name in training_configs.keys() if name.startswith('user_')]

        print(f"[SUCCESS] YAML配置加载成功")
        print(f"总配置数: {len(training_configs)}")
        print(f"用户配置数: {len(user_configs)}")

        # 显示前5个用户配置
        print("前5个用户配置:")
        for i, config_name in enumerate(user_configs[:5], 1):
            print(f"  {i}. {config_name}")

        return True

    except Exception as e:
        print(f"[FAILED] YAML配置加载测试失败: {e}")
        return False

def test_config_tool_import():
    """测试配置导入功能"""
    print("\n[TEST 3] 测试配置导入功能")
    print("-" * 40)

    try:
        from tools.config.config_mongo_tool import ConfigMongoTool

        tool = ConfigMongoTool()

        # 测试导入
        result = tool.import_yaml_to_mongo()

        if result:
            print("[SUCCESS] 配置导入测试通过")
        else:
            print("[FAILED] 配置导入测试失败")

        return result

    except Exception as e:
        print(f"[FAILED] 配置导入测试异常: {e}")
        return False

def test_config_tool_list():
    """测试配置列表功能"""
    print("\n[TEST 4] 测试配置列表功能")
    print("-" * 40)

    try:
        from tools.config.config_mongo_tool import ConfigMongoTool

        tool = ConfigMongoTool()

        # 测试列表
        result = tool.list_mongo_configs()

        if result:
            print("[SUCCESS] 配置列表测试通过")
        else:
            print("[FAILED] 配置列表测试失败")

        return result

    except Exception as e:
        print(f"[FAILED] 配置列表测试异常: {e}")
        return False

def test_config_tool_compare():
    """测试配置对比功能"""
    print("\n[TEST 5] 测试配置对比功能")
    print("-" * 40)

    try:
        from tools.config.config_mongo_tool import ConfigMongoTool

        tool = ConfigMongoTool()

        # 测试对比
        result = tool.compare_yaml_mongo()

        if result:
            print("[SUCCESS] 配置对比测试通过")
        else:
            print("[FAILED] 配置对比测试失败")

        return result

    except Exception as e:
        print(f"[FAILED] 配置对比测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("配置MongoDB工具测试")
    print("=" * 60)

    # 检查依赖
    try:
        import pymongo
        print(f"[INFO] PyMongo 版本: {pymongo.version}")
    except ImportError:
        print("[ERROR] 请先安装 pymongo: pip install pymongo")
        return

    # 运行测试
    tests = [
        ("MongoDB连接", test_mongo_connection),
        ("YAML配置加载", test_yaml_loading),
        ("配置导入", test_config_tool_import),
        ("配置列表", test_config_tool_list),
        ("配置对比", test_config_tool_compare)
    ]

    passed_tests = 0
    total_tests = len(tests)

    for test_name, test_func in tests:
        try:
            if test_func():
                passed_tests += 1
        except Exception as e:
            print(f"[ERROR] {test_name} 测试异常: {e}")

    print("\n" + "=" * 60)
    print(f"测试完成: {passed_tests}/{total_tests} 通过")
    print("=" * 60)

    if passed_tests == total_tests:
        print("[SUCCESS] 所有测试通过，配置MongoDB工具可以正常使用")
    else:
        print("[WARNING] 部分测试失败，请检查配置和连接")

    print("\n使用方法:")
    print("python main.py  # 选择 '3. 配置管理工具'")
    print("或直接运行:")
    print("python runners/utilities/config_mongo_tool.py")

if __name__ == "__main__":
    main()
