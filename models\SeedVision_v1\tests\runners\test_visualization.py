#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像生成功能测试脚本

测试功能：
1. 训练损失曲线图
2. 蛋白质含量回归图
3. 含油量回归图
4. 综合可视化报告
"""

import sys
import os
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_loss_curve_generation():
    """测试损失曲线生成"""
    print("🧪 Testing Loss Curve Generation...")
    
    try:
        from tools.training.visualize import plot_train_val_loss
        
        # 创建模拟的训练数据
        epochs = 50
        # 模拟训练损失：开始高，逐渐降低，有一些波动
        train_losses = []
        val_losses = []
        
        for i in range(epochs):
            # 训练损失：指数衰减 + 随机噪声
            train_loss = 2.0 * np.exp(-0.08 * i) + 0.1 + 0.05 * np.random.randn()
            train_losses.append(max(0.05, train_loss))  # 确保不为负
            
            # 验证损失：稍微高一些，衰减稍慢
            val_loss = 2.2 * np.exp(-0.06 * i) + 0.15 + 0.08 * np.random.randn()
            val_losses.append(max(0.08, val_loss))  # 确保不为负
        
        # 创建输出目录
        output_dir = "output/test_visualizations"
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成损失曲线图
        save_path = os.path.join(output_dir, "test_loss_curve.png")
        plot_train_val_loss(
            train_losses,
            val_losses,
            title="Test Training and Validation Loss Curve",
            save_path=save_path
        )
        
        # 验证文件是否生成
        if os.path.exists(save_path):
            print(f"✅ Loss curve generated successfully: {save_path}")
            return True
        else:
            print("❌ Loss curve file not found")
            return False
            
    except Exception as e:
        print(f"❌ Loss curve generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_regression_plots():
    """测试回归图生成"""
    print("\n🧪 Testing Regression Plots Generation...")
    
    try:
        from tools.training.visualize import plot_protein_regression, plot_oil_regression
        
        # 创建输出目录
        output_dir = "output/test_visualizations"
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成模拟的蛋白质数据
        n_samples = 150
        np.random.seed(42)  # 确保结果可重现
        
        # 蛋白质含量：20-35% 范围
        protein_true = 20 + 15 * np.random.rand(n_samples)
        # 添加一些相关性和噪声
        protein_pred = 0.85 * protein_true + 3 + 2 * np.random.randn(n_samples)
        
        # 生成蛋白质回归图
        protein_path = os.path.join(output_dir, "test_protein_regression.png")
        plot_protein_regression(
            protein_true,
            protein_pred,
            title="Test Protein Content Prediction Results",
            save_path=protein_path
        )
        
        # 含油量：35-55% 范围
        oil_true = 35 + 20 * np.random.rand(n_samples)
        # 添加一些相关性和噪声
        oil_pred = 0.9 * oil_true + 2 + 2.5 * np.random.randn(n_samples)
        
        # 生成含油量回归图
        oil_path = os.path.join(output_dir, "test_oil_regression.png")
        plot_oil_regression(
            oil_true,
            oil_pred,
            title="Test Oil Content Prediction Results",
            save_path=oil_path
        )
        
        # 验证文件是否生成
        protein_exists = os.path.exists(protein_path)
        oil_exists = os.path.exists(oil_path)
        
        if protein_exists and oil_exists:
            print(f"✅ Protein regression plot: {protein_path}")
            print(f"✅ Oil regression plot: {oil_path}")
            return True
        else:
            print(f"❌ Missing files - Protein: {protein_exists}, Oil: {oil_exists}")
            return False
            
    except Exception as e:
        print(f"❌ Regression plots generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_comprehensive_visualization():
    """测试综合可视化功能"""
    print("\n🧪 Testing Comprehensive Visualization...")
    
    try:
        from tools.training.visualize import create_visualization_examples
        
        # 运行综合可视化示例
        print("Generating comprehensive visualization examples...")
        create_visualization_examples()
        
        # 检查生成的文件
        expected_files = [
            "visualization_results/loss_curve.png",
            "visualization_results/protein_regression.png",
            "visualization_results/oil_regression.png"
        ]
        
        all_exist = True
        for file_path in expected_files:
            if os.path.exists(file_path):
                print(f"✅ Generated: {file_path}")
            else:
                print(f"❌ Missing: {file_path}")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ Comprehensive visualization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_custom_visualization():
    """测试自定义可视化功能"""
    print("\n🧪 Testing Custom Visualization...")
    
    try:
        from tools.training.visualize import plot_prediction
        
        # 创建输出目录
        output_dir = "output/test_visualizations"
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成自定义预测数据
        n_samples = 100
        np.random.seed(123)
        
        # 创建一个有良好相关性的数据集
        true_values = np.random.uniform(10, 50, n_samples)
        predicted_values = 0.8 * true_values + 2 + 1.5 * np.random.randn(n_samples)
        
        # 生成自定义预测图
        custom_path = os.path.join(output_dir, "test_custom_prediction.png")
        plot_prediction(
            true_values,
            predicted_values,
            title="Test Custom Prediction Results",
            save_path=custom_path
        )
        
        # 验证文件是否生成
        if os.path.exists(custom_path):
            print(f"✅ Custom prediction plot: {custom_path}")
            return True
        else:
            print("❌ Custom prediction plot not generated")
            return False
            
    except Exception as e:
        print(f"❌ Custom visualization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_matplotlib_setup():
    """测试matplotlib设置"""
    print("\n🧪 Testing Matplotlib Setup...")
    
    try:
        import matplotlib
        import matplotlib.pyplot as plt
        
        # 检查后端设置
        backend = matplotlib.get_backend()
        print(f"Matplotlib backend: {backend}")
        
        # 检查字体设置
        font_family = plt.rcParams['font.sans-serif']
        print(f"Font family: {font_family}")
        
        # 创建一个简单的测试图
        fig, ax = plt.subplots(figsize=(6, 4))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y, label='sin(x)')
        ax.set_title('Matplotlib Test Plot')
        ax.set_xlabel('X axis')
        ax.set_ylabel('Y axis')
        ax.legend()
        ax.grid(True)
        
        # 保存测试图
        test_path = "output/test_visualizations/matplotlib_test.png"
        os.makedirs(os.path.dirname(test_path), exist_ok=True)
        plt.savefig(test_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        if os.path.exists(test_path):
            print(f"✅ Matplotlib test plot: {test_path}")
            return True
        else:
            print("❌ Matplotlib test plot not generated")
            return False
            
    except Exception as e:
        print(f"❌ Matplotlib setup test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_summary_report(results):
    """生成测试总结报告"""
    print("\n" + "="*60)
    print("📊 Visualization Test Summary Report")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    failed_tests = total_tests - passed_tests
    
    print(f"📈 Test Statistics:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Passed: {passed_tests}")
    print(f"   Failed: {failed_tests}")
    print(f"   Success Rate: {(passed_tests/total_tests*100):.1f}%")
    
    print(f"\n📋 Detailed Results:")
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    # 生成文件列表
    output_dir = "output/test_visualizations"
    if os.path.exists(output_dir):
        print(f"\n📁 Generated Files:")
        for file in os.listdir(output_dir):
            if file.endswith('.png'):
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"   📄 {file} ({file_size:.1f} KB)")
    
    # 检查visualization_results目录
    viz_dir = "visualization_results"
    if os.path.exists(viz_dir):
        print(f"\n📁 Example Visualizations:")
        for file in os.listdir(viz_dir):
            if file.endswith('.png'):
                file_path = os.path.join(viz_dir, file)
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"   📄 {file} ({file_size:.1f} KB)")
    
    if failed_tests == 0:
        print(f"\n🎉 All visualization tests passed!")
        print(f"💡 Image generation functionality is working correctly.")
    else:
        print(f"\n⚠️  {failed_tests} tests failed.")
        print(f"🔧 Please check the error messages above.")
    
    return failed_tests == 0

def main():
    """主函数"""
    print("🎨 SeedVision v1 - Image Generation Test Suite")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs("output/test_visualizations", exist_ok=True)
    
    # 运行测试
    tests = [
        ("Matplotlib Setup", test_matplotlib_setup),
        ("Loss Curve Generation", test_loss_curve_generation),
        ("Regression Plots", test_regression_plots),
        ("Custom Visualization", test_custom_visualization),
        ("Comprehensive Visualization", test_comprehensive_visualization),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 生成总结报告
    success = generate_summary_report(results)
    
    return success

if __name__ == "__main__":
    main()
