# 深度学习模型训练框架

## 项目介绍

本项目是基于Pytorch搭建，提供抽象类用于定义用户自己的模型，方便用户规范化实现模型，管理自己的模型，后续可能会提供网页或者图形化界面供用户可视化操作，期望发展成一个开源项目，提供统一的接口，让个人开发者和实验室团队可以靠这个管理自己的模型

## 项目结构

```

├── dataset 数据集保存
├── models 包含各种模型设计，每个模型单开一个文件夹，内部完成所需的全部代码，对外只提供data_loader和model_loader两个类
│   ├── abstract_class 抽象类，提供统一的接口，用户可以继承这个类，实现自己的模型
│   │   ├── base_model_loader.py 基类模型加载器，提供统一的接口
│   │   └── base_data_loader.py 基类数据加载器，提供统一的接口
│   ├── model1 模型1
|   │   ├── tools 模型1所需的工具文件，包括数据处理等，作为模型的内部代码，不对外提供，可以为空，取名tools是为了跟全局的utils区分
│   │   ├── model 模型结构文件夹，可能存在多个文件
│   │   ├── data_loader.py 数据加载器
│   │   ├── model_loader.py 模型加载器
│   │   └── design.md 模型设计文档
├── weights 模型权重保存
│   ├── model1.pth
│   ├── model2.pth
│   └── model3.pth
├── results 训练过程的结果保存，每类模型用一个文件夹保存，如果有从其他模型直接调用的，可能会不一样，因此需要提供基类接口
├── docs 文档保存
├── logs 日志文件保存
├── utils 各种工具方法的保存
│   ├── base_model_loader.py 基类模型加载器，提供统一的接口
│   ├── base_data_loader.py 基类数据加载器，提供统一的接口
│   └── logger.py 日志配置文件
├── src 主要训练代码保存
│   ├── data_loader.py 数据加载器，不同的模型需要设计不同的数据集，考虑最后每个数据集继承基类提供统一的接口
│   ├── model_loader.py 模型加载器，同上
│   └── main.py 训练过程
├── config.json 项目配置文件，包含模型、训练、数据等相关参数
├── .gitignore Git忽略文件
```
