import torch
import torch.nn as nn
from abc import ABC, abstractmethod
import os

class BaseModelLoader(ABC):
    """
    基础模型加载器，提供统一的接口
    
    负责模型的创建、加载、保存以及优化器的配置
    所有具体的模型加载器都应该继承这个类并实现抽象方法
    """
    
    @abstractmethod
    def create_model(self, **kwargs):
        """
        创建模型实例
        
        Args:
            **kwargs: 模型创建所需的参数，如输入维度、隐藏层大小等
            
        Returns:
            nn.Module: 创建的模型实例
        """
        pass
    
    @abstractmethod
    def load_weight(self, model_path, **kwargs):
        """
        加载预训练模型
        
        Args:
            model_path (str): 模型权重文件路径
            **kwargs: 其他加载参数
            
        Returns:
            nn.Module: 加载了权重的模型实例
        """
        pass
    
    def save_weight(self, model, save_path, epoch=None, optimizer=None, loss=None):
        """
        保存模型
        
        Args:
            model (nn.Module): 要保存的模型
            save_path (str): 保存路径
            epoch (int, optional): 当前训练轮次
            optimizer (Optimizer, optional): 优化器状态
            loss (float, optional): 当前损失值
        """
        # 确保保存目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # 保存模型和训练状态
        checkpoint = {
            'model_state_dict': model.state_dict(),
        }
        
        if epoch is not None:
            checkpoint['epoch'] = epoch
        if optimizer is not None:
            checkpoint['optimizer_state_dict'] = optimizer.state_dict()
        if loss is not None:
            checkpoint['loss'] = loss
            
        torch.save(checkpoint, save_path)
        print(f"模型已保存到 {save_path}")
    
    @abstractmethod
    def get_optimizer(self, model, **kwargs):
        """
        获取优化器
        
        Args:
            model (nn.Module): 模型实例
            **kwargs: 优化器参数，如学习率等
            
        Returns:
            Optimizer: 配置好的优化器
        """
        pass
    
    def get_scheduler(self, optimizer, **kwargs):
        """
        获取学习率调度器（可选实现）
        
        Args:
            optimizer (Optimizer): 优化器实例
            **kwargs: 调度器参数
            
        Returns:
            LRScheduler: 学习率调度器
        """
        return None