#!/usr/bin/env python3
"""
测试验证集修复的脚本
验证ZeroDivisionError是否已经修复
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('E:\Proj\pytorch-model-train')

from tools.myscripts.load_data import load_data, get_subset, load_batch
from config.config_loader import Config<PERSON><PERSON><PERSON>

def test_validation_sampling():
    """测试验证集采样是否能正常工作"""
    print("🧪 Testing Validation Sampling Fix...")
    print("="*60)
    
    try:
        # 加载数据
        print("Loading data...")
        data = load_data()
        val_data = get_subset(data, 'val')
        
        print(f"📊 Validation data: {len(val_data)} samples")
        
        # 测试原始配置（可能导致问题的配置）
        original_config = {
            'target_originals': 40,
            'samples_per_original': 60,
            'total_samples': 2400
        }
        
        # 计算修复后的验证集配置
        val_target_originals = max(5, min(15, original_config['target_originals'] // 3))
        val_samples_per_original = max(5, min(10, original_config['samples_per_original'] // 6))
        val_config = {
            'target_originals': val_target_originals,
            'samples_per_original': val_samples_per_original,
            'total_samples': val_target_originals * val_samples_per_original
        }
        
        print(f"\n🎯 Testing Validation Configuration:")
        print(f"  - Target originals: {val_config['target_originals']}")
        print(f"  - Samples per original: {val_config['samples_per_original']}")
        print(f"  - Expected total: {val_config['total_samples']}")
        
        # 尝试采样
        try:
            val_result = load_batch(
                val_data,
                use_fixed_sampling=True,
                original_sampling_config=val_config
            )
            
            if len(val_result) == 3:
                val_batch, val_mapping, _ = val_result
            else:
                val_batch, val_mapping = val_result
            
            print(f"\n✅ Validation sampling successful!")
            print(f"  - Actual samples: {len(val_batch)}")
            print(f"  - Originals used: {len(val_mapping)}")
            
            if len(val_batch) > 0:
                print(f"  - Average samples per original: {len(val_batch) / len(val_mapping):.1f}")
                return True
            else:
                print(f"  ⚠️  Warning: No validation samples obtained")
                return False
                
        except Exception as e:
            print(f"❌ Validation sampling failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_strategy():
    """测试备选策略是否工作"""
    print(f"\n🔄 Testing Fallback Strategy...")
    print("="*60)
    
    try:
        # 加载数据
        data = load_data()
        val_data = get_subset(data, 'val')
        
        # 测试传统采样作为备选方案
        val_sample_size = min(len(val_data), 200)
        val_batch = load_batch(
            val_data,
            balanced_sampling=True,
            target_size=val_sample_size,
            samples_per_original=5
        )
        
        print(f"✅ Fallback strategy successful!")
        print(f"  - Target size: {val_sample_size}")
        print(f"  - Actual samples: {len(val_batch)}")
        
        return len(val_batch) > 0
        
    except Exception as e:
        print(f"❌ Fallback strategy failed: {e}")
        return False

def simulate_training_validation():
    """模拟训练中的验证过程"""
    print(f"\n🏃 Simulating Training Validation Process...")
    print("="*60)
    
    try:
        import torch
        import torch.nn as nn
        from torch.utils.data import DataLoader
        from tools.myscripts.train import SeedDataset, validate
        
        # 创建一个简单的模型用于测试
        class DummyModel(nn.Module):
            def __init__(self):
                super().__init__()
                self.fc = nn.Linear(3*224*224, 2)  # 简单的全连接层
                
            def forward(self, x):
                x = x.view(x.size(0), -1)
                return self.fc(x)
        
        # 加载少量验证数据
        data = load_data()
        val_data = get_subset(data, 'val')
        
        val_config = {
            'target_originals': 5,
            'samples_per_original': 5,
            'total_samples': 25
        }
        
        val_result = load_batch(
            val_data,
            use_fixed_sampling=True,
            original_sampling_config=val_config
        )
        
        if len(val_result) == 3:
            val_batch, _, _ = val_result
        else:
            val_batch, _ = val_result
        
        if len(val_batch) == 0:
            print("⚠️  No validation data available, using fallback")
            val_batch = load_batch(
                val_data,
                balanced_sampling=True,
                target_size=50,
                samples_per_original=3
            )
        
        print(f"📊 Validation batch size: {len(val_batch)}")
        
        if len(val_batch) > 0:
            # 创建数据集和加载器
            val_dataset = SeedDataset(val_batch)
            val_loader = DataLoader(val_dataset, batch_size=min(10, len(val_batch)))
            
            # 创建模型和损失函数
            model = DummyModel()
            criterion = nn.MSELoss()
            device = torch.device("cpu")
            
            print(f"🔍 Testing validation function...")
            
            # 测试验证函数
            val_loss, val_metrics, val_preds, val_labels = validate(
                model, val_loader, criterion, device
            )
            
            print(f"✅ Validation function successful!")
            print(f"  - Validation loss: {val_loss:.4f}")
            print(f"  - Oil R2: {val_metrics['oil']['R2']:.4f}")
            print(f"  - Protein R2: {val_metrics['protein']['R2']:.4f}")
            print(f"  - Predictions shape: {val_preds.shape}")
            print(f"  - Labels shape: {val_labels.shape}")
            
            return True
        else:
            print("❌ No validation data available")
            return False
            
    except Exception as e:
        print(f"❌ Simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Testing Validation Fix...")
    print("="*80)
    
    # 测试验证集采样
    sampling_success = test_validation_sampling()
    
    # 测试备选策略
    fallback_success = test_fallback_strategy()
    
    # 模拟训练验证过程
    simulation_success = simulate_training_validation()
    
    # 总结
    print(f"\n" + "="*80)
    print("📊 VALIDATION FIX TEST RESULTS")
    print("="*80)
    print(f"✅ Validation Sampling: {'PASS' if sampling_success else 'FAIL'}")
    print(f"✅ Fallback Strategy: {'PASS' if fallback_success else 'FAIL'}")
    print(f"✅ Training Simulation: {'PASS' if simulation_success else 'FAIL'}")
    
    all_passed = sampling_success and fallback_success and simulation_success
    
    if all_passed:
        print(f"\n🎉 All validation tests passed! ZeroDivisionError should be fixed.")
        print(f"\n💡 The system is now ready for training:")
        print(f"  - Validation dataset will be properly sampled")
        print(f"  - Fallback strategies are in place")
        print(f"  - No more division by zero errors")
    else:
        print(f"\n❌ Some validation tests failed. Please check the errors above.")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
