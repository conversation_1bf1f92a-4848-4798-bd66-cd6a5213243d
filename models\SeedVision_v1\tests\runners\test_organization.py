#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件整理验证脚本

验证文件整理后各模块的导入和功能是否正常
"""

import sys
import os

# 添加路径
sys.path.append('E:\Proj\pytorch-model-train')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def test_imports():
    """测试各模块导入"""
    print(f"{Colors.BLUE}[TEST]{Colors.END} 测试模块导入")
    print("=" * 50)

    # 测试配置模块
    try:
        from config.config_loader import ConfigLoader
        print(f"{Colors.GREEN}[PASS]{Colors.END} config.config_loader - 导入成功")
    except Exception as e:
        print(f"{Colors.RED}[FAIL]{Colors.END} config.config_loader - 导入失败: {e}")

    # 测试模型模块
    try:
        from models.FasterNet import model as FasterNet_model
        print(f"{Colors.GREEN}[PASS]{Colors.END} models.FasterNet - 导入成功")
    except Exception as e:
        print(f"{Colors.RED}[FAIL]{Colors.END} models.FasterNet - 导入失败: {e}")

    # 测试数据工具
    try:
        from tools.data.load_data import load_data
        print(f"{Colors.GREEN}[PASS]{Colors.END} tools.data.load_data - 导入成功")
    except Exception as e:
        print(f"{Colors.RED}[FAIL]{Colors.END} tools.data.load_data - 导入失败: {e}")

    # 测试训练工具
    try:
        from tools.training.train import train_model
        print(f"{Colors.GREEN}[PASS]{Colors.END} tools.training.train - 导入成功")
    except Exception as e:
        print(f"{Colors.RED}[FAIL]{Colors.END} tools.training.train - 导入失败: {e}")

    # 测试验证工具
    try:
        from tools.training.validate import calculate_r2
        print(f"{Colors.GREEN}[PASS]{Colors.END} tools.training.validate - 导入成功")
    except Exception as e:
        print(f"{Colors.RED}[FAIL]{Colors.END} tools.training.validate - 导入失败: {e}")

    # 测试调度器
    try:
        from scheduler import SchedulerManager, ResourceEstimator
        print(f"{Colors.GREEN}[PASS]{Colors.END} scheduler - 导入成功")
    except Exception as e:
        print(f"{Colors.RED}[FAIL]{Colors.END} scheduler - 导入失败: {e}")

def test_directory_structure():
    """测试目录结构"""
    print(f"\n{Colors.CYAN}[TEST]{Colors.END} 测试目录结构")
    print("=" * 50)

    expected_dirs = [
        'docs',
        'config',
        'models',
        'tools/data',
        'tools/training',
        'tools/analysis',
        'scheduler',
        'tests/unit',
        'tests/integration',
        'tests/examples',
        'tests/debug',
        'output/models',
        'output/results',
        'output/logs',
        'output/visualizations',
        'scripts'
    ]

    for dir_path in expected_dirs:
        if os.path.exists(dir_path):
            print(f"{Colors.GREEN}[PASS]{Colors.END} {dir_path} - 目录存在")
        else:
            print(f"{Colors.RED}[FAIL]{Colors.END} {dir_path} - 目录不存在")

def test_file_locations():
    """测试关键文件位置"""
    print(f"\n{Colors.MAGENTA}[TEST]{Colors.END} 测试关键文件位置")
    print("=" * 50)

    expected_files = [
        'docs/README.md',
        'config/config_loader.py',
        'config/training_config.yaml',
        'models/FasterNet.py',
        'tools/data/load_data.py',
        'tools/training/train.py',
        'tools/training/validate.py',
        'scheduler/resource_estimator.py',
        'scheduler/task_scheduler.py',
        'scheduler/process_manager.py',
        'tests/examples/test_original_evaluation.py',
        'main.py',
        'main_scheduler.py'
    ]

    for file_path in expected_files:
        if os.path.exists(file_path):
            print(f"{Colors.GREEN}[PASS]{Colors.END} {file_path} - 文件存在")
        else:
            print(f"{Colors.RED}[FAIL]{Colors.END} {file_path} - 文件不存在")

def test_scheduler_functionality():
    """测试调度器功能"""
    print(f"\n{Colors.YELLOW}[TEST]{Colors.END} 测试调度器功能")
    print("=" * 50)

    try:
        from scheduler import ResourceEstimator

        estimator = ResourceEstimator()

        # 测试系统资源获取
        resources = estimator.get_system_resources()
        print(f"{Colors.GREEN}[PASS]{Colors.END} 系统资源获取成功")
        print(f"   - CPU使用率: {resources['cpu']['usage_percent']:.1f}%")
        print(f"   - 内存可用: {resources['memory']['available_gb']:.1f}GB")

        if resources['gpu']:
            gpu_0 = resources['gpu']['gpu_0']
            print(f"   - GPU: {gpu_0['name']}")
            print(f"   - GPU可用显存: {gpu_0['free_gb']:.1f}GB")

        # 测试资源预估
        test_config = {
            'name': 'test_config',
            'model': {
                'embed_dim': 192,
                'depths': [3, 4, 18, 3]
            },
            'resources': {
                'batch_size': 40
            },
            'transform_config': '224x224_norm',
            'training': {
                'num_epochs': 100
            },
            'dataset_config': {
                'strategy_parameters': {
                    'total_samples': 2400
                }
            }
        }

        report = estimator.generate_resource_report(test_config)
        print(f"{Colors.GREEN}[PASS]{Colors.END} 资源预估成功")
        print(f"   - 预估显存: {report['memory_estimate']['total_gb']:.2f}GB")
        print(f"   - 预估时间: {report['time_estimate']['total_hours']:.1f}小时")
        print(f"   - 可运行: {report['can_run']}")

    except Exception as e:
        print(f"{Colors.RED}[FAIL]{Colors.END} 调度器功能测试失败: {e}")

def main():
    """主函数"""
    print(f"{Colors.BOLD}[VERIFICATION]{Colors.END} SeedVision v1 - 文件整理验证")
    print("=" * 80)

    # 测试导入
    test_imports()

    # 测试目录结构
    test_directory_structure()

    # 测试文件位置
    test_file_locations()

    # 测试调度器功能
    test_scheduler_functionality()

    print(f"\n{Colors.GREEN}[COMPLETE]{Colors.END} 文件整理验证完成！")
    print(f"\n{Colors.BLUE}[INFO]{Colors.END} 使用建议:")
    print("1. 如果有导入错误，请检查Python路径设置")
    print("2. 如果有文件缺失，请检查文件移动是否完整")
    print("3. 新的项目结构更加清晰，便于维护")

if __name__ == "__main__":
    main()
