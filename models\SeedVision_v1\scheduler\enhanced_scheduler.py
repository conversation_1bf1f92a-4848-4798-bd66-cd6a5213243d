#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版调度器管理器 - Enhanced Scheduler Manager

功能：
1. 动态任务队列管理
2. 自动任务测试 (1 epoch)
3. 智能任务过滤
4. 完整的日志记录
5. 实时状态监控
"""

import time
import logging
import os
from datetime import datetime
from typing import List, Dict, Optional
from .task_scheduler import TaskScheduler, TrainingTask, TaskPriority, TaskStatus
from .resource_estimator import ResourceEstimator
from .process_manager import ProcessManager

class EnhancedSchedulerManager:
    """增强版调度器管理器"""
    
    def __init__(self, max_gpu_memory: float = 8.0, max_concurrent_tasks: int = 1):
        """
        初始化增强版调度器管理器
        
        参数:
            max_gpu_memory: 最大GPU显存限制 (GB)
            max_concurrent_tasks: 最大并发任务数
        """
        self.max_gpu_memory = max_gpu_memory
        self.max_concurrent_tasks = max_concurrent_tasks
        
        # 初始化组件
        self.task_scheduler = TaskScheduler(max_gpu_memory, max_concurrent_tasks)
        self.resource_estimator = ResourceEstimator()
        self.process_manager = ProcessManager()
        
        # 设置日志
        self.setup_logging()
        
        # 状态监控
        self.monitoring_enabled = False
        self.last_status_report = None
        
        self.logger.info("Enhanced Scheduler Manager initialized")
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_dir = "logs/enhanced_scheduler"
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置主日志
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = os.path.join(log_dir, f"enhanced_scheduler_{timestamp}.log")
        
        # 配置logger
        self.logger = logging.getLogger(f"{__name__}.EnhancedScheduler")
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        self.logger.setLevel(logging.INFO)
    
    def submit_tasks(self, configs: List[Dict], priority: TaskPriority = TaskPriority.NORMAL) -> List[str]:
        """
        批量提交任务
        
        参数:
            configs: 任务配置列表
            priority: 任务优先级
            
        返回:
            任务ID列表
        """
        task_ids = []
        
        self.logger.info(f"Submitting {len(configs)} tasks with priority {priority.name}")
        
        for i, config in enumerate(configs):
            # 创建任务
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i:03d}"
            task_name = config.get('name', f"Task_{i+1}")
            
            task = TrainingTask(
                task_id=task_id,
                name=task_name,
                config=config,
                priority=priority
            )
            
            # 设置回调函数
            task.on_test_start = self._on_test_start
            task.on_test_complete = self._on_test_complete
            task.on_test_failed = self._on_test_failed
            task.on_start = self._on_task_start
            task.on_complete = self._on_task_complete
            task.on_error = self._on_task_error
            
            # 提交任务
            submitted_id = self.task_scheduler.add_task(task)
            task_ids.append(submitted_id)
            
            self.logger.info(f"Task {task_id} ({task_name}) submitted")
        
        return task_ids
    
    def start_scheduling(self, enable_monitoring: bool = True):
        """
        启动调度
        
        参数:
            enable_monitoring: 是否启用状态监控
        """
        self.logger.info("Starting enhanced task scheduling")
        
        # 启动任务调度器
        self.task_scheduler.start_scheduler()
        
        # 启用监控
        if enable_monitoring:
            self.monitoring_enabled = True
            self.logger.info("Status monitoring enabled")
        
        self.logger.info("Enhanced scheduler started successfully")
    
    def stop_scheduling(self):
        """停止调度"""
        self.logger.info("Stopping enhanced task scheduling")
        
        # 停止监控
        self.monitoring_enabled = False
        
        # 停止调度器
        self.task_scheduler.stop_scheduler()
        
        self.logger.info("Enhanced scheduler stopped")
    
    def get_comprehensive_status(self) -> Dict:
        """获取综合状态报告"""
        queue_status = self.task_scheduler.get_queue_status()
        test_summary = self.task_scheduler.get_test_summary()
        system_resources = self.resource_estimator.get_system_resources()
        
        status = {
            'timestamp': datetime.now().isoformat(),
            'queue_status': queue_status,
            'test_summary': test_summary,
            'system_resources': system_resources,
            'scheduler_info': {
                'max_gpu_memory': self.max_gpu_memory,
                'max_concurrent_tasks': self.max_concurrent_tasks,
                'monitoring_enabled': self.monitoring_enabled
            }
        }
        
        self.last_status_report = status
        return status
    
    def get_task_details(self, task_id: str) -> Optional[Dict]:
        """获取任务详细信息"""
        task = self.task_scheduler.get_task_status(task_id)
        if not task:
            return None
        
        test_duration = None
        if task.test_start_time and task.test_end_time:
            test_duration = (task.test_end_time - task.test_start_time).total_seconds()
        
        training_duration = None
        if task.start_time and task.end_time:
            training_duration = (task.end_time - task.start_time).total_seconds()
        elif task.start_time:
            training_duration = (datetime.now() - task.start_time).total_seconds()
        
        return {
            'task_id': task.task_id,
            'name': task.name,
            'status': task.status.value,
            'priority': task.priority.name,
            'created_time': task.created_time.isoformat(),
            'estimated_memory_gb': task.estimated_memory_gb,
            'estimated_time_hours': task.estimated_time_hours,
            'test_info': {
                'test_passed': task.test_passed,
                'test_start_time': task.test_start_time.isoformat() if task.test_start_time else None,
                'test_end_time': task.test_end_time.isoformat() if task.test_end_time else None,
                'test_duration_seconds': test_duration,
                'test_error_message': task.test_error_message
            },
            'training_info': {
                'start_time': task.start_time.isoformat() if task.start_time else None,
                'end_time': task.end_time.isoformat() if task.end_time else None,
                'training_duration_seconds': training_duration,
                'process_id': task.process_id,
                'error_message': task.error_message
            },
            'log_files': {
                'training_log': task.log_file_path,
                'test_log': task.test_log_file_path
            }
        }
    
    def monitor_status(self, interval_seconds: int = 30):
        """
        状态监控循环
        
        参数:
            interval_seconds: 监控间隔 (秒)
        """
        self.logger.info(f"Starting status monitoring (interval: {interval_seconds}s)")
        
        while self.monitoring_enabled:
            try:
                status = self.get_comprehensive_status()
                self._log_status_summary(status)
                time.sleep(interval_seconds)
            except Exception as e:
                self.logger.error(f"Error in status monitoring: {e}")
                time.sleep(interval_seconds)
    
    def _log_status_summary(self, status: Dict):
        """记录状态摘要"""
        queue = status['queue_status']
        test = status['test_summary']
        
        self.logger.info(
            f"Status: Pending={queue['pending_count']}, "
            f"Testing={queue['testing_count']}, "
            f"Running={queue['running_count']}, "
            f"Completed={queue['completed_count']}, "
            f"Failed={queue['failed_count']}, "
            f"TestFailed={queue['test_failed_count']}, "
            f"TestSuccessRate={test['test_success_rate']:.1f}%"
        )
    
    # 回调函数
    def _on_test_start(self, task: TrainingTask):
        """测试开始回调"""
        self.logger.info(f"Test started for task {task.task_id} ({task.name})")
    
    def _on_test_complete(self, task: TrainingTask):
        """测试完成回调"""
        duration = (task.test_end_time - task.test_start_time).total_seconds()
        self.logger.info(f"Test completed for task {task.task_id} ({task.name}) in {duration:.1f}s")
    
    def _on_test_failed(self, task: TrainingTask, error: Exception):
        """测试失败回调"""
        self.logger.warning(f"Test failed for task {task.task_id} ({task.name}): {error}")
    
    def _on_task_start(self, task: TrainingTask):
        """训练开始回调"""
        self.logger.info(f"Training started for task {task.task_id} ({task.name})")
    
    def _on_task_complete(self, task: TrainingTask):
        """训练完成回调"""
        if task.start_time and task.end_time:
            duration = (task.end_time - task.start_time).total_seconds() / 3600
            self.logger.info(f"Training completed for task {task.task_id} ({task.name}) in {duration:.2f}h")
    
    def _on_task_error(self, task: TrainingTask, error: Exception):
        """训练错误回调"""
        self.logger.error(f"Training failed for task {task.task_id} ({task.name}): {error}")
    
    def generate_final_report(self) -> Dict:
        """生成最终报告"""
        status = self.get_comprehensive_status()
        task_history = self.task_scheduler.get_task_history()
        
        return {
            'report_timestamp': datetime.now().isoformat(),
            'final_status': status,
            'task_history': task_history,
            'summary': {
                'total_tasks': len(task_history),
                'successful_tasks': len([t for t in task_history if t['status'] == 'completed']),
                'failed_tasks': len([t for t in task_history if t['status'] in ['failed', 'test_failed']]),
                'test_success_rate': status['test_summary']['test_success_rate'],
                'scheduler_uptime': 'N/A'  # TODO: 计算运行时间
            }
        }
