'''
数据加载脚本 - 支持基于original_image的平衡采样
'''
import sys
sys.path.append('E:\Proj\pytorch-model-train')

from utils.db_utils import mysql_connecter
from PIL import Image
import random
from collections import defaultdict
import numpy as np

def load_data():
    """加载所有数据"""
    # 连接数据库
    db_name = 'SeedVision'
    table_name = 'yolotrain2'

    with mysql_connecter.cursor() as cursor:
        cursor.execute(f"USE {db_name}")
        # 查询指定类型的数据
        cursor.execute(f"SELECT * FROM {table_name}")
        # 获取查询结果
        data = cursor.fetchall()

    return data

def get_subset(data, type_):
    """筛选出指定类型的数据"""
    subset = [item for item in data if item['type'] == type_]
    return subset

def analyze_original_images(data):
    """分析original_image的分布情况"""
    original_groups = defaultdict(list)

    for item in data:
        original_image = item.get('original_image', 'unknown')
        original_groups[original_image].append(item)

    print(f"📊 Original Image Analysis:")
    print(f"  - Total unique original images: {len(original_groups)}")
    print(f"  - Total data points: {len(data)}")

    # 统计每个original_image的数据量
    counts = [len(items) for items in original_groups.values()]
    print(f"  - Data per original image: min={min(counts)}, max={max(counts)}, mean={np.mean(counts):.1f}")

    # 显示前10个最多数据的original_image
    sorted_groups = sorted(original_groups.items(), key=lambda x: len(x[1]), reverse=True)
    print(f"  - Top 10 original images by count:")
    for i, (orig_name, items) in enumerate(sorted_groups[:10]):
        print(f"    {i+1}. {orig_name}: {len(items)} items")

    return original_groups

def balanced_sample_by_original(data, target_size, samples_per_original=None, seed=42):
    """
    基于original_image进行平衡采样

    参数:
        data: 数据列表
        target_size: 目标采样数量
        samples_per_original: 每个original_image最多采样的数量，如果为None则自动计算
        seed: 随机种子

    返回:
        sampled_data: 采样后的数据列表
    """
    random.seed(seed)
    np.random.seed(seed)

    # 按original_image分组
    original_groups = analyze_original_images(data)

    # 如果没有指定每个original的采样数量，则自动计算
    if samples_per_original is None:
        # 计算平均每个original应该贡献多少数据
        num_originals = len(original_groups)
        samples_per_original = max(1, target_size // num_originals)

        # 如果平均分配后还有剩余，允许部分original多采样一些
        remaining = target_size - (samples_per_original * num_originals)
        if remaining > 0:
            samples_per_original += 1

    print(f"🎯 Sampling Strategy:")
    print(f"  - Target total samples: {target_size}")
    print(f"  - Max samples per original: {samples_per_original}")

    sampled_data = []
    original_sample_counts = {}

    # 对每个original_image进行采样
    for original_name, items in original_groups.items():
        # 确定这个original要采样多少数据
        available_count = len(items)
        sample_count = min(samples_per_original, available_count)

        # 如果已经达到目标数量，停止采样
        if len(sampled_data) >= target_size:
            break

        # 确保不超过目标总数
        remaining_needed = target_size - len(sampled_data)
        sample_count = min(sample_count, remaining_needed)

        # 随机采样
        if sample_count > 0:
            sampled_items = random.sample(items, sample_count)
            sampled_data.extend(sampled_items)
            original_sample_counts[original_name] = sample_count

    # 如果采样数量不足，从数据较多的original中补充
    if len(sampled_data) < target_size:
        print(f"⚠️  Initial sampling only got {len(sampled_data)} samples, need {target_size - len(sampled_data)} more")

        # 找出还有剩余数据的original
        remaining_items = []
        for original_name, items in original_groups.items():
            used_count = original_sample_counts.get(original_name, 0)
            if len(items) > used_count:
                # 添加未使用的数据
                unused_items = [item for item in items if item not in sampled_data]
                remaining_items.extend(unused_items)

        # 从剩余数据中随机采样补充
        needed = target_size - len(sampled_data)
        if remaining_items and needed > 0:
            additional_samples = random.sample(remaining_items, min(needed, len(remaining_items)))
            sampled_data.extend(additional_samples)

    print(f"✅ Sampling completed:")
    print(f"  - Final sample size: {len(sampled_data)}")
    print(f"  - Originals used: {len(original_sample_counts)}")

    # 显示采样统计
    if len(original_sample_counts) <= 20:  # 如果original数量不多，显示详细统计
        print(f"  - Samples per original:")
        for orig_name, count in sorted(original_sample_counts.items(), key=lambda x: x[1], reverse=True):
            print(f"    {orig_name}: {count}")

    return sampled_data

def fixed_sample_by_original_cross_subset(all_data, original_sampling_config=None, seed=42):
    """
    跨子集固定采样：从所有数据中为每个original采样指定数量，无视train/val/test划分

    参数:
        all_data: 所有数据列表（包含train/val/test）
        original_sampling_config: original采样配置字典
        seed: 随机种子

    返回:
        train_data: 训练集数据
        val_data: 验证集数据
        test_data: 测试集数据
        original_mapping: original到数据的映射
    """
    random.seed(seed)
    np.random.seed(seed)

    # 使用默认配置如果没有提供
    if original_sampling_config is None:
        original_sampling_config = {
            'target_originals': 40,
            'samples_per_original': 60,
            'train_ratio': 0.8,  # 训练集比例
            'val_ratio': 0.1,    # 验证集比例
            'test_ratio': 0.1    # 测试集比例
        }

    samples_per_original = original_sampling_config['samples_per_original']
    target_originals = original_sampling_config['target_originals']
    train_ratio = original_sampling_config.get('train_ratio', 0.8)
    val_ratio = original_sampling_config.get('val_ratio', 0.1)
    test_ratio = original_sampling_config.get('test_ratio', 0.1)

    # 按original_image分组（使用所有数据）
    print(f"🎯 Cross-Subset Fixed Sampling Strategy:")
    print(f"  - Samples per original: {samples_per_original}")
    print(f"  - Target originals: {target_originals}")
    print(f"  - Train/Val/Test ratio: {train_ratio:.1f}/{val_ratio:.1f}/{test_ratio:.1f}")

    original_groups = analyze_original_images(all_data)

    # 筛选有足够数据的original
    valid_originals = {}
    for original_name, items in original_groups.items():
        if len(items) >= samples_per_original:
            valid_originals[original_name] = items

    print(f"  - Valid originals (>={samples_per_original} samples): {len(valid_originals)}")

    # 如果有效original数量不足，调整策略
    if len(valid_originals) < target_originals:
        print(f"⚠️  Only {len(valid_originals)} originals have >={samples_per_original} samples")
        target_originals = len(valid_originals)

    # 随机选择target_originals个original
    selected_originals = random.sample(list(valid_originals.keys()), target_originals)

    train_data = []
    val_data = []
    test_data = []
    original_mapping = {}

    # 计算每个子集应该分配的样本数
    train_samples_per_original = int(samples_per_original * train_ratio)
    val_samples_per_original = int(samples_per_original * val_ratio)
    test_samples_per_original = samples_per_original - train_samples_per_original - val_samples_per_original

    print(f"  - Samples per original distribution:")
    print(f"    Train: {train_samples_per_original}, Val: {val_samples_per_original}, Test: {test_samples_per_original}")

    for original_name in selected_originals:
        items = valid_originals[original_name]
        # 从该original中随机采样指定数量
        sampled_items = random.sample(items, samples_per_original)

        # 按比例分配到不同子集
        train_items = sampled_items[:train_samples_per_original]
        val_items = sampled_items[train_samples_per_original:train_samples_per_original + val_samples_per_original]
        test_items = sampled_items[train_samples_per_original + val_samples_per_original:]

        train_data.extend(train_items)
        val_data.extend(val_items)
        test_data.extend(test_items)

        # 记录映射关系
        original_mapping[original_name] = {
            'all': sampled_items,
            'train': train_items,
            'val': val_items,
            'test': test_items
        }

    print(f"✅ Cross-subset sampling completed:")
    print(f"  - Selected originals: {len(selected_originals)}")
    print(f"  - Train samples: {len(train_data)}")
    print(f"  - Val samples: {len(val_data)}")
    print(f"  - Test samples: {len(test_data)}")
    print(f"  - Total samples: {len(train_data) + len(val_data) + len(test_data)}")

    return train_data, val_data, test_data, original_mapping

def fixed_sample_by_original(data, original_sampling_config=None, seed=42):
    """
    固定每个original采样指定数量的数据（原有函数，保持兼容性）

    参数:
        data: 数据列表
        original_sampling_config: original采样配置字典，包含target_originals, samples_per_original等
        seed: 随机种子

    返回:
        sampled_data: 采样后的数据列表
        original_mapping: original到数据的映射，用于后续评估
    """
    random.seed(seed)
    np.random.seed(seed)

    # 使用默认配置如果没有提供
    if original_sampling_config is None:
        original_sampling_config = {
            'target_originals': 40,
            'samples_per_original': 60,
            'total_samples': 2400
        }

    samples_per_original = original_sampling_config['samples_per_original']
    target_originals = original_sampling_config['target_originals']

    # 按original_image分组
    original_groups = analyze_original_images(data)

    print(f"🎯 Fixed Sampling Strategy:")
    print(f"  - Samples per original: {samples_per_original}")
    print(f"  - Target originals: {target_originals}")
    print(f"  - Available originals: {len(original_groups)}")

    # 筛选有足够数据的original
    valid_originals = {}
    for original_name, items in original_groups.items():
        if len(items) >= samples_per_original:
            valid_originals[original_name] = items

    print(f"  - Valid originals (>={samples_per_original} samples): {len(valid_originals)}")

    # 如果有效original数量不足，调整策略
    if len(valid_originals) < target_originals:
        print(f"⚠️  Only {len(valid_originals)} originals have >={samples_per_original} samples")
        target_originals = len(valid_originals)

    # 随机选择target_originals个original
    selected_originals = random.sample(list(valid_originals.keys()), target_originals)

    sampled_data = []
    original_mapping = {}  # 用于存储每个original的采样数据，便于后续评估

    for original_name in selected_originals:
        items = valid_originals[original_name]
        # 从该original中随机采样指定数量
        sampled_items = random.sample(items, samples_per_original)
        sampled_data.extend(sampled_items)

        # 记录映射关系
        original_mapping[original_name] = sampled_items

    total_samples = len(sampled_data)
    print(f"✅ Fixed sampling completed:")
    print(f"  - Selected originals: {len(selected_originals)}")
    print(f"  - Total samples: {total_samples}")
    print(f"  - Samples per original: {samples_per_original}")
    print(f"  - Expected total: {original_sampling_config['total_samples']}")

    return sampled_data, original_mapping

def get_batch(data, batch_size, start_id=1):
    """从data里分割获取batch（保持原有接口兼容性）"""
    batch = data[start_id:start_id+batch_size]
    return batch

def create_balanced_batch_sampler(original_mapping, batch_size=40):
    """
    创建平衡的batch采样器，确保每个batch从每个original平均取1张

    参数:
        original_mapping: original到数据的映射 {original_name: [data_items]}
        batch_size: batch大小，应该等于original数量

    返回:
        batch_indices: 每个batch的数据索引列表
    """
    # 创建数据索引映射
    data_list = []
    original_indices = {}  # {original_name: [indices]}

    idx = 0
    for original_name, items in original_mapping.items():
        original_indices[original_name] = []
        for item in items:
            data_list.append((item, original_name))
            original_indices[original_name].append(idx)
            idx += 1

    # 计算总的batch数量
    samples_per_original = len(list(original_mapping.values())[0])  # 假设每个original都有相同数量的样本
    total_batches = samples_per_original

    print(f"🎯 Creating balanced batch sampler:")
    print(f"  - Total originals: {len(original_mapping)}")
    print(f"  - Samples per original: {samples_per_original}")
    print(f"  - Batch size: {batch_size}")
    print(f"  - Total batches: {total_batches}")

    batch_indices_list = []

    for batch_idx in range(total_batches):
        batch_indices = []

        # 从每个original中取第batch_idx个样本
        for original_name in original_mapping.keys():
            if batch_idx < len(original_indices[original_name]):
                batch_indices.append(original_indices[original_name][batch_idx])

        batch_indices_list.append(batch_indices)

    return data_list, batch_indices_list

def load_batch(batch, balanced_sampling=True, target_size=None, samples_per_original=None, use_fixed_sampling=False, original_sampling_config=None, preserve_original_info=False):
    """
    从batch里将数据实例加载并形成对应标签返回给模型

    参数:
        batch: 数据批次
        balanced_sampling: 是否启用基于original_image的平衡采样
        target_size: 目标采样数量，如果为None则使用所有数据
        samples_per_original: 每个original_image最多采样的数量
        use_fixed_sampling: 是否使用固定采样策略（每个original固定数量）
        original_sampling_config: original采样配置字典
        preserve_original_info: 是否保留original信息（即使不使用固定采样）

    返回:
        batch_data: 处理后的数据列表 [(image, oil, protein), ...] 或 [(image, oil, protein, original_image), ...]
        original_mapping: 如果使用固定采样，返回original到数据的映射
        batch_sampler_info: 如果使用固定采样，返回batch采样信息
    """

    original_mapping = None
    batch_sampler_info = None

    # 选择采样策略
    if use_fixed_sampling:
        if original_sampling_config:
            print(f"🔄 Applying fixed sampling: {original_sampling_config['samples_per_original']} samples per original, target {original_sampling_config['target_originals']} originals")
        else:
            print(f"🔄 Applying fixed sampling: 60 samples per original, target 40 originals (default)")
        batch, original_mapping = fixed_sample_by_original(batch, original_sampling_config=original_sampling_config)
    elif balanced_sampling and target_size is not None and len(batch) > target_size:
        print(f"🔄 Applying balanced sampling: {len(batch)} -> {target_size}")
        batch = balanced_sample_by_original(batch, target_size, samples_per_original)

    batch_data = []
    failed_count = 0

    for item in batch:
        try:
            image_path = item['path']
            oil = float(item['oil'])
            protein = float(item['protein'])
            original_image = item.get('original_image', 'unknown')

            # 尝试打开图像
            image = Image.open(image_path)

            # 根据是否使用固定采样或保留original信息决定返回格式
            if use_fixed_sampling or preserve_original_info:
                batch_data.append((image, oil, protein, original_image))  # 4元组格式
            else:
                batch_data.append((image, oil, protein))  # 3元组格式（兼容旧版本）

        except Exception as e:
            failed_count += 1
            print(f"⚠️  Failed to load {item.get('path', 'unknown')}: {e}")
            continue

    if failed_count > 0:
        print(f"⚠️  Failed to load {failed_count} images out of {len(batch)}")

    print(f"✅ Successfully loaded {len(batch_data)} images")

    # 如果使用固定采样，创建平衡的batch采样器
    if use_fixed_sampling and original_mapping:
        # 重新组织数据以便于batch采样
        organized_mapping = {}

        for original_name, items in original_mapping.items():
            organized_items = []
            for item in items:
                # 找到对应的processed data
                for processed_item in batch_data:
                    if len(processed_item) == 4 and processed_item[3] == original_name:
                        organized_items.append(processed_item)
                        break
            organized_mapping[original_name] = organized_items

        # 创建batch采样信息
        data_list, batch_indices_list = create_balanced_batch_sampler(organized_mapping, batch_size=len(organized_mapping))
        batch_sampler_info = {
            'data_list': data_list,
            'batch_indices_list': batch_indices_list,
            'original_mapping': organized_mapping
        }

    if use_fixed_sampling:
        return batch_data, original_mapping, batch_sampler_info
    else:
        return batch_data
