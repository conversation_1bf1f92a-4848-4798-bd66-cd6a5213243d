#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新组织scripts目录，避免命名冲突

将scripts目录重命名为更安全的名称
"""

import os
import shutil

def reorganize_scripts_directory():
    """重新组织scripts目录"""
    print("🔧 Reorganizing scripts directory to avoid conflicts...")
    
    # 检查scripts目录是否存在
    if not os.path.exists("scripts"):
        print("   ⚠️  scripts directory not found")
        return False
    
    # 新的目录名称选项
    new_dir_options = [
        "runners",      # 运行器
        "executables",  # 可执行文件
        "launchers",    # 启动器
        "commands",     # 命令
        "bin"          # 二进制/可执行文件（Unix风格）
    ]
    
    # 选择新目录名
    new_dir_name = "runners"  # 使用runners作为新名称
    
    try:
        # 重命名scripts目录
        if os.path.exists(new_dir_name):
            print(f"   ⚠️  {new_dir_name} directory already exists, removing it first")
            shutil.rmtree(new_dir_name)
        
        shutil.move("scripts", new_dir_name)
        print(f"   ✅ Renamed 'scripts' to '{new_dir_name}'")
        
        # 更新目录结构
        print(f"\n📁 New directory structure:")
        for root, dirs, files in os.walk(new_dir_name):
            level = root.replace(new_dir_name, '').count(os.sep)
            indent = '   ' * level
            print(f"{indent}📂 {os.path.basename(root)}/")
            subindent = '   ' * (level + 1)
            for file in files:
                if file.endswith('.py'):
                    print(f"{subindent}📄 {file}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to reorganize: {e}")
        return False

def update_documentation():
    """更新文档中的路径引用"""
    print("\n📝 Updating documentation...")
    
    files_to_update = [
        "README.md",
        "PROJECT_STRUCTURE.md",
        "docs/README.md"
    ]
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换scripts引用为runners
                updated_content = content.replace('scripts/', 'runners/')
                updated_content = updated_content.replace('scripts\\', 'runners\\')
                updated_content = updated_content.replace('📁 scripts/', '📁 runners/')
                updated_content = updated_content.replace('├── scripts/', '├── runners/')
                updated_content = updated_content.replace('└── scripts/', '└── runners/')
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(updated_content)
                
                print(f"   ✅ Updated {file_path}")
                
            except Exception as e:
                print(f"   ⚠️  Failed to update {file_path}: {e}")

def test_import_conflicts():
    """测试导入冲突"""
    print("\n🧪 Testing for import conflicts...")
    
    # 测试是否还有scripts导入冲突
    try:
        import sys
        if 'scripts' in sys.modules:
            del sys.modules['scripts']
        
        # 尝试导入scripts看是否还有冲突
        try:
            import scripts
            print("   ⚠️  'scripts' can still be imported - potential conflict exists")
            return False
        except ImportError:
            print("   ✅ 'scripts' import conflict resolved")
            return True
            
    except Exception as e:
        print(f"   ⚠️  Error testing imports: {e}")
        return False

def create_convenience_scripts():
    """创建便捷启动脚本"""
    print("\n📜 Creating convenience launcher scripts...")
    
    convenience_scripts = {
        "train.py": """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
便捷训练启动脚本
\"\"\"
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    from runners.training.main import main
    main()
""",
        "train_scheduler.py": """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
便捷调度器启动脚本
\"\"\"
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    from runners.training.main_scheduler import main
    main()
""",
        "run_tests.py": """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
便捷测试启动脚本
\"\"\"
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    from tests.scripts.run_tests import main
    main()
"""
    }
    
    for script_name, script_content in convenience_scripts.items():
        try:
            with open(script_name, 'w', encoding='utf-8') as f:
                f.write(script_content)
            print(f"   ✅ Created {script_name}")
        except Exception as e:
            print(f"   ⚠️  Failed to create {script_name}: {e}")

def main():
    """主函数"""
    print("🔧 SeedVision v1 - Scripts Directory Reorganization")
    print("=" * 50)
    
    # 重新组织scripts目录
    success = reorganize_scripts_directory()
    
    if success:
        # 更新文档
        update_documentation()
        
        # 测试冲突
        test_import_conflicts()
        
        # 创建便捷脚本
        create_convenience_scripts()
        
        print("\n🎉 Scripts directory reorganization completed!")
        print("📋 New usage:")
        print("   python train.py                    # 基础训练")
        print("   python train_scheduler.py          # 调度器训练")
        print("   python run_tests.py                # 运行测试")
        print("   python runners/utilities/cleanup_logs.py  # 日志清理")
        
    else:
        print("\n❌ Scripts directory reorganization failed!")

if __name__ == "__main__":
    main()
