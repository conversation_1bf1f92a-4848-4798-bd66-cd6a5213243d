'''
数据库相关操作，在这里配置，有些数据保存到数据库更方便
'''

import pymysql

def test_connection():
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='127.0.0.1',  # 数据库主机地址
            user='root',       # 数据库用户名
            password='911711', # 数据库密码
            charset='utf8mb4', # 字符集
            cursorclass=pymysql.cursors.DictCursor # 返回字典格式
        )

        # show 
        with connection.cursor() as cursor:
            # 执行查询
            cursor.execute("SHOW DATABASES;")
            # 获取查询结果
            result = cursor.fetchall()
            print(result)

        # 关闭连接
        connection.close()
    except pymysql.Error as e:
        print(f"Error: {e}")

class DBConnection:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DBConnection, cls).__new__(cls)
            cls._instance._create_connection()
        return cls._instance

    def _create_connection(self):
        try:
            self.connection = pymysql.connect(
                host='127.0.0.1',
                user='root',
                password='911711',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
        except pymysql.Error as e:
            print(f"数据库连接失败: {e}")
            raise

# 创建单例连接实例
mysql_connecter = DBConnection().connection