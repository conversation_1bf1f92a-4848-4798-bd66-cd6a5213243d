'''
数据库相关操作，在这里配置，有些数据保存到数据库更方便
支持 MySQL 和 MongoDB 连接
'''

import pymysql
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

def test_connection():
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='127.0.0.1',  # 数据库主机地址
            user='root',       # 数据库用户名
            password='911711', # 数据库密码
            charset='utf8mb4', # 字符集
            cursorclass=pymysql.cursors.DictCursor # 返回字典格式
        )

        # show
        with connection.cursor() as cursor:
            # 执行查询
            cursor.execute("SHOW DATABASES;")
            # 获取查询结果
            result = cursor.fetchall()
            print(result)

        # 关闭连接
        connection.close()
    except pymysql.Error as e:
        print(f"Error: {e}")

class DBConnection:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DBConnection, cls).__new__(cls)
            cls._instance._create_connection()
        return cls._instance

    def _create_connection(self):
        try:
            self.connection = pymysql.connect(
                host='127.0.0.1',
                user='root',
                password='911711',
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
        except pymysql.Error as e:
            print(f"数据库连接失败: {e}")
            raise

class MongoDBConnection:
    """MongoDB 连接器单例类"""
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MongoDBConnection, cls).__new__(cls)
            cls._instance._create_connection()
        return cls._instance

    def _create_connection(self):
        """创建 MongoDB 连接"""
        try:
            # MongoDB 连接配置
            host = '127.0.0.1'
            port = 27017
            username = 'zhifu'
            password = '911711'

            # 构建连接字符串
            connection_string = f"mongodb://{username}:{password}@{host}:{port}/"

            # 创建 MongoDB 客户端
            self.client = MongoClient(
                connection_string,
                serverSelectionTimeoutMS=5000,  # 5秒超时
                connectTimeoutMS=5000,
                socketTimeoutMS=5000
            )

            # 测试连接
            self.client.admin.command('ping')
            print(f"MongoDB 连接成功: {host}:{port}")

        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            print(f"MongoDB 连接失败: {e}")
            self.client = None
            raise
        except Exception as e:
            print(f"MongoDB 连接异常: {e}")
            self.client = None
            raise

    def get_database(self, db_name):
        """获取指定数据库"""
        if self.client is None:
            raise Exception("MongoDB 连接未建立")
        return self.client[db_name]

    def list_databases(self):
        """列出所有数据库"""
        if self.client is None:
            raise Exception("MongoDB 连接未建立")
        return self.client.list_database_names()

    def close(self):
        """关闭连接"""
        if self.client:
            self.client.close()
            print("MongoDB 连接已关闭")

def test_mongodb_connection():
    """测试 MongoDB 连接"""
    try:
        # 创建 MongoDB 连接
        mongo_conn = MongoDBConnection()

        # 列出数据库
        databases = mongo_conn.list_databases()
        print(f"可用数据库: {databases}")

        # 测试获取数据库
        test_db = mongo_conn.get_database('test')
        print(f"获取测试数据库: {test_db.name}")

        return True

    except Exception as e:
        print(f"MongoDB 连接测试失败: {e}")
        return False

# 创建单例连接实例
mysql_connecter = DBConnection().connection

# 创建 MongoDB 连接器实例 (延迟初始化)
def get_mongodb_connecter():
    """获取 MongoDB 连接器实例"""
    try:
        return MongoDBConnection()
    except Exception as e:
        print(f"无法创建 MongoDB 连接: {e}")
        return None

# 导出 MongoDB 连接器
mongodb_connecter = get_mongodb_connecter