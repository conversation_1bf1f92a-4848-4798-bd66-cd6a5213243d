# 批量训练指南

## 🚀 快速开始

您现在可以使用批量训练系统来自动训练所有用户配置！

### 📋 当前可用配置

- **总配置数**: 32个 (完整的4×2×4组合)
- **224×224配置**: 8个 (带/不带归一化 × 4个学习率)
- **112×112配置**: 8个 (带/不带归一化 × 4个学习率)
- **80×80配置**: 8个 (带/不带归一化 × 4个学习率)
- **56×56配置**: 8个 (带/不带归一化 × 4个学习率)
- **预估总时间**: 约86小时 (3.6天)

## 🛠️ 使用方法

### 1. 预览训练计划

```bash
cd models/SeedVision_v1

# 查看所有配置的训练计划
python scripts/batch_train.py --all --dry-run

# 查看特定配置的训练计划
python scripts/batch_train.py --filter 224x224 --dry-run
python scripts/batch_train.py --filter norm --dry-run
python scripts/batch_train.py --filter high_lr --dry-run
```

### 2. 开始批量训练

#### 训练所有配置 (推荐)
```bash
python scripts/batch_train.py --all
```

#### 训练特定类型的配置
```bash
# 只训练224x224配置 (8个配置，约34小时)
python scripts/batch_train.py --filter 224x224

# 只训练带归一化的配置
python scripts/batch_train.py --filter norm

# 只训练高学习率配置 (快速验证)
python scripts/batch_train.py --filter high_lr

# 只训练112x112配置 (更快)
python scripts/batch_train.py --filter 112x112
```

#### 从特定位置开始
```bash
# 从第5个配置开始训练
python scripts/batch_train.py --all --start-from 5

# 出错时继续执行
python scripts/batch_train.py --all --continue-on-error
```

## 📊 训练执行顺序

系统会智能排序，**快速配置优先执行**：

1. **56×56配置** (1.2-1.4小时/个) - 最快
   - `user_56x56_norm_high_lr` (1.2小时) - 最快配置
   - `user_56x56_no_norm_high_lr` (1.2小时)
   - `user_56x56_norm_mid_lr` (1.2小时)
   - `user_56x56_no_norm_mid_lr` (1.2小时)
   - 低学习率配置 (1.3小时)
   - 极低学习率配置 (1.4小时)

2. **80×80配置** (2.0-2.4小时/个)
   - 高学习率配置 (2.0小时)
   - 中学习率配置 (2.0小时)
   - 低学习率配置 (2.2小时)
   - 极低学习率配置 (2.4小时)

3. **112×112配置** (2.8-3.4小时/个)
   - 高学习率配置 (2.8小时)
   - 中学习率配置 (2.8小时)
   - 低学习率配置 (3.1小时)
   - 极低学习率配置 (3.4小时)

4. **224×224配置** (4.0-4.8小时/个) - 最慢
   - 高学习率配置 (4.0小时)
   - 中学习率配置 (4.0小时)
   - 低学习率配置 (4.4小时)
   - 极低学习率配置 (4.8小时)

## 📁 结果保存

每个训练完成后，结果自动保存到：

```
output/training/results/{配置名}/
├── best_model.pth                    # 最佳模型
├── final_model.pth                   # 最终模型
├── training_params/                  # 训练参数 (自动保存)
│   ├── training_params_YYYYMMDD_HHMMSS.json
│   └── training_params_YYYYMMDD_HHMMSS.yaml
├── visualizations/                   # 可视化结果
│   ├── loss_curve_YYYYMMDD_HHMMSS.png
│   ├── oil_r2_curve_YYYYMMDD_HHMMSS.png
│   ├── protein_r2_curve_YYYYMMDD_HHMMSS.png
│   ├── oil_regression_YYYYMMDD_HHMMSS.png
│   └── protein_regression_YYYYMMDD_HHMMSS.png
└── logs/                            # 训练日志
    └── training_YYYYMMDD_HHMMSS.log
```

## 📈 批量训练日志

批量训练过程会自动记录到：

```
logs/batch_training/
├── batch_log_YYYYMMDD_HHMMSS.json      # 实时日志
└── final_batch_log_YYYYMMDD_HHMMSS.json # 最终统计
```

日志包含：
- 每个配置的训练结果
- 成功/失败统计
- 实际训练时间
- 错误信息 (如有)

## ⚡ 推荐的训练策略

### 策略1: 快速验证 (推荐新手)
```bash
# 先训练最快的56x56配置验证系统 (约10小时)
python scripts/batch_train.py --filter 56x56 --dry-run  # 预览
python scripts/batch_train.py --filter 56x56            # 执行
```

### 策略2: 分批训练 (推荐)
```bash
# 第一批：56x56配置 (约10小时) - 最快验证
python scripts/batch_train.py --filter 56x56

# 第二批：80x80配置 (约17小时) - 中等速度
python scripts/batch_train.py --filter 80x80

# 第三批：112x112配置 (约25小时) - 较慢
python scripts/batch_train.py --filter 112x112

# 第四批：224x224配置 (约34小时) - 最慢但最高质量
python scripts/batch_train.py --filter 224x224
```

### 策略3: 按学习率分批 (适合对比实验)
```bash
# 高学习率配置 (约20小时) - 快速收敛
python scripts/batch_train.py --filter high_lr

# 中学习率配置 (约20小时) - 平衡性能
python scripts/batch_train.py --filter mid_lr

# 低学习率配置 (约23小时) - 稳定训练
python scripts/batch_train.py --filter low_lr

# 极低学习率配置 (约25小时) - 精细调优
python scripts/batch_train.py --filter very_low_lr
```

### 策略4: 一次性训练 (适合长时间运行)
```bash
# 训练所有32个配置 (约86小时 = 3.6天)
python scripts/batch_train.py --all --continue-on-error
```

## 🔧 高级选项

### 错误处理
```bash
# 出错时继续执行下一个配置
python scripts/batch_train.py --all --continue-on-error

# 从失败的位置重新开始
python scripts/batch_train.py --all --start-from 8
```

### 监控进度
训练过程中，您可以：
1. 查看终端输出了解当前进度
2. 检查 `logs/batch_training/` 目录的实时日志
3. 查看 `output/training/results/` 目录的训练结果

## 🎯 预期结果

完成所有训练后，您将获得：

- **32个完整训练的模型** (4尺寸 × 2归一化 × 4学习率)
- **完整的性能对比数据** (涵盖所有参数组合)
- **不同配置的最佳实践** (从快速原型到高质量模型)
- **详细的训练参数记录** (每个配置的完整参数)

这将帮助您：
1. 找到最佳的模型配置
2. 理解不同参数对性能的影响
3. 为未来的训练选择最优设置

## 🚀 立即开始

如果您准备好了，可以立即开始：

```bash
cd models/SeedVision_v1

# 快速验证 (推荐第一次使用) - 最快的8个配置，约10小时
python scripts/batch_train.py --filter 56x56

# 或者按尺寸分批训练 (推荐)
python scripts/batch_train.py --filter 56x56    # 第一批：8个配置，约10小时
python scripts/batch_train.py --filter 80x80    # 第二批：8个配置，约17小时
python scripts/batch_train.py --filter 112x112  # 第三批：8个配置，约25小时
python scripts/batch_train.py --filter 224x224  # 第四批：8个配置，约34小时

# 或者直接开始全部32个配置的训练 (约86小时 = 3.6天)
python scripts/batch_train.py --all --continue-on-error
```

训练将自动进行，您可以离开让系统自动完成所有32个配置的训练！
