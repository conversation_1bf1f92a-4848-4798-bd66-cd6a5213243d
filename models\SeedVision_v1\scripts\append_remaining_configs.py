#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
追加剩余配置脚本

将80x80和56x56的配置追加到主配置文件中
"""

import os

def append_configs():
    """追加剩余的配置"""

    # 80x80和56x56配置
    configs_to_add = """
  # --------------------------------------------------------------------------------
  # 80x80 配置组合
  # --------------------------------------------------------------------------------
  user_80x80_norm_high_lr:
    enable: false
    name: "user_80x80_norm_high_lr"
    description: "用户配置 - 80x80带归一化，学习率0.01，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 96
      depths: [2, 3, 8, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 2
      patch_stride: 2

    resources:
      estimated_memory: 0.6
      batch_size: 64

    transform_config: "80x80_norm"
    hyperparameter_config: "user_high_lr"

  user_80x80_norm_mid_lr:
    enable: false
    name: "user_80x80_norm_mid_lr"
    description: "用户配置 - 80x80带归一化，学习率0.001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 96
      depths: [2, 3, 8, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 2
      patch_stride: 2

    resources:
      estimated_memory: 0.6
      batch_size: 64

    transform_config: "80x80_norm"
    hyperparameter_config: "user_mid_lr"

  user_80x80_norm_low_lr:
    enable: false
    name: "user_80x80_norm_low_lr"
    description: "用户配置 - 80x80带归一化，学习率0.0001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 96
      depths: [2, 3, 8, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 2
      patch_stride: 2

    resources:
      estimated_memory: 0.6
      batch_size: 64

    transform_config: "80x80_norm"
    hyperparameter_config: "user_low_lr"

  user_80x80_norm_very_low_lr:
    enable: false
    name: "user_80x80_norm_very_low_lr"
    description: "用户配置 - 80x80带归一化，学习率0.00001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 96
      depths: [2, 3, 8, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 2
      patch_stride: 2

    resources:
      estimated_memory: 0.6
      batch_size: 64

    transform_config: "80x80_norm"
    hyperparameter_config: "user_very_low_lr"

  user_80x80_no_norm_high_lr:
    enable: false
    name: "user_80x80_no_norm_high_lr"
    description: "用户配置 - 80x80不带归一化，学习率0.01，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 96
      depths: [2, 3, 8, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 2
      patch_stride: 2

    resources:
      estimated_memory: 0.6
      batch_size: 64

    transform_config: "80x80_no_norm"
    hyperparameter_config: "user_high_lr"

  user_80x80_no_norm_mid_lr:
    enable: false
    name: "user_80x80_no_norm_mid_lr"
    description: "用户配置 - 80x80不带归一化，学习率0.001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 96
      depths: [2, 3, 8, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 2
      patch_stride: 2

    resources:
      estimated_memory: 0.6
      batch_size: 64

    transform_config: "80x80_no_norm"
    hyperparameter_config: "user_mid_lr"

  user_80x80_no_norm_low_lr:
    enable: false
    name: "user_80x80_no_norm_low_lr"
    description: "用户配置 - 80x80不带归一化，学习率0.0001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 96
      depths: [2, 3, 8, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 2
      patch_stride: 2

    resources:
      estimated_memory: 0.6
      batch_size: 64

    transform_config: "80x80_no_norm"
    hyperparameter_config: "user_low_lr"

  user_80x80_no_norm_very_low_lr:
    enable: false
    name: "user_80x80_no_norm_very_low_lr"
    description: "用户配置 - 80x80不带归一化，学习率0.00001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 96
      depths: [2, 3, 8, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 2
      patch_stride: 2

    resources:
      estimated_memory: 0.6
      batch_size: 64

    transform_config: "80x80_no_norm"
    hyperparameter_config: "user_very_low_lr"

  # --------------------------------------------------------------------------------
  # 56x56 配置组合
  # --------------------------------------------------------------------------------
  user_56x56_norm_high_lr:
    enable: false
    name: "user_56x56_norm_high_lr"
    description: "用户配置 - 56x56带归一化，学习率0.01，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 64
      depths: [2, 2, 6, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 1
      patch_stride: 1

    resources:
      estimated_memory: 0.4
      batch_size: 80

    transform_config: "56x56_norm"
    hyperparameter_config: "user_high_lr"

  user_56x56_norm_mid_lr:
    enable: false
    name: "user_56x56_norm_mid_lr"
    description: "用户配置 - 56x56带归一化，学习率0.001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 64
      depths: [2, 2, 6, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 1
      patch_stride: 1

    resources:
      estimated_memory: 0.4
      batch_size: 80

    transform_config: "56x56_norm"
    hyperparameter_config: "user_mid_lr"

  user_56x56_norm_low_lr:
    enable: false
    name: "user_56x56_norm_low_lr"
    description: "用户配置 - 56x56带归一化，学习率0.0001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 64
      depths: [2, 2, 6, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 1
      patch_stride: 1

    resources:
      estimated_memory: 0.4
      batch_size: 80

    transform_config: "56x56_norm"
    hyperparameter_config: "user_low_lr"

  user_56x56_norm_very_low_lr:
    enable: false
    name: "user_56x56_norm_very_low_lr"
    description: "用户配置 - 56x56带归一化，学习率0.00001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 64
      depths: [2, 2, 6, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 1
      patch_stride: 1

    resources:
      estimated_memory: 0.4
      batch_size: 80

    transform_config: "56x56_norm"
    hyperparameter_config: "user_very_low_lr"

  user_56x56_no_norm_high_lr:
    enable: false
    name: "user_56x56_no_norm_high_lr"
    description: "用户配置 - 56x56不带归一化，学习率0.01，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 64
      depths: [2, 2, 6, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 1
      patch_stride: 1

    resources:
      estimated_memory: 0.4
      batch_size: 80

    transform_config: "56x56_no_norm"
    hyperparameter_config: "user_high_lr"

  user_56x56_no_norm_mid_lr:
    enable: false
    name: "user_56x56_no_norm_mid_lr"
    description: "用户配置 - 56x56不带归一化，学习率0.001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 64
      depths: [2, 2, 6, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 1
      patch_stride: 1

    resources:
      estimated_memory: 0.4
      batch_size: 80

    transform_config: "56x56_no_norm"
    hyperparameter_config: "user_mid_lr"

  user_56x56_no_norm_low_lr:
    enable: false
    name: "user_56x56_no_norm_low_lr"
    description: "用户配置 - 56x56不带归一化，学习率0.0001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 64
      depths: [2, 2, 6, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 1
      patch_stride: 1

    resources:
      estimated_memory: 0.4
      batch_size: 80

    transform_config: "56x56_no_norm"
    hyperparameter_config: "user_low_lr"

  user_56x56_no_norm_very_low_lr:
    enable: false
    name: "user_56x56_no_norm_very_low_lr"
    description: "用户配置 - 56x56不带归一化，学习率0.00001，20原图×60样本"

    training:
      num_epochs: 100
      save_params: true

    dataset_config:
      sampling_strategy: "original_level_sampling"
      strategy_parameters:
        target_originals: 20
        samples_per_original: 60
        total_samples: 1200
        cross_subset_sampling: true
        train_ratio: 0.8
        val_ratio: 0.1
        test_ratio: 0.1

    model:
      embed_dim: 64
      depths: [2, 2, 6, 2]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.2
      layer_scale_init_value: 0
      patch_size: 1
      patch_stride: 1

    resources:
      estimated_memory: 0.4
      batch_size: 80

    transform_config: "56x56_no_norm"
    hyperparameter_config: "user_very_low_lr"
"""

    # 追加到配置文件
    config_file = "config/training_config.yaml"

    with open(config_file, 'a', encoding='utf-8') as f:
        f.write(configs_to_add)

    print("已追加80x80和56x56配置到主配置文件")

if __name__ == "__main__":
    append_configs()
