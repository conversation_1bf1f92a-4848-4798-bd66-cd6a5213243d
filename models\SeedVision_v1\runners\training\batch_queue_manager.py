#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量训练队列管理器

统一管理32个用户配置的批量训练任务队列，集成智能调度功能。
不需要命令行操作，通过main.py统一配置和启动。

功能：
1. 自动加载所有用户配置
2. 智能排序和调度
3. 批量训练执行
4. 进度监控和日志记录
5. 错误处理和恢复

使用方法：
从main.py调用，不直接执行此脚本
"""

import sys
import os
import yaml
import time
import subprocess
from datetime import datetime
import json

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

class BatchQueueManager:
    """批量训练队列管理器"""
    
    def __init__(self):
        self.config = None
        self.user_configs = []
        self.current_task = 0
        self.total_tasks = 0
        self.successful_count = 0
        self.failed_count = 0
        self.results = []
        self.batch_start_time = None
        
    def load_config(self):
        """加载训练配置文件"""
        config_path = "config/training_config.yaml"
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            return True
        except Exception as e:
            print(f"{Colors.RED}[ERROR]{Colors.END} 无法加载配置文件: {e}")
            return False

    def save_config(self):
        """保存训练配置文件"""
        config_path = "config/training_config.yaml"
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True, indent=2)
            return True
        except Exception as e:
            print(f"{Colors.RED}[ERROR]{Colors.END} 无法保存配置文件: {e}")
            return False

    def get_user_configs(self, filter_text=None):
        """获取用户配置列表"""
        if not self.config or 'training_configs' not in self.config:
            return []
        
        training_configs = self.config['training_configs']
        user_configs = []
        
        for name, cfg in training_configs.items():
            if name.startswith('user_'):
                if not filter_text or filter_text.lower() in name.lower():
                    user_configs.append((name, cfg))
        
        return user_configs

    def estimate_training_time(self, config_name):
        """估算训练时间"""
        base_time = 4.0  # 基础时间 (小时)
        
        # 根据输入尺寸调整
        if '56x56' in config_name:
            time_multiplier = 0.3
        elif '80x80' in config_name:
            time_multiplier = 0.5
        elif '112x112' in config_name:
            time_multiplier = 0.7
        elif '224x224' in config_name:
            time_multiplier = 1.0
        else:
            time_multiplier = 1.0
        
        # 根据学习率调整
        if 'very_low_lr' in config_name:
            lr_multiplier = 1.2
        elif 'low_lr' in config_name:
            lr_multiplier = 1.1
        else:
            lr_multiplier = 1.0
        
        return base_time * time_multiplier * lr_multiplier

    def sort_configs_by_priority(self, user_configs):
        """智能排序配置 - 快速配置优先"""
        def sort_key(config_item):
            name, cfg = config_item
            estimated_time = self.estimate_training_time(name)
            
            # 尺寸优先级权重 (小尺寸优先)
            if '56x56' in name:
                size_weight = 1000
            elif '80x80' in name:
                size_weight = 800
            elif '112x112' in name:
                size_weight = 600
            elif '224x224' in name:
                size_weight = 400
            else:
                size_weight = 200
            
            # 学习率权重 (高学习率优先，收敛更快)
            if 'high_lr' in name:
                lr_weight = 100
            elif 'mid_lr' in name:
                lr_weight = 80
            elif 'low_lr' in name:
                lr_weight = 60
            else:
                lr_weight = 40
            
            return -(size_weight + lr_weight - estimated_time * 10)
        
        return sorted(user_configs, key=sort_key)

    def enable_config(self, config_name):
        """启用指定配置"""
        if not self.config or 'training_configs' not in self.config:
            return False
            
        training_configs = self.config['training_configs']
        
        # 禁用所有配置
        for name, cfg in training_configs.items():
            cfg['enable'] = False
        
        # 启用指定配置
        if config_name in training_configs:
            training_configs[config_name]['enable'] = True
            return True
        
        return False

    def run_single_training(self, config_name):
        """运行单个训练任务"""
        print(f"\n{Colors.BLUE}[TRAINING]{Colors.END} 开始训练: {config_name}")
        print("=" * 80)
        
        start_time = datetime.now()
        
        try:
            # 运行训练脚本
            result = subprocess.run(
                [sys.executable, "runners/training/main.py"],
                capture_output=True,
                text=True,
                timeout=28800,  # 8小时超时
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds() / 3600
            
            if result.returncode == 0:
                print(f"{Colors.GREEN}[SUCCESS]{Colors.END} 训练完成: {config_name}")
                print(f"耗时: {duration:.1f} 小时")
                return True, duration, None
            else:
                error_msg = result.stderr if result.stderr else "未知错误"
                print(f"{Colors.RED}[FAILED]{Colors.END} 训练失败: {config_name}")
                print(f"错误: {error_msg[:200]}...")
                return False, duration, error_msg
                
        except subprocess.TimeoutExpired:
            print(f"{Colors.YELLOW}[TIMEOUT]{Colors.END} 训练超时: {config_name}")
            return False, 8.0, "训练超时"
        except Exception as e:
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds() / 3600
            print(f"{Colors.RED}[ERROR]{Colors.END} 执行错误: {e}")
            return False, duration, str(e)

    def save_batch_log(self, log_data, log_file=None):
        """保存批量训练日志"""
        if log_file is None:
            timestamp = self.batch_start_time.strftime("%Y%m%d_%H%M%S")
            log_file = f"batch_training_log_{timestamp}.json"
            
        log_dir = "logs/batch_training"
        os.makedirs(log_dir, exist_ok=True)
        
        log_path = os.path.join(log_dir, log_file)
        
        try:
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, indent=2, ensure_ascii=False, default=str)
            return log_path
        except Exception as e:
            print(f"{Colors.YELLOW}[WARNING]{Colors.END} 无法保存日志: {e}")
            return None

    def print_training_plan(self, user_configs):
        """打印训练计划"""
        print(f"\n{Colors.BOLD}[TRAINING PLAN]{Colors.END} 批量训练计划")
        print("=" * 100)
        
        total_time = sum(self.estimate_training_time(name) for name, _ in user_configs)
        
        print(f"{Colors.CYAN}[SUMMARY]{Colors.END}")
        print(f"  总配置数: {len(user_configs)}")
        print(f"  预估总时间: {total_time:.1f} 小时 ({total_time/24:.1f} 天)")
        print(f"  平均每配置: {total_time/len(user_configs):.1f} 小时")
        print()
        
        print(f"{Colors.YELLOW}[EXECUTION ORDER]{Colors.END}")
        print(f"{'序号':<4} {'配置名':<35} {'预估时间':<10} {'描述':<40}")
        print("-" * 100)
        
        cumulative_time = 0
        for i, (name, cfg) in enumerate(user_configs, 1):
            estimated_time = self.estimate_training_time(name)
            cumulative_time += estimated_time
            
            description = cfg.get('description', '')[:38] + '...' if len(cfg.get('description', '')) > 40 else cfg.get('description', '')
            
            print(f"{i:<4} {name:<35} {estimated_time:<10.1f} {description:<40}")
        
        print("-" * 100)
        print(f"预估完成时间: {cumulative_time:.1f} 小时")

    def execute_batch_training(self, filter_text=None, continue_on_error=True, dry_run=False):
        """执行批量训练"""
        print(f"{Colors.BOLD}[BATCH QUEUE MANAGER]{Colors.END} 批量训练队列系统")
        print("=" * 80)
        
        # 加载配置
        print(f"{Colors.BLUE}[LOADING]{Colors.END} 加载配置...")
        if not self.load_config():
            return False
        
        # 获取用户配置
        self.user_configs = self.get_user_configs(filter_text)
        if not self.user_configs:
            print(f"{Colors.YELLOW}[WARNING]{Colors.END} 没有找到匹配的用户配置")
            return False
        
        print(f"找到 {len(self.user_configs)} 个用户配置")
        
        # 智能排序
        self.user_configs = self.sort_configs_by_priority(self.user_configs)
        self.total_tasks = len(self.user_configs)
        
        # 显示计划
        self.print_training_plan(self.user_configs)
        
        if dry_run:
            print(f"\n{Colors.YELLOW}[DRY RUN]{Colors.END} 预览模式，未实际执行训练")
            return True
        
        # 开始批量训练
        print(f"\n{Colors.GREEN}[STARTING]{Colors.END} 开始批量训练...")
        
        self.batch_start_time = datetime.now()
        self.results = []
        self.successful_count = 0
        self.failed_count = 0
        
        for i, (config_name, cfg) in enumerate(self.user_configs, 1):
            self.current_task = i
            
            print(f"\n{Colors.BOLD}[PROGRESS]{Colors.END} 进度: {i}/{self.total_tasks}")
            print(f"当前配置: {config_name}")
            
            # 启用配置
            if not self.enable_config(config_name):
                print(f"{Colors.RED}[ERROR]{Colors.END} 无法启用配置: {config_name}")
                continue
            
            if not self.save_config():
                print(f"{Colors.RED}[ERROR]{Colors.END} 无法保存配置文件")
                continue
            
            # 运行训练
            success, duration, error_msg = self.run_single_training(config_name)
            
            # 记录结果
            result = {
                'config_name': config_name,
                'success': success,
                'duration_hours': duration,
                'error_message': error_msg,
                'timestamp': datetime.now().isoformat()
            }
            self.results.append(result)
            
            if success:
                self.successful_count += 1
            else:
                self.failed_count += 1
                if not continue_on_error:
                    print(f"{Colors.RED}[STOPPING]{Colors.END} 训练失败，停止执行")
                    break
            
            # 保存中间结果
            self._save_intermediate_log()
        
        # 最终统计
        self._print_final_summary()
        self._save_final_log()
        
        return True

    def _save_intermediate_log(self):
        """保存中间日志"""
        log_data = {
            'batch_start_time': self.batch_start_time.isoformat(),
            'total_configs': self.total_tasks,
            'completed': self.current_task,
            'successful': self.successful_count,
            'failed': self.failed_count,
            'results': self.results
        }
        self.save_batch_log(log_data)

    def _save_final_log(self):
        """保存最终日志"""
        batch_end_time = datetime.now()
        total_duration = (batch_end_time - self.batch_start_time).total_seconds() / 3600
        
        final_log_data = {
            'batch_start_time': self.batch_start_time.isoformat(),
            'batch_end_time': batch_end_time.isoformat(),
            'total_duration_hours': total_duration,
            'total_configs': self.total_tasks,
            'successful': self.successful_count,
            'failed': self.failed_count,
            'results': self.results
        }
        
        timestamp = self.batch_start_time.strftime("%Y%m%d_%H%M%S")
        log_path = self.save_batch_log(final_log_data, f"final_batch_log_{timestamp}.json")
        if log_path:
            print(f"\n详细日志已保存到: {log_path}")

    def _print_final_summary(self):
        """打印最终统计"""
        batch_end_time = datetime.now()
        total_duration = (batch_end_time - self.batch_start_time).total_seconds() / 3600
        
        print(f"\n{Colors.BOLD}[COMPLETE]{Colors.END} 批量训练完成")
        print("=" * 80)
        print(f"总配置数: {self.total_tasks}")
        print(f"成功: {Colors.GREEN}{self.successful_count}{Colors.END}")
        print(f"失败: {Colors.RED}{self.failed_count}{Colors.END}")
        print(f"总耗时: {total_duration:.1f} 小时")
        if self.results:
            print(f"平均耗时: {total_duration/len(self.results):.1f} 小时/配置")

# 全局实例
batch_queue_manager = BatchQueueManager()
