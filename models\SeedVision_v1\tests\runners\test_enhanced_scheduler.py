#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版调度器测试脚本

测试功能：
1. 多任务提交
2. 自动测试 (1 epoch)
3. 动态任务过滤
4. 日志记录
5. 状态监控
"""

import sys
import os
import time
import logging
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def setup_test_logging():
    """设置测试日志"""
    log_dir = "logs/test"
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"enhanced_scheduler_test_{timestamp}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

def create_test_configs():
    """创建测试配置"""
    base_config = {
        'model': {
            'embed_dim': 64,
            'depths': [2, 2, 8, 2],
            'mlp_ratio': 2.0,
            'n_div': 2,
            'drop_path_rate': 0.1
        },
        'resources': {
            'batch_size': 10
        },
        'transform_config': '224x224_norm',
        'training': {
            'num_epochs': 5,
            'learning_rate': 0.001
        },
        'sampling_strategy_config': {
            'strategy_type': 'random',
            'parameters': {
                'sample_size': 200,
                'seed': 42
            }
        }
    }
    
    # 创建多个不同的配置
    configs = []
    
    # 配置1: 正常配置 (应该通过测试)
    config1 = base_config.copy()
    config1['name'] = 'normal_config_64dim'
    configs.append(config1)
    
    # 配置2: 更大的模型 (可能通过测试)
    config2 = base_config.copy()
    config2['name'] = 'large_config_128dim'
    config2['model'] = config2['model'].copy()
    config2['model']['embed_dim'] = 128
    config2['model']['depths'] = [3, 4, 12, 3]
    configs.append(config2)
    
    # 配置3: 小模型 (应该通过测试)
    config3 = base_config.copy()
    config3['name'] = 'small_config_32dim'
    config3['model'] = config3['model'].copy()
    config3['model']['embed_dim'] = 32
    config3['model']['depths'] = [1, 1, 4, 1]
    configs.append(config3)
    
    # 配置4: 可能有问题的配置 (可能失败)
    config4 = base_config.copy()
    config4['name'] = 'problematic_config'
    config4['model'] = config4['model'].copy()
    config4['model']['embed_dim'] = 256  # 很大的模型
    config4['model']['depths'] = [4, 6, 24, 4]
    config4['resources']['batch_size'] = 50  # 大批次
    configs.append(config4)
    
    return configs

def test_enhanced_scheduler():
    """测试增强版调度器"""
    logger = setup_test_logging()
    logger.info("Starting Enhanced Scheduler Test")
    
    try:
        from scheduler.enhanced_scheduler import EnhancedSchedulerManager
        from scheduler.task_scheduler import TaskPriority
        
        # 创建调度器管理器
        scheduler = EnhancedSchedulerManager(
            max_gpu_memory=8.0,
            max_concurrent_tasks=2
        )
        
        logger.info("Enhanced Scheduler Manager created")
        
        # 创建测试配置
        configs = create_test_configs()
        logger.info(f"Created {len(configs)} test configurations")
        
        # 提交任务
        task_ids = scheduler.submit_tasks(configs, TaskPriority.HIGH)
        logger.info(f"Submitted {len(task_ids)} tasks: {task_ids}")
        
        # 启动调度
        scheduler.start_scheduling(enable_monitoring=True)
        logger.info("Scheduler started")
        
        # 监控状态
        logger.info("Starting status monitoring...")
        
        # 运行测试循环
        test_duration = 300  # 5分钟测试
        start_time = time.time()
        
        while time.time() - start_time < test_duration:
            try:
                # 获取状态
                status = scheduler.get_comprehensive_status()
                
                # 记录状态
                queue = status['queue_status']
                test_summary = status['test_summary']
                
                logger.info(f"Queue Status: "
                          f"Pending={queue['pending_count']}, "
                          f"Testing={queue['testing_count']}, "
                          f"Running={queue['running_count']}, "
                          f"Completed={queue['completed_count']}, "
                          f"Failed={queue['failed_count']}, "
                          f"TestFailed={queue['test_failed_count']}")
                
                logger.info(f"Test Summary: "
                          f"Tested={test_summary['total_tested']}, "
                          f"Passed={test_summary['passed_tests']}, "
                          f"Failed={test_summary['failed_tests']}, "
                          f"SuccessRate={test_summary['test_success_rate']:.1f}%")
                
                # 检查任务详情
                for task_id in task_ids:
                    task_details = scheduler.get_task_details(task_id)
                    if task_details:
                        logger.info(f"Task {task_id}: Status={task_details['status']}, "
                                  f"TestPassed={task_details['test_info']['test_passed']}")
                
                # 检查是否所有任务都完成
                all_finished = all(
                    scheduler.get_task_details(task_id)['status'] in 
                    ['completed', 'failed', 'test_failed'] 
                    for task_id in task_ids
                )
                
                if all_finished:
                    logger.info("All tasks finished, ending test early")
                    break
                
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(10)
        
        # 停止调度器
        scheduler.stop_scheduling()
        logger.info("Scheduler stopped")
        
        # 生成最终报告
        final_report = scheduler.generate_final_report()
        
        # 保存报告
        report_file = f"logs/test/enhanced_scheduler_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Final report saved to: {report_file}")
        
        # 显示测试结果
        summary = final_report['summary']
        logger.info("=" * 60)
        logger.info("Enhanced Scheduler Test Results")
        logger.info("=" * 60)
        logger.info(f"Total Tasks: {summary['total_tasks']}")
        logger.info(f"Successful Tasks: {summary['successful_tasks']}")
        logger.info(f"Failed Tasks: {summary['failed_tasks']}")
        logger.info(f"Test Success Rate: {summary['test_success_rate']:.1f}%")
        
        # 详细任务结果
        logger.info("\nDetailed Task Results:")
        for task_id in task_ids:
            task_details = scheduler.get_task_details(task_id)
            if task_details:
                logger.info(f"  {task_id} ({task_details['name']}): "
                          f"Status={task_details['status']}, "
                          f"TestPassed={task_details['test_info']['test_passed']}")
        
        logger.info("Enhanced Scheduler Test Completed Successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Enhanced Scheduler Test Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_basic_functionality():
    """测试基础功能"""
    logger = setup_test_logging()
    logger.info("Testing basic enhanced scheduler functionality")
    
    try:
        from scheduler.enhanced_scheduler import EnhancedSchedulerManager
        from scheduler.task_scheduler import TaskPriority
        
        # 创建调度器
        scheduler = EnhancedSchedulerManager(max_gpu_memory=8.0, max_concurrent_tasks=1)
        
        # 创建简单配置
        simple_config = {
            'name': 'basic_test',
            'model': {
                'embed_dim': 32,
                'depths': [1, 1, 2, 1],
                'mlp_ratio': 2.0,
                'n_div': 2,
                'drop_path_rate': 0.1
            },
            'resources': {'batch_size': 5},
            'transform_config': '224x224_norm',
            'training': {'num_epochs': 1, 'learning_rate': 0.001},
            'sampling_strategy_config': {
                'strategy_type': 'random',
                'parameters': {'sample_size': 50, 'seed': 42}
            }
        }
        
        # 提交单个任务
        task_ids = scheduler.submit_tasks([simple_config], TaskPriority.NORMAL)
        logger.info(f"Submitted basic test task: {task_ids[0]}")
        
        # 获取状态
        status = scheduler.get_comprehensive_status()
        logger.info(f"Initial status: {status['queue_status']}")
        
        logger.info("Basic functionality test completed")
        return True
        
    except Exception as e:
        logger.error(f"Basic functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("Enhanced Scheduler Test Suite")
    print("=" * 50)
    
    # 测试基础功能
    print("1. Testing basic functionality...")
    if test_basic_functionality():
        print("   ✅ Basic functionality test passed")
    else:
        print("   ❌ Basic functionality test failed")
        return False
    
    # 询问是否运行完整测试
    print("\n2. Full enhanced scheduler test (may take 5+ minutes)")
    response = input("   Run full test? (y/N): ").strip().lower()
    
    if response == 'y':
        print("   Starting full test...")
        if test_enhanced_scheduler():
            print("   ✅ Full enhanced scheduler test passed")
        else:
            print("   ❌ Full enhanced scheduler test failed")
            return False
    else:
        print("   Skipped full test")
    
    print("\n🎉 Enhanced Scheduler Test Suite Completed!")
    return True

if __name__ == "__main__":
    main()
