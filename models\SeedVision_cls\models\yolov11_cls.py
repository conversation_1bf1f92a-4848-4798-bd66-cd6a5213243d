'''
训练一个基础yolov11的模型进行分类，然后用于ai自动标注
'''

from ultralytics import Y<PERSON><PERSON>

def main():
    # 加载预训练的YOLO11模型
    model = YOLO('yolo11s.pt')  # 加载预训练模型

    # 训练模型
    # 对数据进行多种预处理再输入
    model.train(data=r'E:\Proj\pytorch-model-train\dataset\SeedVisionDataset\YOLO_Data\yolotrain1\data.yaml', # 数据集配置文件路径
                epochs=10, # 训练轮数
                imgsz=680, # 输入图像大小
                batch=1, # 批大小
                device=0,# 使用的设备索引，0表示使用第一个GPU
                optimizer='SGD', # 优化器类型
                lr0=0.01, # 初始学习率
                lrf=0.01,# 最终学习率
                save = True,
                save_dir = 'results/base_yolo/train', # 保存训练结果的路径
                # resume = True, # 是否从上次中断的地方继续训练
                augment=True, # 启用数据增强
                mixup=0.1, # 混合增强
                mosaic=1.0, # 马赛克增强
                degrees=0.5, # 旋转角度范围
                translate=0.1, # 平移范围
                scale=0.5, # 缩放范围
                shear=0.3, # 剪切变换范围
                perspective=0.0005, # 透视变换
                flipud=0.5, # 上下翻转概率
                fliplr=0.5, # 左右翻转概率
                hsv_h=0.015, # HSV色调增强
                hsv_s=0.7, # HSV饱和度增强
                hsv_v=0.4, # HSV亮度增强
                )  # 训练模型
    # 验证
    metrics = model.val()  # 在验证集上评估模型性能
    print(metrics)  # 打印验证结果

if __name__ == '__main__':
    main()
