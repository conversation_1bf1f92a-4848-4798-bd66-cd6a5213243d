#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SeedVision v1 - 训练功能测试脚本

专门测试训练相关功能，包括：
1. 数据加载和采样
2. 模型训练流程
3. Original级别评估
4. 可视化生成
5. 调度器训练

使用方法：
python training_test.py [--full]  # --full 进行完整训练测试
"""

import sys
import os
import argparse
import time
import torch
from datetime import datetime

# 添加路径
sys.path.append('E:\Proj\pytorch-model-train')
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_sampling():
    """测试数据采样功能"""
    print("📊 测试数据采样功能...")

    try:
        from tools.data.load_data import load_data, get_subset, load_batch

        # 加载数据
        data = load_data()
        print(f"   ✅ 数据加载成功: {len(data)} 条记录")

        # 测试子集获取
        train_data = get_subset(data, 'train')
        val_data = get_subset(data, 'val')
        print(f"   ✅ 子集获取成功: 训练集 {len(train_data)}, 验证集 {len(val_data)}")

        # 测试批次加载
        batch_data = load_batch(train_data, sample_size=100)
        print(f"   ✅ 批次加载成功: {len(batch_data)} 个样本")

        return True, f"数据采样功能正常"

    except Exception as e:
        return False, f"数据采样测试失败: {e}"

def test_original_evaluation():
    """测试Original级别评估"""
    print("🎯 测试Original级别评估...")

    try:
        import numpy as np
        from tools.training.validate import evaluate_original_level

        # 创建测试数据
        predictions = np.array([
            [20.1, 35.1], [19.9, 34.9], [20.0, 35.0],  # Original A
            [25.1, 40.1], [24.9, 39.9],  # Original B
        ])

        labels = np.array([
            [20.0, 35.0], [20.0, 35.0], [20.0, 35.0],  # Original A
            [25.0, 40.0], [25.0, 40.0],  # Original B
        ])

        original_names = ['A', 'A', 'A', 'B', 'B']

        # 执行评估
        metrics, results = evaluate_original_level(predictions, labels, original_names)

        if metrics and results:
            oil_r2 = metrics['oil']['R2']
            protein_r2 = metrics['protein']['R2']
            print(f"   ✅ Original评估成功: Oil R²={oil_r2:.4f}, Protein R²={protein_r2:.4f}")
            return True, f"Original级别评估功能正常"
        else:
            return False, "Original评估返回空结果"

    except Exception as e:
        return False, f"Original评估测试失败: {e}"

def test_quick_training():
    """测试快速训练"""
    print("🏃 测试快速训练（1 epoch, 50 samples）...")

    try:
        from config.config_loader import ConfigLoader
        from tools.training.train import train_model

        # 加载配置
        config_loader = ConfigLoader()
        configs = config_loader.get_enabled_training_configs()

        if not configs:
            return False, "没有可用的训练配置"

        config = configs[0]

        # 创建快速测试配置
        test_config = {
            'model_config': config['model'],
            'hyperparam_config': config.get('hyperparameter_config_data', {}),
            'transform': config.get('transform'),
            'result_dir': 'output/training_test',
            'device': torch.device('cuda' if torch.cuda.is_available() else 'cpu'),
            'num_epochs': 1,
            'batch_size': 5,
            'sample_size': 50,
            'task_name': 'TrainingTest',
            'sampling_strategy_config': {
                'strategy_type': 'random',
                'parameters': {'sample_size': 50, 'seed': 42}
            }
        }

        print(f"   🎯 配置: {test_config['num_epochs']} epoch, {test_config['sample_size']} samples")
        print(f"   🖥️  设备: {test_config['device']}")

        # 执行训练
        start_time = time.time()
        history, model = train_model(**test_config)
        duration = time.time() - start_time

        if history and 'train_losses' in history and len(history['train_losses']) > 0:
            final_loss = history['train_losses'][-1]
            print(f"   ✅ 训练完成: 最终损失={final_loss:.4f}, 耗时={duration:.1f}秒")

            # 检查输出文件
            output_files = ['loss_curve.png', 'oil_regression.png', 'protein_regression.png']
            existing_files = []
            for file in output_files:
                if os.path.exists(os.path.join(test_config['result_dir'], file)):
                    existing_files.append(file)

            print(f"   📊 生成文件: {len(existing_files)}/{len(output_files)} 个")

            return True, f"快速训练成功，耗时 {duration:.1f}秒"
        else:
            return False, "训练返回结果异常"

    except Exception as e:
        return False, f"快速训练失败: {e}"

def test_scheduler_training():
    """测试调度器训练"""
    print("📋 测试调度器训练...")

    try:
        from scheduler import SchedulerManager, TrainingTask, TaskPriority, ResourceEstimator

        # 测试资源预估
        estimator = ResourceEstimator()
        test_config = {
            'name': 'scheduler_test',
            'model': {'embed_dim': 32, 'depths': [1, 1, 2, 1]},
            'resources': {'batch_size': 5},
            'transform_config': '224x224_norm',
            'training': {'num_epochs': 1},
            'dataset_config': {'strategy_parameters': {'total_samples': 50}}
        }

        report = estimator.generate_resource_report(test_config)
        memory_gb = report['memory_estimate']['total_gb']
        can_run = report['can_run']

        print(f"   📊 资源预估: {memory_gb:.2f}GB, 可运行: {can_run}")

        if not can_run:
            return False, f"资源不足，无法运行测试"

        # 创建调度管理器
        scheduler = SchedulerManager(max_gpu_memory=8.0, max_concurrent_tasks=1)

        # 创建测试任务
        task = TrainingTask(
            task_id='scheduler_test_001',
            name='调度器测试任务',
            config=test_config,
            priority=TaskPriority.HIGH
        )

        print(f"   ✅ 调度器创建成功")
        print(f"   ✅ 测试任务创建成功")

        return True, "调度器功能正常"

    except Exception as e:
        return False, f"调度器测试失败: {e}"

def test_visualization():
    """测试可视化功能"""
    print("📈 测试可视化功能...")

    try:
        from tools.training.visualize import plot_train_val_loss, plot_oil_regression, plot_protein_regression
        import numpy as np
        import matplotlib.pyplot as plt

        # 创建测试数据
        train_losses = [2.5, 2.0, 1.8, 1.5, 1.3]
        val_losses = [2.8, 2.2, 1.9, 1.6, 1.4]

        predictions = np.random.randn(20, 2) * 5 + 25
        labels = np.random.randn(20, 2) * 5 + 25

        # 测试损失曲线
        plt.figure(figsize=(8, 6))
        plot_train_val_loss(train_losses, val_losses, save_path='output/test_loss.png')
        plt.close()

        # 测试回归图
        plt.figure(figsize=(8, 6))
        plot_oil_regression(predictions[:, 0], labels[:, 0], save_path='output/test_oil.png')
        plt.close()

        plt.figure(figsize=(8, 6))
        plot_protein_regression(predictions[:, 1], labels[:, 1], save_path='output/test_protein.png')
        plt.close()

        # 检查文件是否生成
        test_files = ['output/test_loss.png', 'output/test_oil.png', 'output/test_protein.png']
        generated = sum(1 for f in test_files if os.path.exists(f))

        print(f"   ✅ 可视化测试完成: {generated}/{len(test_files)} 个文件生成")

        return True, f"可视化功能正常"

    except Exception as e:
        return False, f"可视化测试失败: {e}"

# 颜色定义
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def run_training_tests(full_test=False):
    """运行训练测试"""
    print(f"{Colors.BOLD}[TRAINING TEST]{Colors.END} SeedVision v1 - 训练功能测试")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试模式: {'完整测试' if full_test else '快速测试'}")
    print()

    # 确保输出目录存在
    os.makedirs('output', exist_ok=True)

    start_time = time.time()
    results = []

    # 运行测试
    print(f"{Colors.BLUE}[STEP 1]{Colors.END} 基础功能测试")
    print("-" * 40)

    success, message = test_data_sampling()
    results.append(("数据采样", success, message))

    success, message = test_original_evaluation()
    results.append(("Original评估", success, message))

    success, message = test_visualization()
    results.append(("可视化", success, message))

    success, message = test_scheduler_training()
    results.append(("调度器", success, message))

    if full_test:
        print("\n2️⃣ 完整训练测试")
        print("-" * 40)

        success, message = test_quick_training()
        results.append(("快速训练", success, message))

    # 统计结果
    total_tests = len(results)
    passed_tests = sum(1 for _, success, _ in results if success)
    failed_tests = total_tests - passed_tests
    duration = time.time() - start_time

    # 显示结果
    print(f"\n📊 测试结果:")
    print("-" * 60)

    for test_name, success, message in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")

    print("\n" + "=" * 60)
    print("📈 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过: {passed_tests}")
    print(f"   失败: {failed_tests}")
    print(f"   成功率: {(passed_tests/total_tests*100):.1f}%")
    print(f"   耗时: {duration:.1f}秒")

    # 结论
    print("\n🎯 测试结论:")
    if failed_tests == 0:
        print("✅ 所有训练功能测试通过！")
        print("🚀 系统已准备好进行实际训练。")
        print("\n💡 下一步建议:")
        if not full_test:
            print("   1. 运行完整训练测试: python training_test.py --full")
        print("   2. 开始实际训练: python main.py --sequential")
        print("   3. 使用调度器: python main_scheduler.py --mode schedule")
    else:
        print(f"⚠️  有 {failed_tests} 个测试失败。")
        print("🔧 请修复失败的功能后再进行实际训练。")

    return failed_tests == 0

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='SeedVision v1 训练功能测试')
    parser.add_argument('--full', action='store_true', help='进行完整训练测试（包含实际训练）')
    args = parser.parse_args()

    try:
        success = run_training_tests(args.full)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
