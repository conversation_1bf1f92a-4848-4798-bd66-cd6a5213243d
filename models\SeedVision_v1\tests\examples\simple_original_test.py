#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Original级别评估测试

直接测试main.py的训练功能，验证Original级别评估是否正常工作
"""

import sys
import os

# 添加路径
sys.path.append('E:\Proj\pytorch-model-train')

def main():
    """运行简单的训练测试"""
    print("🚀 Simple Original-level Evaluation Test")
    print("=" * 60)
    
    # 切换到正确的目录
    original_dir = os.getcwd()
    
    try:
        # 运行main.py进行训练测试
        print("📋 Running main.py with sequential training...")
        print("   This will test Original-level evaluation in real training")
        print()
        
        # 导入main模块
        from main import main as run_main
        
        # 模拟命令行参数
        import sys
        original_argv = sys.argv.copy()
        sys.argv = ['main.py', '--sequential', '--max_memory', '8.0']
        
        print("🏃 Starting training with Original-level evaluation...")
        print("-" * 50)
        
        # 运行训练
        run_main()
        
        print("-" * 50)
        print("✅ Training completed successfully!")
        
        # 恢复原始参数
        sys.argv = original_argv
        
        print("\n🎉 Original-level evaluation test completed!")
        print("\n💡 Expected output during training:")
        print("   📊 Sample-level R²:")
        print("     Oil: Training=0.xxxx, Validation=0.xxxx")
        print("     Protein: Training=0.xxxx, Validation=0.xxxx")
        print("   🎯 Original-level R²:")
        print("     Oil: 0.xxxx")
        print("     Protein: 0.xxxx")
        print("\n✅ If you saw both sample-level and original-level R² values,")
        print("   the Original-level evaluation is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复原始目录
        os.chdir(original_dir)

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed. Please check the implementation.")
