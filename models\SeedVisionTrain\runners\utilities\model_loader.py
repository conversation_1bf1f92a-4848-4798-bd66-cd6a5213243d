"""
SeedVision_v1 模型加载器
负责加载和管理FasterNet模型，使用V0版本的实现
"""

import os
import torch
from models.SeedVision_v1.models.FasterNet import model as FasterNet
from models.abstract_classes.base_model_loader import BaseModelLoader
from utils.logger import logger

class ModelLoader(BaseModelLoader):
    """SeedVision_v1 模型加载器类"""

    def __init__(self, config=None):
        """
        初始化模型加载器

        Args:
            config (dict, optional): 配置参数，包含模型参数等
        """
        super(ModelLoader, self).__init__()
        self.config = config or {}
        self.model = None
        self.device = self.config.get('device', 'cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"模型将使用设备: {self.device}")

    def load_model(self, weight_path=None):
        """
        加载模型

        Args:
            weight_path (str, optional): 模型权重路径，如果为None则使用配置中的路径

        Returns:
            model: 加载好的模型
        """
        # 获取模型参数
        num_classes = self.config.get('num_classes', 2)

        # 创建模型
        self.model = FasterNet(num_classes=num_classes, device=self.device)
        logger.info(f"创建FasterNet模型，类别数: {num_classes}")

        # 加载权重
        if weight_path is None:
            weight_path = self.config.get('weight_path')

        if weight_path and os.path.exists(weight_path):
            try:
                self.model.load_model_weight(weight_path)
                logger.info(f"成功加载模型权重: {weight_path}")
            except Exception as e:
                logger.error(f"加载模型权重失败: {e}")
        else:
            logger.warning(f"未找到模型权重文件: {weight_path}")

        # 将模型移至指定设备
        self.model = self.model.to(self.device)
        return self.model

    def save_model(self, save_path=None):
        """
        保存模型

        Args:
            save_path (str, optional): 保存路径，如果为None则使用配置中的路径

        Returns:
            bool: 是否保存成功
        """
        if self.model is None:
            logger.error("没有可保存的模型")
            return False

        if save_path is None:
            save_path = self.config.get('save_path', 'weights/FasterNet.pt')

        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            # 保存模型
            torch.save(self.model.model.state_dict(), save_path)
            logger.info(f"模型已保存至: {save_path}")
            return True
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            return False

    def predict(self, inputs):
        """
        使用模型进行预测

        Args:
            inputs (torch.Tensor): 输入数据

        Returns:
            torch.Tensor: 预测结果
        """
        if self.model is None:
            logger.error("模型未加载，无法进行预测")
            return None

        # 确保输入在正确的设备上
        if inputs.device != self.device:
            inputs = inputs.to(self.device)

        # 设置为评估模式并进行预测
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(inputs)

        return outputs

    def get_model(self):
        """
        获取当前模型

        Returns:
            model: 当前模型
        """
        return self.model

    def train(self, train_loader, val_loader=None, epochs=100, learning_rate=0.001, weight_decay=1e-4,
              save_dir='weights', save_freq=10, model_name='SeedVisionFasterNet.pt'):
        """
        训练模型

        Args:
            train_loader (DataLoader): 训练数据加载器
            val_loader (DataLoader, optional): 验证数据加载器
            epochs (int): 训练轮数
            learning_rate (float): 学习率
            weight_decay (float): 权重衰减
            save_dir (str): 模型保存目录
            save_freq (int): 保存频率（每多少个epoch保存一次）
            model_name (str): 模型名称

        Returns:
            dict: 训练历史记录
        """
        from models.SeedVision_v1.tools.train_utils import train_epoch, validate
        import torch.optim as optim
        import torch.nn as nn
        import os

        if self.model is None:
            logger.error("模型未加载，无法进行训练")
            return None

        # 确保保存目录存在
        os.makedirs(save_dir, exist_ok=True)

        # 定义损失函数和优化器
        criterion = nn.MSELoss()  # 根据任务选择合适的损失函数
        optimizer = optim.Adam(self.model.parameters(), lr=learning_rate, weight_decay=weight_decay)

        # 学习率调度器
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', factor=0.5, patience=5, verbose=True)

        # 训练历史记录
        history = {
            'train_loss': [],
            'val_loss': []
        }

        # 最佳验证损失
        best_val_loss = float('inf')

        # 训练循环
        for epoch in range(epochs):
            # 训练一个epoch
            train_loss = train_epoch(self.model, train_loader, criterion, optimizer, self.device)
            history['train_loss'].append(train_loss)

            logger.info(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}")

            # 验证
            if val_loader is not None:
                val_loss = validate(self.model, val_loader, criterion, self.device)
                history['val_loss'].append(val_loss)

                logger.info(f"Epoch {epoch+1}/{epochs}, Val Loss: {val_loss:.4f}")

                # 更新学习率
                scheduler.step(val_loss)

                # 保存最佳模型
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    best_model_path = os.path.join(save_dir, f"best_{model_name}")
                    self.save_model(best_model_path)
                    logger.info(f"保存最佳模型，验证损失: {val_loss:.4f}")

            # 定期保存模型
            if (epoch + 1) % save_freq == 0:
                save_path = os.path.join(save_dir, f"epoch_{epoch+1}_{model_name}")
                self.save_model(save_path)

        # 保存最终模型
        final_save_path = os.path.join(save_dir, model_name)
        self.save_model(final_save_path)
        logger.info(f"训练完成，最终模型已保存至: {final_save_path}")

        return history
