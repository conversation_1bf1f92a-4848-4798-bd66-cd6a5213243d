#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调度器使用示例

演示如何使用资源预估、任务调度和进程管理功能
"""

import time
import json
from datetime import datetime
from scheduler import SchedulerManager, TrainingTask, TaskPriority, ResourceEstimator

def example_resource_estimation():
    """资源预估示例"""
    print("🔍 资源预估示例")
    print("=" * 60)
    
    estimator = ResourceEstimator()
    
    # 示例配置
    config = {
        'name': 'test_config',
        'model': {
            'embed_dim': 192,
            'depths': [3, 4, 18, 3],
            'mlp_ratio': 2.0,
            'n_div': 4,
            'drop_path_rate': 0.3,
            'layer_scale_init_value': 0,
            'patch_size': 4,
            'patch_stride': 4
        },
        'resources': {
            'batch_size': 40
        },
        'transform_config': '224x224_norm',
        'training': {
            'num_epochs': 100
        },
        'dataset_config': {
            'strategy_parameters': {
                'total_samples': 2400
            }
        }
    }
    
    # 生成资源报告
    report = estimator.generate_resource_report(config)
    
    print(f"📊 任务: {report['task_name']}")
    print(f"💾 显存预估:")
    memory = report['memory_estimate']
    print(f"  - 总显存: {memory['total_gb']:.2f} GB")
    print(f"  - 模型参数: {memory['parameters_mb']:.1f} MB")
    print(f"  - 激活值: {memory['activations_mb']:.1f} MB")
    print(f"  - 梯度: {memory['gradients_mb']:.1f} MB")
    print(f"  - 优化器: {memory['optimizer_mb']:.1f} MB")
    print(f"  - 模型参数量: {memory['model_params_millions']:.2f} M")
    
    print(f"⏱️ 时间预估:")
    time_est = report['time_estimate']
    print(f"  - 总训练时间: {time_est['total_hours']:.1f} 小时")
    print(f"  - 每epoch时间: {time_est['epoch_minutes']:.1f} 分钟")
    print(f"  - 样本数量: {time_est['samples_per_epoch']}")
    
    print(f"✅ 可运行: {report['can_run']}")
    print(f"📝 原因: {report['run_reason']}")
    
    if report['recommendations']:
        print(f"💡 建议:")
        for rec in report['recommendations']:
            print(f"  - {rec}")
    
    print()

def example_task_scheduling():
    """任务调度示例"""
    print("📋 任务调度示例")
    print("=" * 60)
    
    # 创建调度管理器
    scheduler = SchedulerManager(max_gpu_memory=8.0, max_concurrent_tasks=2)
    
    # 启动调度器
    scheduler.start()
    
    # 创建示例任务
    tasks = []
    
    # 任务1: 高优先级，小模型
    task1_config = {
        'name': 'small_model_urgent',
        'model': {'embed_dim': 128, 'depths': [2, 3, 12, 2]},
        'resources': {'batch_size': 20},
        'transform_config': '112x112_norm',
        'training': {'num_epochs': 50},
        'dataset_config': {'strategy_parameters': {'total_samples': 600}}
    }
    
    task1 = TrainingTask(
        task_id='task_001',
        name='小模型紧急任务',
        config=task1_config,
        priority=TaskPriority.URGENT
    )
    
    # 任务2: 普通优先级，标准模型
    task2_config = {
        'name': 'standard_model',
        'model': {'embed_dim': 192, 'depths': [3, 4, 18, 3]},
        'resources': {'batch_size': 40},
        'transform_config': '224x224_norm',
        'training': {'num_epochs': 100},
        'dataset_config': {'strategy_parameters': {'total_samples': 2400}}
    }
    
    task2 = TrainingTask(
        task_id='task_002',
        name='标准模型训练',
        config=task2_config,
        priority=TaskPriority.NORMAL
    )
    
    # 任务3: 低优先级，大模型
    task3_config = {
        'name': 'large_model',
        'model': {'embed_dim': 256, 'depths': [4, 6, 20, 4]},
        'resources': {'batch_size': 60},
        'transform_config': '224x224_norm',
        'training': {'num_epochs': 120},
        'dataset_config': {'strategy_parameters': {'total_samples': 4800}}
    }
    
    task3 = TrainingTask(
        task_id='task_003',
        name='大模型训练',
        config=task3_config,
        priority=TaskPriority.LOW
    )
    
    # 提交任务
    print("📤 提交任务...")
    scheduler.submit_task(task1)
    scheduler.submit_task(task2)
    scheduler.submit_task(task3)
    
    # 监控任务状态
    print("👀 监控任务状态...")
    for i in range(10):  # 监控10次
        time.sleep(2)
        
        status = scheduler.get_status()
        print(f"\n⏰ 时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"📊 队列状态:")
        print(f"  - 等待: {status['scheduler']['pending_count']}")
        print(f"  - 运行: {status['scheduler']['running_count']}")
        print(f"  - 完成: {status['scheduler']['completed_count']}")
        print(f"  - 失败: {status['scheduler']['failed_count']}")
        
        if status['processes']['total_processes'] > 0:
            print(f"🔄 进程状态:")
            print(f"  - 总进程: {status['processes']['total_processes']}")
            print(f"  - 已启动: {status['processes']['statistics']['total_started']}")
            print(f"  - 已完成: {status['processes']['statistics']['total_completed']}")
        
        # 如果所有任务都完成了，退出监控
        if (status['scheduler']['pending_count'] == 0 and 
            status['scheduler']['running_count'] == 0):
            break
    
    # 生成报告
    print("\n📋 生成最终报告...")
    report = scheduler.generate_report()
    
    print(f"📈 调度统计:")
    stats = report['scheduler_report']['statistics']
    print(f"  - 总提交: {stats['total_submitted']}")
    print(f"  - 总完成: {stats['total_completed']}")
    print(f"  - 总失败: {stats['total_failed']}")
    print(f"  - 成功率: {stats['success_rate_percent']:.1f}%")
    
    # 停止调度器
    scheduler.stop()
    print("\n✅ 调度器已停止")

def example_resource_monitoring():
    """资源监控示例"""
    print("📊 资源监控示例")
    print("=" * 60)
    
    estimator = ResourceEstimator()
    
    # 获取系统资源
    resources = estimator.get_system_resources()
    
    print(f"💻 CPU信息:")
    cpu = resources['cpu']
    print(f"  - 使用率: {cpu['usage_percent']:.1f}%")
    print(f"  - 核心数: {cpu['core_count']}")
    
    print(f"🧠 内存信息:")
    memory = resources['memory']
    print(f"  - 总内存: {memory['total_gb']:.1f} GB")
    print(f"  - 已使用: {memory['used_gb']:.1f} GB")
    print(f"  - 可用: {memory['available_gb']:.1f} GB")
    print(f"  - 使用率: {memory['usage_percent']:.1f}%")
    
    print(f"🎮 GPU信息:")
    if resources['gpu']:
        for gpu_id, gpu_info in resources['gpu'].items():
            print(f"  - {gpu_id}: {gpu_info['name']}")
            print(f"    总显存: {gpu_info['total_gb']:.1f} GB")
            print(f"    已分配: {gpu_info['allocated_gb']:.1f} GB")
            print(f"    已缓存: {gpu_info['cached_gb']:.1f} GB")
            print(f"    可用: {gpu_info['free_gb']:.1f} GB")
            print(f"    使用率: {gpu_info['utilization_percent']:.1f}%")
    else:
        print("  - 未检测到GPU")
    
    print()

def save_example_configs():
    """保存示例配置文件"""
    print("💾 保存示例配置文件...")
    
    configs = {
        'small_test': {
            'name': 'small_test',
            'description': '小规模测试配置',
            'model': {
                'embed_dim': 64,
                'depths': [2, 2, 8, 2],
                'mlp_ratio': 2.0,
                'n_div': 2,
                'drop_path_rate': 0.1
            },
            'resources': {'batch_size': 20},
            'transform_config': '56x56_norm',
            'training': {'num_epochs': 20},
            'dataset_config': {
                'strategy_parameters': {'total_samples': 400}
            }
        },
        'standard': {
            'name': 'standard',
            'description': '标准配置',
            'model': {
                'embed_dim': 192,
                'depths': [3, 4, 18, 3],
                'mlp_ratio': 2.0,
                'n_div': 4,
                'drop_path_rate': 0.3
            },
            'resources': {'batch_size': 40},
            'transform_config': '224x224_norm',
            'training': {'num_epochs': 100},
            'dataset_config': {
                'strategy_parameters': {'total_samples': 2400}
            }
        },
        'large_scale': {
            'name': 'large_scale',
            'description': '大规模配置',
            'model': {
                'embed_dim': 256,
                'depths': [4, 6, 20, 4],
                'mlp_ratio': 2.0,
                'n_div': 4,
                'drop_path_rate': 0.4
            },
            'resources': {'batch_size': 60},
            'transform_config': '224x224_norm',
            'training': {'num_epochs': 150},
            'dataset_config': {
                'strategy_parameters': {'total_samples': 4800}
            }
        }
    }
    
    # 保存配置文件
    with open('scheduler/example_configs.json', 'w') as f:
        json.dump(configs, f, indent=2)
    
    print("✅ 示例配置已保存到 scheduler/example_configs.json")

def main():
    """主函数"""
    print("🚀 SeedVision v1 调度器示例")
    print("=" * 80)
    
    try:
        # 1. 资源预估示例
        example_resource_estimation()
        
        # 2. 资源监控示例
        example_resource_monitoring()
        
        # 3. 保存示例配置
        save_example_configs()
        
        # 4. 任务调度示例 (注释掉，避免实际启动训练)
        print("📋 任务调度示例 (已注释，避免实际启动训练)")
        print("如需测试任务调度，请取消注释 example_task_scheduling() 调用")
        # example_task_scheduling()
        
        print("\n🎉 所有示例运行完成！")
        print("\n💡 使用建议:")
        print("1. 查看 scheduler/example_configs.json 了解配置格式")
        print("2. 使用 ResourceEstimator 预估任务资源需求")
        print("3. 使用 SchedulerManager 管理多个训练任务")
        print("4. 监控系统资源使用情况")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
