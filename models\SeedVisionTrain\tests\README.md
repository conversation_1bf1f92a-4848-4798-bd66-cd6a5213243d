# SeedVision v1 测试套件

## 概述

本目录包含SeedVision v1系统的完整测试套件，用于验证系统各个组件的功能和稳定性。

## 测试文件说明

### 1. test_config.py
**功能**: 配置系统测试
- 验证YAML配置文件的加载和解析
- 检查配置参数的完整性和正确性
- 测试original采样配置的读取
- 验证训练参数配置

**运行方式**:
```bash
python test/test_config.py
```

### 2. test_data_loading.py
**功能**: 数据加载功能测试
- 验证基本数据加载功能
- 测试original级别采样
- 检查数据分布和统计信息
- 验证配置集成

**运行方式**:
```bash
python test/test_data_loading.py
```

### 3. test_cross_subset_sampling.py
**功能**: 跨子集采样测试
- 验证跨子集采样算法
- 测试数据来源分布
- 比较传统采样和跨子集采样的效果
- 验证配置集成

**运行方式**:
```bash
python test/test_cross_subset_sampling.py
```

### 4. test_validation_fix.py
**功能**: 验证集修复测试
- 测试验证集采样策略
- 验证备选策略的有效性
- 模拟训练验证过程
- 确保ZeroDivisionError已修复

**运行方式**:
```bash
python test/test_validation_fix.py
```

### 5. test_data_format_fix.py
**功能**: 数据格式兼容性测试
- 测试SeedDataset对字典格式数据的处理
- 验证混合数据格式支持
- 测试DataLoader集成
- 验证错误处理机制

**运行方式**:
```bash
python test/test_data_format_fix.py
```

### 6. debug_data_format.py
**功能**: 数据格式调试工具
- 分析数据格式结构
- 调试数据加载问题
- 检查采样后的数据格式
- 提供详细的调试信息

**运行方式**:
```bash
python test/debug_data_format.py
```

## 运行所有测试

### 快速测试
```bash
# 测试配置系统
python test/test_config.py

# 测试数据加载
python test/test_data_loading.py

# 测试跨子集采样
python test/test_cross_subset_sampling.py
```

### 完整测试
```bash
# 运行所有测试
cd models/SeedVision_v1
python test/test_config.py
python test/test_data_loading.py
python test/test_cross_subset_sampling.py
python test/test_validation_fix.py
python test/test_data_format_fix.py
```

## 测试结果解读

### 成功标志
- ✅ **PASS**: 测试通过
- 📊 **数据统计**: 显示数据分布信息
- 🎯 **采样结果**: 显示采样效果
- 🛡️ **错误处理**: 验证错误处理机制

### 失败处理
- ❌ **FAIL**: 测试失败
- 检查错误堆栈信息
- 验证数据库连接
- 检查配置文件语法

## 测试覆盖范围

### 功能测试
- [x] 配置加载和解析
- [x] 数据加载和采样
- [x] 跨子集采样算法
- [x] 数据格式兼容性
- [x] 错误处理机制

### 性能测试
- [x] 采样效率验证
- [x] 内存使用检查
- [x] 数据加载速度

### 集成测试
- [x] 端到端数据流
- [x] 配置系统集成
- [x] 训练流程验证

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```
❌ Failed to load data: database connection error
```
**解决方案**: 检查数据库连接配置和网络状态

#### 2. 配置文件错误
```
❌ Configuration loading failed: YAML syntax error
```
**解决方案**: 检查training_config.yaml语法

#### 3. 内存不足
```
❌ Out of memory during sampling
```
**解决方案**: 减少采样数量或target_originals

### 调试技巧
1. 使用 `debug_data_format.py` 分析数据结构
2. 检查详细的错误堆栈信息
3. 逐步运行测试，定位问题
4. 查看系统日志文件

## 测试数据

### 预期结果
- **总数据量**: ~195,000条
- **Original数量**: ~2,680个
- **跨子集采样提升**: +107.9%可用original
- **数据格式**: 支持字典和元组格式

### 性能指标
- **采样速度**: <30秒完成2400样本采样
- **内存使用**: <2GB内存占用
- **错误率**: 0%测试失败率

## 维护说明

### 添加新测试
1. 在test目录创建新的测试文件
2. 遵循现有的测试模式
3. 添加适当的错误处理
4. 更新本README文档

### 更新测试
1. 保持测试的独立性
2. 确保测试可重复运行
3. 添加详细的测试说明
4. 验证测试覆盖范围

---

**🧪 完整的测试套件确保SeedVision v1系统的稳定性和可靠性！**
