#!/usr/bin/env python3
"""
调试数据格式问题的脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('E:\Proj\pytorch-model-train')

from tools.myscripts.load_data import load_data, fixed_sample_by_original_cross_subset

def debug_data_format():
    """调试数据格式"""
    print("🔍 Debugging Data Format...")
    print("="*60)
    
    try:
        # 加载数据
        data = load_data()
        print(f"📊 Original data loaded: {len(data)} items")
        
        # 检查原始数据格式
        if len(data) > 0:
            sample_item = data[0]
            print(f"\n🔍 Original data item format:")
            print(f"  - Type: {type(sample_item)}")
            print(f"  - Length: {len(sample_item) if hasattr(sample_item, '__len__') else 'N/A'}")
            print(f"  - Keys: {list(sample_item.keys()) if isinstance(sample_item, dict) else 'Not a dict'}")
            print(f"  - Sample: {sample_item}")
        
        # 测试跨子集采样
        config = {
            'target_originals': 2,
            'samples_per_original': 5,
            'train_ratio': 0.8,
            'val_ratio': 0.1,
            'test_ratio': 0.1
        }
        
        print(f"\n🌐 Testing cross-subset sampling...")
        train_data, val_data, test_data, original_mapping = fixed_sample_by_original_cross_subset(
            data, original_sampling_config=config
        )
        
        print(f"\n🔍 Cross-subset sampling results:")
        print(f"  - Train data: {len(train_data)} items")
        print(f"  - Val data: {len(val_data)} items")
        print(f"  - Test data: {len(test_data)} items")
        
        # 检查采样后的数据格式
        if len(train_data) > 0:
            sample_train = train_data[0]
            print(f"\n🔍 Train data item format:")
            print(f"  - Type: {type(sample_train)}")
            print(f"  - Length: {len(sample_train) if hasattr(sample_train, '__len__') else 'N/A'}")
            print(f"  - Keys: {list(sample_train.keys()) if isinstance(sample_train, dict) else 'Not a dict'}")
            print(f"  - Sample: {sample_train}")
        
        if len(val_data) > 0:
            sample_val = val_data[0]
            print(f"\n🔍 Val data item format:")
            print(f"  - Type: {type(sample_val)}")
            print(f"  - Length: {len(sample_val) if hasattr(sample_val, '__len__') else 'N/A'}")
            print(f"  - Keys: {list(sample_val.keys()) if isinstance(sample_val, dict) else 'Not a dict'}")
            print(f"  - Sample: {sample_val}")
        
        # 检查original mapping格式
        if original_mapping:
            first_original = list(original_mapping.keys())[0]
            mapping_data = original_mapping[first_original]
            print(f"\n🔍 Original mapping format:")
            print(f"  - Original: {first_original}")
            print(f"  - Mapping keys: {list(mapping_data.keys()) if isinstance(mapping_data, dict) else 'Not a dict'}")
            
            if 'all' in mapping_data:
                sample_all = mapping_data['all'][0] if len(mapping_data['all']) > 0 else None
                if sample_all:
                    print(f"  - 'all' item format:")
                    print(f"    - Type: {type(sample_all)}")
                    print(f"    - Length: {len(sample_all) if hasattr(sample_all, '__len__') else 'N/A'}")
                    print(f"    - Keys: {list(sample_all.keys()) if isinstance(sample_all, dict) else 'Not a dict'}")
                    print(f"    - Sample: {sample_all}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_data_format()
