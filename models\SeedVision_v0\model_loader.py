import torch
import torch.nn as nn
import torch.optim as optim
import os
from abstract_classes.base_model_loader import BaseModelLoader
from utils.logger import Logger
from models.SeedVision_v0.models import MODEL_OPTIONS, MPViT, ResNet, FasterNet, EfficientNet, Swin, VanillaNet

class SeedVisionModelLoader(BaseModelLoader):
    """
    SeedVision模型加载器，用于加载和管理SeedVision模型
    """

    def __init__(self, device='cpu', logger=None):
        """
        初始化SeedVision模型加载器

        Args:
            device (str): 设备类型，'cpu'或'cuda'
            logger (Logger, optional): 日志记录器
        """
        self.device = device
        self.logger = logger or Logger()
        self.model_weights_dir = os.path.join('weights', 'SeedVision_v0')

        # 确保权重目录存在
        os.makedirs(self.model_weights_dir, exist_ok=True)

        # 模型权重文件映射
        self.model_weights = {
            'MPViT': 'MPViT_best.pt',
            'ResNet': 'ResNet18_best.pt',
            'FasterNet': 'FasterNet_best.pt',
            'EfficientNet': 'EfficientNet_best.pt',
            'Swin': 'Swin_best.pt',
            'VanillaNet': 'VanillaNet_best.pt'
        }

    def create_model(self, model_type: MODEL_OPTIONS, num_classes=2, **kwargs):
        """
        创建指定类型的模型

        Args:
            model_type (str): 模型类型，必须是MODEL_OPTIONS中的一种
            num_classes (int): 分类数量
            **kwargs: 其他模型参数

        Returns:
            nn.Module: 创建的模型实例
        """
        self.logger.info(f"创建{model_type}模型...")

        if model_type == 'MPViT':
            model = MPViT(num_classes=num_classes, device=self.device)
        elif model_type == 'ResNet':
            model = ResNet(num_classes=num_classes, device=self.device)
        elif model_type == 'FasterNet':
            model = FasterNet(num_classes=num_classes, device=self.device)
        elif model_type == 'EfficientNet':
            model = EfficientNet(number_classes=num_classes, device=self.device)
        elif model_type == 'Swin':
            model = Swin(number_of_classes=num_classes, device=self.device)
        elif model_type == 'VanillaNet':
            model = VanillaNet(number_of_classes=num_classes, device=self.device)
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")

        model = model.to(self.device)
        return model

    def load_weight(self, model_type: MODEL_OPTIONS, model_path=None, **kwargs):
        """
        加载预训练模型

        Args:
            model_type (str): 模型类型
            model_path (str, optional): 模型权重文件路径，如果为None则使用默认路径
            **kwargs: 其他加载参数

        Returns:
            nn.Module: 加载了权重的模型实例
        """
        # 创建模型实例
        model = self.create_model(model_type, **kwargs)

        # 确定模型权重路径
        if model_path is None:
            if model_type not in self.model_weights:
                raise ValueError(f"未找到模型{model_type}的默认权重文件")
            model_path = os.path.join(self.model_weights_dir, self.model_weights[model_type])

        # 检查权重文件是否存在
        if not os.path.exists(model_path):
            self.logger.warning(f"模型权重文件不存在: {model_path}")
            return model

        # 加载模型权重
        try:
            self.logger.info(f"从{model_path}加载{model_type}模型权重...")
            model.load_model_weight(model_path)
            self.logger.info(f"{model_type}模型权重加载成功")
        except Exception as e:
            self.logger.error(f"加载模型权重时出错: {str(e)}")

        return model

    def get_optimizer(self, model, lr=0.001, weight_decay=1e-4, **kwargs):
        """
        获取优化器

        Args:
            model (nn.Module): 模型实例
            lr (float): 学习率
            weight_decay (float): 权重衰减
            **kwargs: 其他优化器参数

        Returns:
            Optimizer: 配置好的优化器
        """
        return optim.Adam(model.parameters(), lr=lr, weight_decay=weight_decay)

    def get_scheduler(self, optimizer, **kwargs):
        """
        获取学习率调度器

        Args:
            optimizer (Optimizer): 优化器实例
            **kwargs: 调度器参数

        Returns:
            LRScheduler: 学习率调度器
        """
        return optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode='min',
            factor=0.5,
            patience=5,
            verbose=True
        )