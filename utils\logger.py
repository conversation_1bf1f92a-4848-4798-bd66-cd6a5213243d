'''
日志文件
提供统一的日志记录功能，支持控制台和文件输出
每次启动创建新的日志文件
'''

import os
import json
import logging
import datetime
import sys
# sys.path.append('E:\Proj\pytorch-model-train')

class Logger:
    """
    日志管理类，提供统一的日志记录接口
    
    支持多级别日志、每次启动创建新日志文件、格式化输出等功能
    """
    
    _instance = None  # 单例模式实现
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(Logger, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, config_path='config.json', name='deep_learning_framework'):
        """
        初始化日志管理器
        
        Args:
            config_path (str): 配置文件路径
            name (str): 日志记录器名称
        """
        # 单例模式，避免重复初始化
        if self._initialized:
            return
        self._initialized = True
        
        # 创建日志记录器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)
        self.logger.propagate = False
        
        # 如果已经有处理器，则不重复添加
        if self.logger.handlers:
            return
            
        # 加载配置
        try:
            # 检查文件是否存在且不为空
            if os.path.exists(config_path) and os.path.getsize(config_path) > 0:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    log_config = config.get('logging', {})
            else:
                # 文件不存在或为空时使用默认配置
                log_config = {}
        except Exception as e:
            # 捕获所有异常并使用默认配置
            self.logger.warning(f"加载配置文件失败: {e}, 使用默认配置")
            log_config = {}
    
        # 设置默认配置
        default_config = {
            "level": "INFO",
            "format": "%(asctime)s - %(levelname)s - %(message)s",
            "log_dir": "logs"
        }
        log_config = {**default_config, **log_config}  # 合并配置
        
        # 设置日志级别
        level = getattr(logging, log_config.get('level', 'INFO'))
        self.logger.setLevel(level)
        
        # 日志格式
        formatter = logging.Formatter(
            log_config.get('format', '%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 创建日志目录
        log_dir = log_config.get('log_dir', 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        # 生成唯一的日志文件名（基于时间戳）
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_filename = f"{name}_{timestamp}.log"
        log_path = os.path.join(log_dir, log_filename)
        
        # 文件处理器 - 每次启动创建新文件
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
        
        # 记录启动信息
        self.logger.info(f"日志系统初始化完成，日志文件: {log_path}")
    
    def get_logger(self):
        """获取日志记录器实例"""
        return self.logger


# 创建全局唯一的日志记录器实例
logger = Logger().get_logger()
