# 🌱 SeedVision v1 - 企业级深度学习训练管理系统

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PyTorch](https://img.shields.io/badge/PyTorch-1.9+-red.svg)](https://pytorch.org)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

## 🎯 项目概述

SeedVision v1 是一个**企业级深度学习训练管理系统**，专为种子品质预测而设计。系统结合了YOLO目标检测和FasterNet回归预测，具备智能任务调度、多策略数据采样、Original级别评估和完整的可视化功能。

### 🏆 核心特色

- ✅ **智能任务调度系统** - 自动任务测试、动态资源管理、智能任务过滤
- ✅ **多策略数据采样** - 支持随机、平衡、分层等多种采样策略
- ✅ **Original级别评估** - 基于原始图像的聚合评估，更贴近实际应用
- ✅ **高质量可视化** - 智能文本位置、标准化输出、专业图表
- ✅ **企业级架构** - 模块化设计、完整测试、标准化配置

## ✨ 核心特性

### 🧠 **智能任务调度系统**
- **资源预估**: 基于模型参数和数据量的科学显存预估
- **任务调度**: 支持优先级队列和并发控制的智能调度
- **进程管理**: 安全的进程启动、监控和异常处理
- **系统监控**: 实时CPU、内存、GPU资源监控

### 📊 **Original级别评估**
- **样本级评估**: 传统的单样本预测评估
- **Original级评估**: 同一原始样本的多个子样本预测平均值评估
- **双重指标**: 提供更准确的模型性能评估

### 🎲 **多策略数据采样**
- **随机采样**: 完全随机选择样本
- **分层采样**: 按类别比例采样
- **平衡采样**: 确保各类别样本均衡
- **跨子集采样**: 支持训练集、验证集、测试集的灵活采样

### 🔧 **灵活配置系统**
- **YAML配置**: 人性化的配置文件格式
- **多配置支持**: 支持多个训练配置并行管理
- **动态启用**: 通过enable字段控制配置使用
- **参数验证**: 自动验证配置参数的有效性

## 📁 项目结构

```
SeedVision_v1/
├── README.md                           # 项目主文档
├── main.py                            # 传统训练入口
├── main_scheduler.py                  # 智能调度训练入口
│
├── docs/                              # 📚 项目文档
│   ├── README.md                      # 文档索引
│   ├── design.md                      # 系统设计文档
│   ├── OPTIMIZATION_SUMMARY.md        # 系统优化总结
│   ├── ORIGINAL_LEVEL_EVALUATION.md   # Original级别评估详解
│   ├── SAMPLING_STRATEGY_REFACTOR.md  # 采样策略重构文档
│   └── SCHEDULER_SUMMARY.md           # 调度器功能总结
│
├── config/                            # ⚙️ 配置管理
│   ├── config_loader.py               # 配置加载器
│   ├── training_config.yaml           # 训练配置文件
│   ├── CONFIG_GUIDE.md               # 配置使用指南
│   └── SAMPLING_STRATEGIES.md        # 采样策略文档
│
├── models/                            # 🧠 模型定义
│   ├── FasterNet.py                   # FasterNet模型实现
│   ├── Mixed_YOLO_FasterNet.py        # YOLO+FasterNet混合模型
│   ├── Simple_Mixed.py                # 简化混合模型
│   ├── fasternet_blocks.py            # FasterNet基础模块
│   └── model_utils.py                 # 模型工具函数
│
├── tools/                             # 🔧 工具模块
│   ├── data/                          # 数据处理工具
│   │   ├── load_data.py               # 数据加载
│   │   ├── data_process1.py           # 数据预处理
│   │   ├── data_process2.py           # 数据后处理
│   │   └── redistribute_data.py       # 数据重分布
│   ├── training/                      # 训练相关工具
│   │   ├── train.py                   # 训练核心逻辑
│   │   ├── validate.py                # 验证和评估
│   │   └── visualize.py               # 结果可视化
│   └── analysis/                      # 分析工具
│       ├── analyze_original_distribution.py
│       └── visualization_results/     # 可视化结果
│
├── scheduler/                         # 📋 智能调度系统
│   ├── resource_estimator.py          # 资源预估器
│   ├── task_scheduler.py              # 任务调度器
│   ├── process_manager.py             # 进程管理器
│   ├── README.md                      # 调度器详细文档
│   └── example_configs.json           # 示例配置
│
├── tests/                             # 🧪 测试系统
│   ├── unit/                          # 单元测试
│   ├── integration/                   # 集成测试
│   ├── examples/                      # 示例测试
│   └── debug/                         # 调试脚本
│
├── output/                            # 📊 输出目录
│   ├── models/                        # 训练模型
│   ├── results/                       # 训练结果
│   ├── logs/                          # 日志文件
│   └── visualizations/                # 可视化结果
│
├── runners/                           # 📜 执行脚本
│   ├── training/                      # 训练脚本
│   │   ├── main.py                    # 主训练入口
│   │   └── main_scheduler.py          # 调度器入口
│   └── utilities/                     # 工具脚本
│       ├── organize_project.py        # 项目整理脚本
│       ├── cleanup_logs.py            # 日志清理脚本
│       └── model_loader.py            # 模型加载器
│
├── train.py                           # 便捷训练启动脚本
├── train_scheduler.py                 # 便捷调度器启动脚本
└── quick_test.py                      # 便捷测试启动脚本
```

## 🚀 快速开始

### 1. 环境验证
```bash
# 快速验证系统功能 (2-3分钟)
python quick_test.py quick

# 完整功能测试 (15-30分钟)
python quick_test.py full
```

### 2. 资源预估
```bash
# 预估训练资源需求
python train_scheduler.py --mode estimate
```

### 3. 开始训练

#### 便捷训练方式 (推荐)
```bash
# 基础训练
python train.py

# 智能调度训练 (推荐)
python train_scheduler.py
```

#### 直接调用方式
```bash
# 传统训练
python runners/training/main.py --sequential --max_memory 8.0

# 智能调度训练
python runners/training/main_scheduler.py --mode schedule --max_memory 8.0 --max_concurrent 2
```

## 📊 功能详解

### 🎯 **智能调度系统**

#### 资源预估
- **显存预估**: 基于模型参数、输入尺寸、批次大小的科学计算
- **时间预估**: 根据数据量、模型复杂度预估训练时间
- **可行性检查**: 判断任务是否可在当前资源下运行

#### 任务调度
- **优先级队列**: URGENT > HIGH > NORMAL > LOW
- **并发控制**: 可配置最大并发任务数
- **动态调度**: 根据资源可用性动态调整执行

#### 进程管理
- **安全启动**: 独立进程运行，避免相互影响
- **实时监控**: 监控进程CPU、内存、GPU使用
- **异常处理**: 自动检测和处理进程异常

### 📈 **Original级别评估**

传统评估只考虑单个样本的预测准确性，而Original级别评估考虑同一原始样本的多个子样本预测的平均准确性，更符合实际应用场景。

```python
# 样本级评估 (传统)
sample_r2 = calculate_r2(predictions, labels)

# Original级评估 (新增)
original_metrics, original_results = evaluate_original_level(
    predictions, labels, original_names
)
```

### 🎲 **多策略数据采样**

支持多种采样策略，满足不同训练需求：

```yaml
sampling_strategy_config:
  strategy_type: "random"  # random, stratified, balanced
  parameters:
    sample_size: 2400
    seed: 42
    balance_ratio: 0.8  # 仅balanced策略使用
```

## ⚙️ 配置管理

### 配置文件结构
```yaml
training_configs:
  - name: "original_balanced_224x224"
    enable: true
    description: "Original级别平衡采样，224x224输入"

    model:
      embed_dim: 192
      depths: [3, 4, 18, 3]
      mlp_ratio: 2.0
      n_div: 4
      drop_path_rate: 0.3

    resources:
      batch_size: 40

    transform_config: "224x224_norm"

    training:
      num_epochs: 100
      learning_rate: 0.001

    sampling_strategy_config:
      strategy_type: "balanced"
      parameters:
        sample_size: 2400
        balance_ratio: 0.8
```

### 配置使用
```python
from config.config_loader import ConfigLoader

# 加载配置
config_loader = ConfigLoader()
configs = config_loader.get_enabled_training_configs()

# 获取特定配置
config = config_loader.get_config_by_name("original_balanced_224x224")
```

## 🧪 测试系统

### 测试层次
- **quick**: 快速测试 (2-3分钟) - 基础功能验证
- **basic**: 基础测试 (5-10分钟) - 核心功能验证
- **training**: 训练测试 (10-15分钟) - 训练功能验证
- **scheduler**: 调度器测试 (3-5分钟) - 调度功能验证
- **full**: 完整测试 (15-30分钟) - 所有功能验证

### 使用方式
```bash
# 便捷测试方式 (推荐)
python quick_test.py quick
python quick_test.py full

# 直接调用方式
python tests/runners/run_tests.py quick
python tests/runners/run_tests.py training
python tests/runners/run_tests.py full
```

## 📈 性能特点

### 资源预估准确性
- **显存预估误差**: < 10%
- **时间预估误差**: < 20%
- **系统兼容性**: Windows/Linux通用

### 调度效率
- **任务响应时间**: < 1秒
- **资源利用率**: > 90%
- **并发稳定性**: 支持4个并发任务

### 评估精度提升
- **Original级评估**: 提供更准确的模型性能评估
- **双重指标**: 样本级 + Original级评估
- **实际应用**: 更符合实际使用场景

## 📚 文档导航

- **[项目状态](docs/PROJECT_STATUS.md)** - 项目整体状态和功能总结
- **[系统设计](docs/design.md)** - 整体架构和设计理念
- **[配置指南](config/CONFIG_GUIDE.md)** - 详细配置说明
- **[调度器文档](scheduler/README.md)** - 调度系统使用指南
- **[测试指南](docs/TESTING_GUIDE.md)** - 完整测试说明
- **[采样策略](config/SAMPLING_STRATEGIES.md)** - 数据采样策略详解
- **[文档索引](docs/README.md)** - 完整文档目录

## 🎯 版本信息

- **当前版本**: v1.0.0
- **发布日期**: 2024年
- **Python版本**: >= 3.8
- **PyTorch版本**: >= 1.9.0

## 🚀 未来规划

- [ ] 支持分布式训练调度
- [ ] 添加自动超参数优化
- [ ] 集成模型性能预测
- [ ] 支持云端资源调度
- [ ] 添加Web管理界面

---

**SeedVision v1 - 让深度学习训练更智能、更高效！**
