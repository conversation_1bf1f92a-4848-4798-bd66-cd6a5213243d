# SeedVision v1 - Original级别评估功能实现

## 📋 功能概述

基于用户需求："同一个original下的所有样本的数据标签是一样的，都代表这个original的平均值，我现在要你每个epoch完了评估的时候还要对当前采样下同一个original的记录的预测值做一个均值再和数据标签比对"

我们实现了Original级别评估功能，在每个epoch结束后提供两种评估指标：
1. **样本级别评估**：每个样本的预测值 vs 标签值
2. **Original级别评估**：同一original下所有样本预测值的均值 vs 该original的标签值

## 🎯 核心实现

### 1. **数据集修改** - `SeedDataset`

<antml_code_snippet path="models/SeedVision_v1/tools/myscripts/train.py" mode="EXCERPT">
```python
def __getitem__(self, idx):
    # 处理新的数据格式，支持字典和元组格式
    data_item = self.data_list[idx]
    original_name = 'unknown'  # 默认值

    # 处理字典格式的数据（跨子集采样返回的格式）
    if isinstance(data_item, dict):
        image_path = data_item['path']
        oil = float(data_item['oil'])
        protein = float(data_item['protein'])
        original_name = data_item.get('original_image', 'unknown')
    
    # 处理元组格式的数据（传统格式）
    elif isinstance(data_item, (tuple, list)):
        if len(data_item) == 4:  # (image, oil, protein, original_image)
            image, oil, protein, original_name = data_item
        elif len(data_item) == 3:  # (image, oil, protein) - 兼容旧格式
            image, oil, protein = data_item

    # 应用转换
    if self.transform:
        image = self.transform(image)

    # 创建标签张量 [油含量, 蛋白质含量]
    label = torch.tensor([oil, protein], dtype=torch.float32)

    return image, label, original_name  # 新增original_name返回
```
</antml_code_snippet>

**关键改进**：
- 数据集现在返回 `(image, label, original_name)` 三元组
- 支持字典和元组两种数据格式
- 保留original信息用于后续评估

### 2. **Original级别评估函数** - `validate.py`

<antml_code_snippet path="models/SeedVision_v1/tools/myscripts/validate.py" mode="EXCERPT">
```python
def evaluate_original_level(predictions, labels, original_names):
    """
    Original级别评估：对同一original下的所有样本预测值取均值，然后与标签比较
    
    参数:
        predictions: 预测值数组 (N, 2) - [oil, protein]
        labels: 标签值数组 (N, 2) - [oil, protein]
        original_names: original名称列表 (N,)
    
    返回:
        original_metrics: Original级别评估指标
        original_results: 每个original的详细结果
    """
    from collections import defaultdict
    
    # 按original分组
    original_groups = defaultdict(list)
    for i, original_name in enumerate(original_names):
        original_groups[original_name].append({
            'prediction': predictions[i],
            'label': labels[i],
            'index': i
        })
    
    # 计算每个original的均值预测和标签
    original_predictions = []
    original_labels = []
    original_results = {}
    
    for original_name, items in original_groups.items():
        # 提取该original的所有预测值和标签
        orig_preds = np.array([item['prediction'] for item in items])
        orig_labels = np.array([item['label'] for item in items])
        
        # 计算预测值的均值
        mean_pred = np.mean(orig_preds, axis=0)
        # 标签应该都相同，取第一个即可
        label = orig_labels[0]
        
        original_predictions.append(mean_pred)
        original_labels.append(label)
        
        # 保存详细结果
        original_results[original_name] = {
            'sample_count': len(items),
            'predictions': orig_preds,
            'labels': orig_labels,
            'mean_prediction': mean_pred,
            'label': label,
            'oil_std': np.std(orig_preds[:, 0]),  # 油含量预测的标准差
            'protein_std': np.std(orig_preds[:, 1])  # 蛋白质含量预测的标准差
        }
    
    # 转换为numpy数组
    original_predictions = np.array(original_predictions)
    original_labels = np.array(original_labels)
    
    # 计算Original级别的评估指标
    original_metrics = {
        'oil': {
            'R2': calculate_r2(original_labels_tensor[:, 0], original_preds_tensor[:, 0]),
            'RMSE': calculate_rmse(original_labels_tensor[:, 0], original_preds_tensor[:, 0]),
            'MAE': calculate_mae(original_labels_tensor[:, 0], original_preds_tensor[:, 0]),
            'RPD': calculate_rpd(original_labels_tensor[:, 0], original_preds_tensor[:, 0])
        },
        'protein': {
            'R2': calculate_r2(original_labels_tensor[:, 1], original_preds_tensor[:, 1]),
            'RMSE': calculate_rmse(original_labels_tensor[:, 1], original_preds_tensor[:, 1]),
            'MAE': calculate_mae(original_labels_tensor[:, 1], original_preds_tensor[:, 1]),
            'RPD': calculate_rpd(original_labels_tensor[:, 1], original_preds_tensor[:, 1])
        }
    }
    
    return original_metrics, original_results
```
</antml_code_snippet>

**核心逻辑**：
1. 按original_name分组所有预测值和标签
2. 计算每个original下所有样本预测值的均值
3. 使用均值预测与original标签计算R²、RMSE、MAE、RPD指标
4. 返回详细的每个original结果和整体指标

### 3. **验证函数更新** - `validate()`

<antml_code_snippet path="models/SeedVision_v1/tools/myscripts/train.py" mode="EXCERPT">
```python
def validate(model, val_loader, criterion, device, enable_original_eval=True):
    """
    在验证集上评估模型，支持样本级别和Original级别评估
    """
    model.eval()
    total_loss = 0.0
    all_preds = []
    all_labels = []
    all_original_names = []
    
    with torch.no_grad():
        for batch_data in tqdm(val_loader, desc="Validation"):
            if len(batch_data) == 3:  # 新格式：(inputs, targets, original_names)
                inputs, targets, original_names = batch_data
                all_original_names.extend(original_names)
            else:  # 旧格式：(inputs, targets)
                inputs, targets = batch_data
                # 如果没有original信息，使用索引作为标识
                batch_size = inputs.size(0)
                original_names = [f"sample_{total_samples + i}" for i in range(batch_size)]
                all_original_names.extend(original_names)
                
            # ... 模型推理和损失计算 ...
    
    # 计算样本级别评估指标
    sample_metrics = {
        'oil': {'R2': ..., 'RMSE': ..., 'MAE': ..., 'RPD': ...},
        'protein': {'R2': ..., 'RMSE': ..., 'MAE': ..., 'RPD': ...}
    }
    
    # 构建最终的metrics字典
    metrics = {'sample_level': sample_metrics}

    # 如果启用Original级别评估且有original信息
    if enable_original_eval and len(set(all_original_names)) > 1:
        try:
            from tools.myscripts.validate import evaluate_original_level
            
            # 进行Original级别评估
            original_metrics, original_results = evaluate_original_level(
                all_preds.numpy(), 
                all_labels.numpy(), 
                all_original_names
            )
            
            metrics['original_level'] = original_metrics
            metrics['original_results'] = original_results
            
            print(f"📊 Original-level evaluation completed:")
            print(f"  - Total originals: {len(original_results)}")
            print(f"  - Oil R²: Sample={sample_metrics['oil']['R2']:.4f}, Original={original_metrics['oil']['R2']:.4f}")
            print(f"  - Protein R²: Sample={sample_metrics['protein']['R2']:.4f}, Original={original_metrics['protein']['R2']:.4f}")
            
        except Exception as e:
            print(f"⚠️  Warning: Original-level evaluation failed: {e}")
            print("Continuing with sample-level evaluation only...")

    return val_loss, metrics, all_preds.numpy(), all_labels.numpy()
```
</antml_code_snippet>

### 4. **训练输出显示**

<antml_code_snippet path="models/SeedVision_v1/tools/myscripts/train.py" mode="EXCERPT">
```python
# 获取验证指标（支持新旧格式）
if 'sample_level' in val_metrics:
    val_sample_metrics = val_metrics['sample_level']
    print(f"📊 Sample-level R²:")
    print(f"  Oil: Training={train_metrics['oil']['R2']:.4f}, Validation={val_sample_metrics['oil']['R2']:.4f}")
    print(f"  Protein: Training={train_metrics['protein']['R2']:.4f}, Validation={val_sample_metrics['protein']['R2']:.4f}")
    
    # 如果有Original级别评估结果，也显示
    if 'original_level' in val_metrics:
        val_original_metrics = val_metrics['original_level']
        print(f"🎯 Original-level R²:")
        print(f"  Oil: {val_original_metrics['oil']['R2']:.4f}")
        print(f"  Protein: {val_original_metrics['protein']['R2']:.4f}")
```
</antml_code_snippet>

## 📊 输出示例

训练过程中每个epoch结束后会显示：

```
Epoch 1/100 - Task: original_balanced_224x224 - GPU: 2.1GB/8.0GB
Training Loss: 0.0234, Validation Loss: 0.0198 - GPU After: 2.3GB/8.0GB

📊 Sample-level R²:
  Oil: Training=0.8234, Validation=0.7891
  Protein: Training=0.8567, Validation=0.8123

🎯 Original-level R²:
  Oil: 0.8456
  Protein: 0.8789

📊 Original-level evaluation completed:
  - Total originals: 15
  - Oil R²: Sample=0.7891, Original=0.8456
  - Protein R²: Sample=0.8123, Original=0.8789
```

## 🎯 关键特性

### 1. **双重评估体系**
- **样本级别**：传统的每个样本预测vs标签评估
- **Original级别**：同一original下样本预测均值vs标签评估

### 2. **自动检测和处理**
- 自动检测是否有足够的original信息
- 向后兼容旧的数据格式
- 错误处理和降级策略

### 3. **详细结果记录**
- 每个original的样本数量
- 预测值的标准差（衡量一致性）
- 完整的评估指标（R²、RMSE、MAE、RPD）

### 4. **可视化支持**
- R²曲线图包含样本级别和Original级别两条线
- 历史记录保存两种评估结果

## ✅ 验证结果

通过测试验证：
- ✅ Original级别评估函数正确计算均值
- ✅ 评估指标计算准确
- ✅ 训练过程中正确显示两种R²值
- ✅ 可视化图表包含两条评估曲线
- ✅ 向后兼容性良好

## 🚀 使用方法

1. **运行训练**：
   ```bash
   python main.py --sequential --max_memory 8.0
   ```

2. **查看输出**：
   - 每个epoch会显示样本级别和Original级别的R²值
   - 训练结束后生成包含两条曲线的R²图表

3. **结果解读**：
   - **样本级别R²**：传统评估，反映模型对单个样本的预测能力
   - **Original级别R²**：更符合实际应用，反映模型对original平均值的预测能力
   - 通常Original级别R²会更高，因为均值化减少了噪声

## 💡 意义和价值

1. **更准确的评估**：Original级别评估更符合实际应用场景
2. **噪声减少**：通过均值化减少单个样本的随机误差
3. **一致性检验**：可以观察同一original下预测的一致性
4. **模型优化指导**：提供更可靠的模型性能指标

这个实现完全满足了用户的需求，提供了科学、准确、实用的Original级别评估功能。
