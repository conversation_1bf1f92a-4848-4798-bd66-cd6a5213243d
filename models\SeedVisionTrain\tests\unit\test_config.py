#!/usr/bin/env python3
"""
测试配置加载器的脚本
验证新的可配置图片数量功能是否正常工作
"""

import sys
import os

# 添加项目根目录到路径
# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from config.config_loader import Config<PERSON>oader

def test_config_loading():
    """测试配置加载功能"""
    print("🧪 Testing Configuration Loading...")
    print("="*60)
    
    try:
        # 加载配置
        config_loader = ConfigLoader()
        
        # 打印配置摘要
        config_loader.print_config_summary()
        
        # 获取启用的配置
        enabled_configs = config_loader.get_enabled_training_configs()
        
        print(f"\n🔍 Detailed Analysis of Enabled Configurations:")
        print("="*60)
        
        for i, config in enumerate(enabled_configs, 1):
            print(f"\n📋 Configuration {i}: {config['name']}")
            print(f"   Description: {config.get('description', 'N/A')}")
            
            # 训练配置
            training_config = config.get('training_config', {})
            print(f"   Training epochs: {training_config.get('num_epochs', 'N/A')}")
            
            # Original采样配置
            original_sampling = config.get('original_sampling_config', {})
            if original_sampling:
                print(f"   Original sampling:")
                print(f"     - Target originals: {original_sampling.get('target_originals', 'N/A')}")
                print(f"     - Samples per original: {original_sampling.get('samples_per_original', 'N/A')}")
                print(f"     - Total samples: {original_sampling.get('total_samples', 'N/A')}")
            
            # 资源配置
            resources = config.get('resources', {})
            print(f"   Resources:")
            print(f"     - Batch size: {resources.get('batch_size', 'N/A')}")
            print(f"     - Estimated memory: {resources.get('estimated_memory', 'N/A')}GB")
            
            # 变换配置
            transform_data = config.get('transform_config_data', {})
            if transform_data:
                input_size = transform_data.get('input_size', [0, 0])
                normalize = transform_data.get('normalize', False)
                print(f"   Transform:")
                print(f"     - Input size: {input_size[0]}x{input_size[1]}")
                print(f"     - Normalization: {'Yes' if normalize else 'No'}")
        
        print(f"\n✅ Configuration loading test completed successfully!")
        print(f"   Found {len(enabled_configs)} enabled configuration(s)")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_original_sampling_variations():
    """测试不同的original采样配置"""
    print(f"\n🎯 Testing Original Sampling Variations...")
    print("="*60)
    
    # 测试不同的采样配置
    test_configs = [
        {
            'name': 'Small Scale',
            'target_originals': 20,
            'samples_per_original': 30,
            'total_samples': 600
        },
        {
            'name': 'Medium Scale',
            'target_originals': 40,
            'samples_per_original': 60,
            'total_samples': 2400
        },
        {
            'name': 'Large Scale',
            'target_originals': 60,
            'samples_per_original': 80,
            'total_samples': 4800
        }
    ]
    
    for config in test_configs:
        print(f"\n📊 {config['name']} Configuration:")
        print(f"   - Target originals: {config['target_originals']}")
        print(f"   - Samples per original: {config['samples_per_original']}")
        print(f"   - Total samples: {config['total_samples']:,}")
        
        # 计算预期的batch数量（假设每个batch从每个original取1张）
        expected_batches = config['samples_per_original']
        print(f"   - Expected batches: {expected_batches}")
        print(f"   - Samples per batch: {config['target_originals']}")

if __name__ == "__main__":
    print("🚀 Starting Configuration Tests...")
    
    # 测试配置加载
    success = test_config_loading()
    
    if success:
        # 测试original采样变化
        test_original_sampling_variations()
        
        print(f"\n🎉 All tests completed!")
        print(f"\n💡 Usage Tips:")
        print(f"   1. 修改 training_config.yaml 中的 original_sampling 配置来调整图片数量")
        print(f"   2. 设置 enable: true 来启用特定的训练配置")
        print(f"   3. 每个配置可以有不同的 target_originals 和 samples_per_original")
        print(f"   4. batch_size 应该等于 target_originals 以确保每个batch从每个original取1张")
    else:
        print(f"\n❌ Tests failed. Please check the configuration file.")
        sys.exit(1)
