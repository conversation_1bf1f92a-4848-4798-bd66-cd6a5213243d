#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新组织scripts目录，避免命名冲突
"""

import os
import shutil

def reorganize_scripts_directory():
    """重新组织scripts目录"""
    print("🔧 Reorganizing scripts directory to avoid conflicts...")
    
    if not os.path.exists("scripts"):
        print("   ⚠️  scripts directory not found")
        return False
    
    new_dir_name = "runners"
    
    try:
        if os.path.exists(new_dir_name):
            print(f"   ⚠️  {new_dir_name} directory already exists, removing it first")
            shutil.rmtree(new_dir_name)
        
        shutil.move("scripts", new_dir_name)
        print(f"   ✅ Renamed 'scripts' to '{new_dir_name}'")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to reorganize: {e}")
        return False

def create_convenience_scripts():
    """创建便捷启动脚本"""
    print("\n📜 Creating convenience launcher scripts...")
    
    convenience_scripts = {
        "train.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""便捷训练启动脚本"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    from runners.training.main import main
    main()
''',
        "train_scheduler.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""便捷调度器启动脚本"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    from runners.training.main_scheduler import main
    main()
''',
        "quick_test.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""便捷测试启动脚本"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    from tests.scripts.run_tests import main
    main()
'''
    }
    
    for script_name, script_content in convenience_scripts.items():
        try:
            with open(script_name, 'w', encoding='utf-8') as f:
                f.write(script_content)
            print(f"   ✅ Created {script_name}")
        except Exception as e:
            print(f"   ⚠️  Failed to create {script_name}: {e}")

def main():
    """主函数"""
    print("🔧 SeedVision v1 - Scripts Directory Reorganization")
    print("=" * 50)
    
    success = reorganize_scripts_directory()
    
    if success:
        create_convenience_scripts()
        
        print("\n🎉 Scripts directory reorganization completed!")
        print("📋 New usage:")
        print("   python train.py                    # 基础训练")
        print("   python train_scheduler.py          # 调度器训练") 
        print("   python quick_test.py               # 运行测试")
        
    else:
        print("\n❌ Scripts directory reorganization failed!")

if __name__ == "__main__":
    main()
