'''
数据可视化
评估指标
决定系数（R2）：衡量预测值与真实值的拟合程度（越接近 1 越好）。
均方根误差（RMSE）：反映预测误差的标准差（值越小越好）。
平均绝对误差（MAE）：计算预测值与真实值误差的绝对值均值（值越小越好）。
剩余预测偏差（RPD）：评估模型预测准确性（RPD>2.5 表示良好）。
损失函数
MSE：均方误差，用于回归任务。
'''

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import os
import seaborn as sns
import torch
from .validate import calculate_r2, calculate_rmse, calculate_mae, calculate_rpd

def find_best_text_position(x_data, y_data, text_width_ratio=0.25, text_height_ratio=0.15):
    """
    智能寻找文本框的最佳位置，避免与数据点和图表元素重叠

    参数:
        x_data: x轴数据数组
        y_data: y轴数据数组
        text_width_ratio: 文本框宽度占图表宽度的比例
        text_height_ratio: 文本框高度占图表高度的比例

    返回:
        best_position: 最佳位置的相对坐标 (x, y)
        corner_name: 位置名称
    """
    # 定义更多候选位置，避免重叠
    corners = {
        'top_left': (0.02, 0.98),
        'top_center': (0.35, 0.98),
        'top_right': (0.72, 0.98),
        'middle_left': (0.02, 0.65),
        'middle_right': (0.72, 0.65),
        'bottom_left': (0.02, 0.35),
        'bottom_center': (0.35, 0.35),
        'bottom_right': (0.72, 0.35),
        'lower_left': (0.02, 0.02),
        'lower_right': (0.72, 0.02)
    }

    # 数据范围
    x_min, x_max = np.min(x_data), np.max(x_data)
    y_min, y_max = np.min(y_data), np.max(y_data)
    x_range = x_max - x_min if x_max != x_min else 1
    y_range = y_max - y_min if y_max != y_min else 1

    # 计算每个位置的得分 (越低越好)
    position_scores = {}

    for corner_name, (rel_x, rel_y) in corners.items():
        # 将相对坐标转换为数据坐标
        corner_x = x_min + rel_x * x_range
        corner_y = y_min + rel_y * y_range

        # 定义文本框区域
        text_width = x_range * text_width_ratio
        text_height = y_range * text_height_ratio

        # 文本框边界
        text_x_min = corner_x - text_width/2 if 'center' in corner_name else corner_x
        text_x_max = text_x_min + text_width
        text_y_min = corner_y - text_height if 'top' in corner_name or 'middle' in corner_name else corner_y
        text_y_max = text_y_min + text_height

        # 计算得分
        score = 0

        # 1. 数据点重叠惩罚
        for x, y in zip(x_data, y_data):
            if text_x_min <= x <= text_x_max and text_y_min <= y <= text_y_max:
                score += 10  # 重叠惩罚
            elif (text_x_min - text_width*0.1 <= x <= text_x_max + text_width*0.1 and
                  text_y_min - text_height*0.1 <= y <= text_y_max + text_height*0.1):
                score += 3   # 接近惩罚

        # 2. 边界惩罚 (避免文本框超出图表边界)
        if rel_x < 0.05 or rel_x > 0.95:
            score += 5
        if rel_y < 0.05 or rel_y > 0.95:
            score += 5

        # 3. 对角线区域惩罚 (避免与理想预测线重叠)
        diagonal_distance = abs((corner_x - x_min) - (corner_y - y_min))
        if diagonal_distance < min(x_range, y_range) * 0.1:
            score += 8

        # 4. 优先级调整 (某些位置更优)
        if 'top_right' in corner_name:
            score -= 2  # 传统上右上角较好
        elif 'bottom_left' in corner_name:
            score -= 1

        position_scores[corner_name] = score

    # 选择得分最低的位置
    best_corner = min(position_scores, key=position_scores.get)
    best_position = corners[best_corner]

    return best_position, best_corner

def setup_matplotlib():
    """设置matplotlib配置"""
    import warnings
    import matplotlib

    # 设置后端为Agg，避免显示图形窗口
    matplotlib.use('Agg')

    # 禁用所有matplotlib相关警告
    warnings.filterwarnings('ignore')

    # 设置使用英文字体，避免中文字体问题
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False

    print("Matplotlib setup completed with English fonts")

# 初始化matplotlib设置
setup_matplotlib()

def plot_loss(losses, title='Loss'):
    """
    绘制训练损失曲线
    """

    plt.figure(figsize=(10, 6))
    plt.plot(losses, label='Loss')
    plt.title(title)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.show()

def plot_R2(trainR2, testR2, title='R2'):
    """
    绘制R2曲线
    """
    plt.figure(figsize=(10, 6))
    plt.plot(trainR2, label='Train R2')
    plt.plot(testR2, label='Test R2')
    plt.title(title)
    plt.xlabel('Epoch')
    plt.ylabel('R2')
    plt.legend()
    plt.show()

def plot_RMSE(trainRMSE, testRMSE, title='RMSE'):
    """
    绘制RMSE曲线
    """
    plt.figure(figsize=(10, 6))
    plt.plot(trainRMSE, label='Train RMSE')
    plt.plot(testRMSE, label='Test RMSE')
    plt.title(title)
    plt.xlabel('Epoch')
    plt.ylabel('RMSE')
    plt.legend()
    plt.show()

def plot_MAE(trainMAE, testMAE, title='MAE'):
    """
    绘制MAE曲线
    """
    plt.figure(figsize=(10, 6))
    plt.plot(trainMAE, label='Train MAE')
    plt.plot(testMAE, label='Test MAE')
    plt.title(title)
    plt.xlabel('Epoch')
    plt.ylabel('MAE')
    plt.legend()
    plt.show()

def plot_RPD(trainRPD, testRPD, title='RPD'):
    """
    绘制RPD曲线
    """
    plt.figure(figsize=(10, 6))
    plt.plot(trainRPD, label='Train RPD')
    plt.plot(testRPD, label='Test RPD')
    plt.title(title)
    plt.xlabel('Epoch')
    plt.ylabel('RPD')
    plt.legend()
    plt.show()

def plot_prediction(y_true, y_pred, title='Prediction', save_path=None):
    """
    绘制真实值与预测值对比图

    参数:
        y_true: 真实值数组
        y_pred: 预测值数组
        title: 图表标题
        save_path: 保存图片的路径，如果为None则显示图片不保存
    """
    # 转换为PyTorch张量以便使用validate中的函数
    if not isinstance(y_true, torch.Tensor):
        y_true = torch.tensor(y_true, dtype=torch.float32)
    if not isinstance(y_pred, torch.Tensor):
        y_pred = torch.tensor(y_pred, dtype=torch.float32)

    # 计算评估指标
    r2 = calculate_r2(y_true, y_pred)
    rmse = calculate_rmse(y_true, y_pred)
    mae = calculate_mae(y_true, y_pred)

    # 转回numpy数组用于绘图
    y_true_np = y_true.numpy() if isinstance(y_true, torch.Tensor) else y_true
    y_pred_np = y_pred.numpy() if isinstance(y_pred, torch.Tensor) else y_pred

    plt.figure(figsize=(10, 6))
    plt.scatter(y_true_np, y_pred_np, alpha=0.5)
    plt.plot([min(y_true_np), max(y_true_np)], [min(y_true_np), max(y_true_np)], color='red', linestyle='--')

    # 添加评估指标文本
    plt.annotate(f'R² = {r2:.4f}\nRMSE = {rmse:.4f}\nMAE = {mae:.4f}',
                 xy=(0.05, 0.95), xycoords='axes fraction',
                 bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

    plt.title(title)
    plt.xlabel('True Values')
    plt.ylabel('Predicted Values')
    plt.grid(True, linestyle='--', alpha=0.7)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")

    plt.close()  # 关闭图形以释放内存

def plot_train_val_loss(train_losses, val_losses, title='Training and Validation Loss', save_path=None):
    """
    绘制训练和验证损失曲线

    参数:
        train_losses: 训练损失列表
        val_losses: 验证损失列表
        title: 图表标题
        save_path: 保存图片的路径，如果为None则显示图片不保存
    """
    epochs = range(1, len(train_losses) + 1)

    plt.figure(figsize=(12, 7))
    plt.plot(epochs, train_losses, 'b-', label='Training Loss')
    plt.plot(epochs, val_losses, 'r-', label='Validation Loss')

    plt.title(title, fontsize=16)
    plt.xlabel('Epoch', fontsize=14)
    plt.ylabel('Loss', fontsize=14)
    plt.legend(fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)

    # 添加最终损失值标注
    plt.annotate(f'Final Training Loss: {train_losses[-1]:.4f}',
                 xy=(0.05, 0.15), xycoords='axes fraction',
                 bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))
    plt.annotate(f'Final Validation Loss: {val_losses[-1]:.4f}',
                 xy=(0.05, 0.05), xycoords='axes fraction',
                 bbox=dict(boxstyle="round,pad=0.3", fc="white", ec="gray", alpha=0.8))

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")

    plt.close()  # 关闭图形以释放内存

def plot_protein_regression(y_true, y_pred, title='Protein Content Prediction Results', save_path=None):
    """
    绘制蛋白质含量预测值与真实值的散点分布图

    参数:
        y_true: 真实蛋白质含量数组
        y_pred: 预测蛋白质含量数组
        title: 图表标题
        save_path: 保存图片的路径，如果为None则显示图片不保存
    """
    # 转换为PyTorch张量以便使用validate中的函数
    if not isinstance(y_true, torch.Tensor):
        y_true = torch.tensor(y_true, dtype=torch.float32)
    if not isinstance(y_pred, torch.Tensor):
        y_pred = torch.tensor(y_pred, dtype=torch.float32)

    # 计算评估指标
    r2 = calculate_r2(y_true, y_pred)
    rmse = calculate_rmse(y_true, y_pred)
    mae = calculate_mae(y_true, y_pred)
    rpd = calculate_rpd(y_true, y_pred)

    # 转回numpy数组用于绘图
    y_true_np = y_true.numpy() if isinstance(y_true, torch.Tensor) else y_true
    y_pred_np = y_pred.numpy() if isinstance(y_pred, torch.Tensor) else y_pred

    # 创建数据框用于回归图
    df = pd.DataFrame({'True Values': y_true_np, 'Predicted Values': y_pred_np})

    # 使用seaborn绘制回归图
    plt.figure(figsize=(10, 8))
    sns.set_style("whitegrid")

    # 绘制散点图和回归线
    sns.regplot(x='True Values', y='Predicted Values', data=df,
               scatter_kws={'alpha':0.6, 's':50, 'color':'blue'},
               line_kws={'color':'red', 'linestyle':'--'})

    # 添加对角线（理想预测线）
    min_val = min(min(y_true_np), min(y_pred_np))
    max_val = max(max(y_true_np), max(y_pred_np))
    plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5, label='Perfect Prediction')

    # 固定文本框位置到左上角，稍微往下一点避免与图例重叠
    text_position = (0.02, 0.9)

    # 固定对齐方式为左上角
    va_align = 'top'
    ha_align = 'left'

    # 创建更美观的文本框
    text_content = f'R² = {r2:.4f}\nRMSE = {rmse:.4f}\nMAE = {mae:.4f}\nRPD = {rpd:.4f}'

    # 添加评估指标文本框，使用改进的样式
    plt.annotate(text_content,
                 xy=text_position, xycoords='axes fraction',
                 bbox=dict(boxstyle="round,pad=0.5",
                          facecolor="white",
                          edgecolor="blue",
                          alpha=0.95,
                          linewidth=1.5),
                 fontsize=11,
                 ha=ha_align,
                 va=va_align,
                 weight='bold',
                 color='darkblue')

    plt.title(title, fontsize=16)
    plt.xlabel('True Protein Content (%)', fontsize=14)
    plt.ylabel('Predicted Protein Content (%)', fontsize=14)
    plt.legend(fontsize=12)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")

    plt.close()  # 关闭图形以释放内存

def plot_oil_regression(y_true, y_pred, title='Oil Content Prediction Results', save_path=None):
    """
    绘制含油量预测值与真实值的散点分布图

    参数:
        y_true: 真实含油量数组
        y_pred: 预测含油量数组
        title: 图表标题
        save_path: 保存图片的路径，如果为None则显示图片不保存
    """
    # 转换为PyTorch张量以便使用validate中的函数
    if not isinstance(y_true, torch.Tensor):
        y_true = torch.tensor(y_true, dtype=torch.float32)
    if not isinstance(y_pred, torch.Tensor):
        y_pred = torch.tensor(y_pred, dtype=torch.float32)

    # 计算评估指标
    r2 = calculate_r2(y_true, y_pred)
    rmse = calculate_rmse(y_true, y_pred)
    mae = calculate_mae(y_true, y_pred)
    rpd = calculate_rpd(y_true, y_pred)

    # 转回numpy数组用于绘图
    y_true_np = y_true.numpy() if isinstance(y_true, torch.Tensor) else y_true
    y_pred_np = y_pred.numpy() if isinstance(y_pred, torch.Tensor) else y_pred

    # 创建数据框用于回归图
    df = pd.DataFrame({'True Values': y_true_np, 'Predicted Values': y_pred_np})

    # 使用seaborn绘制回归图
    plt.figure(figsize=(10, 8))
    sns.set_style("whitegrid")

    # 绘制散点图和回归线
    sns.regplot(x='True Values', y='Predicted Values', data=df,
               scatter_kws={'alpha':0.6, 's':50, 'color':'green'},
               line_kws={'color':'red', 'linestyle':'--'})

    # 添加对角线（理想预测线）
    min_val = min(min(y_true_np), min(y_pred_np))
    max_val = max(max(y_true_np), max(y_pred_np))
    plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5, label='Perfect Prediction')

    # 固定文本框位置到左上角，稍微往下一点避免与图例重叠
    text_position = (0.02, 0.85)

    # 固定对齐方式为左上角
    va_align = 'top'
    ha_align = 'left'

    # 创建更美观的文本框
    text_content = f'R² = {r2:.4f}\nRMSE = {rmse:.4f}\nMAE = {mae:.4f}\nRPD = {rpd:.4f}'

    # 添加评估指标文本框，使用改进的样式
    plt.annotate(text_content,
                 xy=text_position, xycoords='axes fraction',
                 bbox=dict(boxstyle="round,pad=0.5",
                          facecolor="white",
                          edgecolor="green",
                          alpha=0.95,
                          linewidth=1.5),
                 fontsize=11,
                 ha=ha_align,
                 va=va_align,
                 weight='bold',
                 color='darkgreen')

    plt.title(title, fontsize=16)
    plt.xlabel('True Oil Content (%)', fontsize=14)
    plt.ylabel('Predicted Oil Content (%)', fontsize=14)
    plt.legend(fontsize=12)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")

    plt.close()  # 关闭图形以释放内存


def plot_original_level_regression(original_true, original_pred, component_name='Component',
                                  title='Original-Level Prediction Results', save_path=None):
    """
    绘制Original级别的回归散点图

    参数:
        original_true: Original级别真实值数组
        original_pred: Original级别预测值数组 (每个original的样本预测均值)
        component_name: 组分名称 ('Oil' 或 'Protein')
        title: 图表标题
        save_path: 保存图片的路径，如果为None则显示图片不保存
    """
    # 转换为PyTorch张量以便使用validate中的函数
    if not isinstance(original_true, torch.Tensor):
        original_true = torch.tensor(original_true, dtype=torch.float32)
    if not isinstance(original_pred, torch.Tensor):
        original_pred = torch.tensor(original_pred, dtype=torch.float32)

    # 计算评估指标
    r2 = calculate_r2(original_true, original_pred)
    rmse = calculate_rmse(original_true, original_pred)
    mae = calculate_mae(original_true, original_pred)
    rpd = calculate_rpd(original_true, original_pred)

    # 转回numpy数组用于绘图
    original_true_np = original_true.numpy() if isinstance(original_true, torch.Tensor) else original_true
    original_pred_np = original_pred.numpy() if isinstance(original_pred, torch.Tensor) else original_pred

    # 创建数据框用于回归图
    df = pd.DataFrame({'True Values': original_true_np, 'Predicted Values': original_pred_np})

    # 使用seaborn绘制回归图
    plt.figure(figsize=(10, 8))
    sns.set_style("whitegrid")

    # 根据组分选择颜色
    if 'Oil' in component_name:
        scatter_color = 'green'
        edge_color = 'darkgreen'
        text_color = 'darkgreen'
    else:  # Protein
        scatter_color = 'blue'
        edge_color = 'darkblue'
        text_color = 'darkblue'

    # 绘制散点图和回归线
    sns.regplot(x='True Values', y='Predicted Values', data=df,
               scatter_kws={'alpha':0.7, 's':80, 'color':scatter_color, 'edgecolors':'white', 'linewidth':1},
               line_kws={'color':'red', 'linestyle':'--', 'linewidth':2})

    # 添加对角线（理想预测线）
    min_val = min(min(original_true_np), min(original_pred_np))
    max_val = max(max(original_true_np), max(original_pred_np))
    plt.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5, label='Perfect Prediction', linewidth=2)

    # 固定文本框位置到左上角，稍微往下一点避免与图例重叠
    text_position = (0.02, 0.85)

    # 固定对齐方式为左上角
    va_align = 'top'
    ha_align = 'left'

    # 创建更美观的文本框
    text_content = f'Original-Level Metrics:\nR² = {r2:.4f}\nRMSE = {rmse:.4f}\nMAE = {mae:.4f}\nRPD = {rpd:.4f}\nSamples: {len(original_true_np)} originals'

    # 添加评估指标文本框，使用改进的样式
    plt.annotate(text_content,
                 xy=text_position, xycoords='axes fraction',
                 bbox=dict(boxstyle="round,pad=0.5",
                          facecolor="lightyellow",
                          edgecolor=edge_color,
                          alpha=0.95,
                          linewidth=2),
                 fontsize=11,
                 ha=ha_align,
                 va=va_align,
                 weight='bold',
                 color=text_color)

    plt.title(title, fontsize=16, weight='bold')
    plt.xlabel(f'True {component_name} Content (%)', fontsize=14)
    plt.ylabel(f'Predicted {component_name} Content (% - Original Average)', fontsize=14)
    plt.legend(fontsize=12)

    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")

    plt.close()  # 关闭图形以释放内存


def create_visualization_examples():
    """
    创建可视化示例，展示如何使用各种可视化函数
    """
    # 创建输出目录
    output_dir = os.path.join(os.getcwd(), 'visualization_results')
    os.makedirs(output_dir, exist_ok=True)

    # 模拟训练和验证损失数据
    epochs = 100
    train_losses = [np.exp(-0.05 * i) + 0.1 + 0.05 * np.random.randn() for i in range(epochs)]
    val_losses = [np.exp(-0.04 * i) + 0.15 + 0.07 * np.random.randn() for i in range(epochs)]

    # 绘制训练和验证损失曲线
    plot_train_val_loss(
        train_losses,
        val_losses,
        title='FasterNet Training and Validation Loss',
        save_path=os.path.join(output_dir, 'loss_curve.png')
    )

    # 模拟蛋白质含量数据
    n_samples = 100
    protein_true = 20 + 10 * np.random.rand(n_samples)  # 20-30% 范围的真实蛋白质含量
    protein_pred = protein_true + 1.5 * np.random.randn(n_samples)  # 添加一些预测误差

    # 绘制蛋白质含量回归图
    plot_protein_regression(
        protein_true,
        protein_pred,
        title='FasterNet Protein Content Prediction Results',
        save_path=os.path.join(output_dir, 'protein_regression.png')
    )

    # 模拟含油量数据
    oil_true = 40 + 15 * np.random.rand(n_samples)  # 40-55% 范围的真实含油量
    oil_pred = oil_true + 2 * np.random.randn(n_samples)  # 添加一些预测误差

    # 绘制含油量回归图
    plot_oil_regression(
        oil_true,
        oil_pred,
        title='FasterNet Oil Content Prediction Results',
        save_path=os.path.join(output_dir, 'oil_regression.png')
    )

    print(f"All example charts saved to: {output_dir}")


if __name__ == "__main__":
    # 运行示例
    create_visualization_examples()