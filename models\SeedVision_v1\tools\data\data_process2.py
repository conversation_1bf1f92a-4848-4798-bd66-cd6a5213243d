'''
这里主要是生成针对二级数值推理模型的数据集
主要是使用yolo对图片进行分割，然后将分割后的图片单独保存
对yolotrain1的数据进行再次处理
'''
import os
from ultralytics import YOLO
import pandas as pd
from PIL import Image
import sys
sys.path.append('E:\Proj\pytorch-model-train')

# 基础路径
BASE_DIR = r'E:\Proj\pytorch-model-train\dataset\SeedVisionDataset'
# 原始数据路径
ORIGINAL_DATA_DIR = BASE_DIR + r'\Origin_Data'
# 原始数据标注
# DATA_CSV = ORIGINAL_DATA_DIR + r'\data.csv' # 每个种类的基础标注
# 基础输出路径
BASE_OUTPUT_DIR = BASE_DIR + r'\YOLO_Data'
# 原始数据标注
ORIGINAL_OUTPUT_DIR = BASE_OUTPUT_DIR + r'\yolotrain1\data.csv'
# 本次处理的输出路径
CURRENT_OUTPUT_DIR = BASE_OUTPUT_DIR + r'\yolotrain2'
# 本次处理的数据csv路径
CURRENT_OUTPUT_CSV = CURRENT_OUTPUT_DIR + r'\data.csv'
# 数据集划分的路径
# 这里严格来说应该再有变量表示划分路径的，但是这里用不到就是这样写了
TRAIN_IMAGE_DIR = CURRENT_OUTPUT_DIR + r'\train\images'
VAL_IMAGE_DIR = CURRENT_OUTPUT_DIR + r'\val\images'
TEST_IMAGE_DIR = CURRENT_OUTPUT_DIR + r'\test\images'

# 如果不存在就创建
if not os.path.exists(CURRENT_OUTPUT_DIR):
    os.makedirs(CURRENT_OUTPUT_DIR)
if not os.path.exists(TRAIN_IMAGE_DIR):
    os.makedirs(TRAIN_IMAGE_DIR)
if not os.path.exists(VAL_IMAGE_DIR):
    os.makedirs(VAL_IMAGE_DIR)
if not os.path.exists(TEST_IMAGE_DIR):
    os.makedirs(TEST_IMAGE_DIR)

TRAIN_RATE = 0.8 # 训练集比例
VAL_RATE = 0.1 # 验证集比例
TEST_RATE = 0.1 # 测试集比例

def main():
    # 读取原始数据标注
    df = pd.read_csv(ORIGINAL_OUTPUT_DIR)
    yolo = YOLO('E:\Proj\pytorch-model-train\weights\SeedVision_cls\yolo11s.pt')
    
    # 初始化数据集划分
    train_data = []
    val_data = []
    test_data = []
    
    # 计算划分数量
    total_images = len(df)
    train_count = int(total_images * TRAIN_RATE)
    val_count = int(total_images * VAL_RATE)
    test_count = total_images - train_count - val_count
    
    # 遍历每一行数据
    for index, row in df.iterrows():
        # 读取图片
        image_path = row['path']
        image_name = os.path.basename(image_path)
        image = Image.open(image_path)
        yolo_cropped = yolo.predict(image)
        
        # 确定当前图片的划分类型
        if index < train_count:
            output_subdir = TRAIN_IMAGE_DIR
            dataset_type = 'train'
        elif index < train_count + val_count:
            output_subdir = VAL_IMAGE_DIR
            dataset_type = 'val'
        else:
            output_subdir = TEST_IMAGE_DIR
            dataset_type = 'test'
        
        # 分割图片
        for i, detection in enumerate(yolo_cropped[0].boxes):
            # 裁剪目标区域
            x1, y1, x2, y2 = detection.xyxy[0].tolist()
            cropped_image = image.crop((x1, y1, x2, y2))
            
            # 生成新的图片名称
            suffix = image_name.split('.')[-1]
            new_name = f"{os.path.splitext(image_name)[0]}_{i}.{suffix}"
            save_path = os.path.join(output_subdir, new_name)
            
            # 保存图片
            cropped_image.save(save_path)
            
            # 记录数据
            data_row = {
                'path': save_path,
                'type': dataset_type,
                'original_image': image_path,
                'oil': row['oil'],
                'protein': row['protein']
            }
            
            if dataset_type == 'train':
                train_data.append(data_row)
            elif dataset_type == 'val':
                val_data.append(data_row)
            else:
                test_data.append(data_row)
    
    # 合并数据并保存
    all_data = train_data + val_data + test_data
    result_df = pd.DataFrame(all_data)
    result_df.to_csv(CURRENT_OUTPUT_CSV, index=False, encoding='utf-8')

def main2():
    # 读取原始数据标注
    df = pd.read_csv(ORIGINAL_OUTPUT_DIR)
    yolo = YOLO('E:\Proj\pytorch-model-train\weights\SeedVision_cls\yolo11s.pt')
    
    # 初始化数据集划分
    train_data = []
    val_data = []
    test_data = []
    
    # 计算划分数量
    total_images = len(df)
    train_count = int(total_images * TRAIN_RATE)
    val_count = int(total_images * VAL_RATE)
    
    # 连接数据库
    from utils import mysql_connecter
    db_name = 'SeedVision'
    table_name = 'yolotrain2'
    
    # 创建数据库表（如果不存在）
    with mysql_connecter.cursor() as cursor:
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name}")
        cursor.execute(f"USE {db_name}")
        cursor.execute(f"""CREATE TABLE IF NOT EXISTS {table_name} (
            id INT AUTO_INCREMENT PRIMARY KEY,
            path VARCHAR(255) NOT NULL,
            type VARCHAR(255) NOT NULL,
            original_image VARCHAR(255) NOT NULL,
            oil FLOAT NOT NULL,
            protein FLOAT NOT NULL
        )""")
    
    # 遍历每一行数据
    for index, row in df.iterrows():
        # 读取图片
        image_path = row['path']
        image_name = os.path.basename(image_path)
        image = Image.open(image_path)
        yolo_cropped = yolo.predict(image)
        
        # 确定当前图片的划分类型
        if len(train_data) < train_count:
            output_subdir = TRAIN_IMAGE_DIR
            dataset_type = 'train'
        elif len(val_data) < val_count:
            output_subdir = VAL_IMAGE_DIR
            dataset_type = 'val'
        else:
            output_subdir = TEST_IMAGE_DIR
            dataset_type = 'test'
        
        # 分割图片
        for i, detection in enumerate(yolo_cropped[0].boxes):
            # 检查置信度
            if detection.conf.item() < 0.5:
                continue
                
            # 裁剪目标区域
            x1, y1, x2, y2 = detection.xyxy[0].tolist()
            cropped_image = image.crop((x1, y1, x2, y2))
            
            # 生成新的图片名称
            suffix = image_name.split('.')[-1]
            new_name = f"{os.path.splitext(image_name)[0]}_{i}.{suffix}"
            save_path = os.path.join(output_subdir, new_name)
            
            # 保存图片
            cropped_image.save(save_path)
            
            # 记录数据
            data_row = {
                'path': save_path,
                'type': dataset_type,
                'original_image': image_path,
                'oil': row['oil'],
                'protein': row['protein']
            }
            
            # 插入数据库
            with mysql_connecter.cursor() as cursor:
                cursor.execute(f"USE {db_name}")
                sql = f"""INSERT INTO {table_name} (path, type, original_image, oil, protein) 
                          VALUES (%s, %s, %s, %s, %s)"""
                cursor.execute(sql, (data_row['path'], data_row['type'], 
                                   data_row['original_image'], data_row['oil'], 
                                   data_row['protein']))
                mysql_connecter.commit()
            
            # 添加到对应数据集
            if dataset_type == 'train':
                train_data.append(data_row)
            elif dataset_type == 'val':
                val_data.append(data_row)
            else:
                test_data.append(data_row)
    
    # 合并数据并保存
    all_data = train_data + val_data + test_data
    result_df = pd.DataFrame(all_data)
    result_df.to_csv(CURRENT_OUTPUT_CSV, index=False, encoding='utf-8')

def transofrm_to_mysql():
    pass

def split_dataset():
    '''
    读取csv，根据总量划分数据集，然后将图片移动到对应文件夹
    mysql_connecter = pymysql.connect(
    host='127.0.0.1',  # 数据库主机地址
    user='root',       # 数据库用户名
    password='911711', # 数据库密码
    charset='utf8mb4', # 字符集
    cursorclass=pymysql.cursors.DictCursor # 返回字典格式
)
    '''
    from utils import mysql_connecter
    db_name = 'SeedVision'
    # 使用sql查找数据库是否存在
    sql = f"SHOW DATABASES LIKE '{db_name}'"
    with mysql_connecter.cursor() as cursor:
        cursor.execute(sql)
        result = cursor.fetchone()
        if result is None:
            # 如果数据库不存在，则创建数据库
            sql = f"CREATE DATABASE {db_name}"
            cursor.execute(sql)
            # 再新增一个表存储本次数据
            '''
            字段 全是字符串和浮点
            '''
            table_name = 'yolotrain2'
            sql = f"""CREATE TABLE {table_name} (
                id INT AUTO_INCREMENT PRIMARY KEY,
                path VARCHAR(255) NOT NULL,
                type VARCHAR(255) NOT NULL,
                original_image VARCHAR(255) NOT NULL,
                oil FLOAT NOT NULL,
                protein FLOAT NOT NULL,
            )
            """

    df = pd.read_csv(CURRENT_OUTPUT_CSV)
    # 计算划分数量
    total_images = len(df)
    # train_count = int(total_images * TRAIN_RATE) 目前全部被标记成训练集了
    val_count = int(total_images * VAL_RATE)
    test_count = int(total_images * TEST_RATE)
    process_data_num = 0
    # 多线程遍历每一行数据，将图片移动到对应文件夹
    for index, row in df.iterrows():
        # 读取图片
        image_path = row['path']
        image_name = os.path.basename(image_path)
        # 确定当前图片的划分类型
        if index < val_count: # 这里应该是val
            output_subdir = VAL_IMAGE_DIR
            dataset_type = 'val'
        elif index < val_count + test_count: # 这里应该是test
            output_subdir = TEST_IMAGE_DIR
            dataset_type = 'test'
        else: # 这里应该是train
            # 本来就在train里，不用动
            pass
        # 移动图片
        save_path = os.path.join(output_subdir, image_name)
        os.rename(image_path, save_path)
        # 更新数据
        row['path'] = save_path
        row['type'] = dataset_type
        # 插入数据库
        
        with mysql_connecter.cursor() as cursor:
            # 选择数据库
            sql = f"USE {db_name}"
            cursor.execute(sql)
            sql = f"""INSERT INTO {table_name} (path, type, original_image, oil, protein) VALUES (%s, %s, %s, %s, %s)"""
            cursor.execute(sql, (row['path'], row['type'], row['original_image'], row['oil'], row['protein']))
            mysql_connecter.commit()
        process_data_num += 1
        print(f"Processed {process_data_num}/{total_images} images")
if __name__ == "__main__":
    main2()