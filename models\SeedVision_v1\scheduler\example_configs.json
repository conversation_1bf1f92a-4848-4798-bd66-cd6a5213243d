{"small_test": {"name": "small_test", "description": "小规模测试配置", "model": {"embed_dim": 64, "depths": [2, 2, 8, 2], "mlp_ratio": 2.0, "n_div": 2, "drop_path_rate": 0.1}, "resources": {"batch_size": 20}, "transform_config": "56x56_norm", "training": {"num_epochs": 20}, "dataset_config": {"strategy_parameters": {"total_samples": 400}}}, "standard": {"name": "standard", "description": "标准配置", "model": {"embed_dim": 192, "depths": [3, 4, 18, 3], "mlp_ratio": 2.0, "n_div": 4, "drop_path_rate": 0.3}, "resources": {"batch_size": 40}, "transform_config": "224x224_norm", "training": {"num_epochs": 100}, "dataset_config": {"strategy_parameters": {"total_samples": 2400}}}, "large_scale": {"name": "large_scale", "description": "大规模配置", "model": {"embed_dim": 256, "depths": [4, 6, 20, 4], "mlp_ratio": 2.0, "n_div": 4, "drop_path_rate": 0.4}, "resources": {"batch_size": 60}, "transform_config": "224x224_norm", "training": {"num_epochs": 150}, "dataset_config": {"strategy_parameters": {"total_samples": 4800}}}}